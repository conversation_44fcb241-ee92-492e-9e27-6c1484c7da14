{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(find:*)", "Bash(find:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(gradle test:*)", "Bash(grep:*)", "Bash(for:*)", "Bash(do)", "Bash(if [ -f \"/Users/<USER>/IdeaProjects/ParabellaCSRDFaqTool/documentation/$file\" ])", "<PERSON><PERSON>(then)", "Bash(echo \"✓ $file exists\")", "<PERSON><PERSON>(else)", "<PERSON><PERSON>(echo:*)", "Bash(fi)", "Bash(done)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(gradle:*)", "<PERSON><PERSON>(./gradlew:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(pkill:*)", "Bash(psql:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git add:*)", "Bash(git commit:*)"], "deny": []}}