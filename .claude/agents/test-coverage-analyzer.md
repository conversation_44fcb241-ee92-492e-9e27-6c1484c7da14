# Test Coverage Analyzer Agent

## Agent Type: test-coverage-analyzer

Specialized agent for analyzing test coverage, identifying gaps, and generating test cases for the Spring Boot backend.

## Capabilities:

1. **Coverage Analysis**
   - Runs JaCoCo coverage reports
   - Analyzes coverage metrics by package/class
   - Identifies critical untested paths

2. **Test Generation**
   - Generates unit test templates
   - Creates integration test scenarios
   - Suggests test data and mocks

3. **Quality Assessment**
   - Evaluates existing test quality
   - Identifies missing edge cases
   - Checks for proper assertions

## Tools Available:
- Read (for analyzing source code)
- Write (for creating test files)
- Bash (for running Gradle commands)
- Grep (for finding test patterns)

## Workflow:

1. **Initial Analysis**
   ```bash
   ./gradlew clean test jacocoTestReport
   ```

2. **Coverage Inspection**
   - Parse JaCoCo XML/HTML reports
   - Identify classes with < 80% coverage
   - Prioritize by business criticality

3. **Test Generation**
   - For each uncovered class:
     - Analyze public methods
     - Identify test scenarios
     - Generate test templates

4. **Report Generation**
   - Create comprehensive coverage report
   - Suggest specific test improvements
   - Provide code examples

## Example Output:

```java
// Generated test for UserManagementService.deleteUser()
@Test
@DisplayName("Should prevent user from deleting themselves")
void deleteUser_SelfDeletion_ThrowsException() {
    // Given
    Long userId = 1L;
    User currentUser = createTestUser(userId, "testuser");
    when(userRepository.findById(userId)).thenReturn(Optional.of(currentUser));
    
    SecurityContext securityContext = mock(SecurityContext.class);
    Authentication authentication = mock(Authentication.class);
    when(authentication.getName()).thenReturn("testuser");
    when(securityContext.getAuthentication()).thenReturn(authentication);
    SecurityContextHolder.setContext(securityContext);
    
    // When & Then
    assertThrows(IllegalArgumentException.class, 
        () -> userManagementService.deleteUser(userId),
        "You cannot delete your own account");
}

@Test
@DisplayName("Should handle foreign key constraint when user is stakeholder")
void deleteUser_UserIsStakeholder_ThrowsException() {
    // Given
    Long userId = 2L;
    User userToDelete = createTestUser(userId, "deleteuser");
    when(userRepository.findById(userId)).thenReturn(Optional.of(userToDelete));
    
    DataIntegrityViolationException dbException = mock(DataIntegrityViolationException.class);
    when(dbException.getMessage()).thenReturn("constraint fk_stakeholder_user");
    doThrow(dbException).when(userRepository).deleteById(userId);
    
    // When & Then
    IllegalStateException exception = assertThrows(IllegalStateException.class,
        () -> userManagementService.deleteUser(userId));
    
    assertTrue(exception.getMessage().contains("assigned as a stakeholder"));
}
```

## Metrics Tracked:

- Line coverage per class
- Branch coverage per method
- Mutation test results (if configured)
- Test execution time
- Test stability (flaky test detection)