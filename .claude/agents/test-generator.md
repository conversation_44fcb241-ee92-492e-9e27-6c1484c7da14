# Test Generator Agent

## Agent Type: test-generator

Automated test generation agent specifically for Spring Boot applications with focus on service and controller layers.

## Capabilities:

1. **Unit Test Generation**
   - Service layer tests with Mockito
   - Repository tests with @DataJpaTest
   - Utility class tests

2. **Integration Test Generation**
   - Controller tests with MockMvc
   - Security test scenarios
   - Database integration tests

3. **Test Data Generation**
   - Builder patterns for entities
   - Fixture factories
   - Test data repositories

## Test Templates:

### Service Layer Test Template:
```java
@ExtendWith(MockitoExtension.class)
class [ServiceName]Test {
    
    @Mock
    private [Repository] repository;
    
    @Mock
    private [Mapper] mapper;
    
    @InjectMocks
    private [Service] service;
    
    @BeforeEach
    void setUp() {
        // Common setup
    }
    
    @Test
    @DisplayName("[Method] should [expected behavior] when [condition]")
    void testMethodName() {
        // Given
        
        // When
        
        // Then
    }
}
```

### Controller Test Template:
```java
@WebMvcTest([Controller].class)
@AutoConfigureMockMvc(addFilters = false)
class [ControllerName]Test {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private [Service] service;
    
    @MockBean
    private JwtUtils jwtUtils;
    
    @Test
    @WithMockUser(authorities = {"permission.name"})
    void testEndpoint() throws Exception {
        // Test implementation
    }
}
```

## Workflow:

1. **Analyze Target Class**
   - Identify dependencies
   - Map public methods
   - Determine test scenarios

2. **Generate Test Structure**
   - Create test class
   - Add necessary annotations
   - Setup mocks and dependencies

3. **Create Test Methods**
   - Happy path tests
   - Error scenarios
   - Edge cases
   - Security tests

4. **Add Assertions**
   - Verify return values
   - Check exception messages
   - Validate mock interactions

## Special Features:

### 1. **Parameterized Test Generation**
```java
@ParameterizedTest
@ValueSource(strings = {"", " ", "invalid-email"})
@DisplayName("Should reject invalid email formats")
void testInvalidEmails(String email) {
    // Test implementation
}
```

### 2. **Test Data Builders**
```java
public class UserTestDataBuilder {
    private Long id = 1L;
    private String username = "testuser";
    private String email = "<EMAIL>";
    private Role role = new Role("User");
    
    public static UserTestDataBuilder aUser() {
        return new UserTestDataBuilder();
    }
    
    public UserTestDataBuilder withId(Long id) {
        this.id = id;
        return this;
    }
    
    public User build() {
        User user = new User();
        user.setId(id);
        user.setUsername(username);
        user.setEmail(email);
        user.setRole(role);
        return user;
    }
}
```

### 3. **Security Test Utilities**
```java
public class SecurityTestUtils {
    public static void mockAuthenticatedUser(String username) {
        SecurityContext context = mock(SecurityContext.class);
        Authentication auth = mock(Authentication.class);
        when(auth.getName()).thenReturn(username);
        when(context.getAuthentication()).thenReturn(auth);
        SecurityContextHolder.setContext(context);
    }
}
```

## Coverage Goals:

- Minimum 80% line coverage
- All public methods tested
- All exception paths covered
- Security scenarios validated
- Database constraints tested