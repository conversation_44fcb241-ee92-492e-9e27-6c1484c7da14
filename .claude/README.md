# Claude Commands and Agents

This directory contains custom commands and specialized agents for the Parabella CSRD FAQ Tool project.

## Available Commands

### `/test-coverage`
Analyzes test coverage for the backend Spring Boot application.

**Usage:**
```bash
/test-coverage [options]
```

**Options:**
- `--full` - Generate and open full HTML coverage report
- `--service` - Focus analysis on service layer
- `--controller` - Focus analysis on controller layer  
- `--threshold <percentage>` - Set minimum coverage threshold (default: 80%)

**Examples:**
```bash
/test-coverage                          # Basic coverage report
/test-coverage --full                   # With HTML report
/test-coverage --service --threshold 90 # Service layer with 90% threshold
```

## Available Agents

### `test-coverage-analyzer`
Specialized agent for deep test coverage analysis and recommendations.

**Capabilities:**
- Runs JaCoCo coverage analysis
- Identifies critical untested code paths
- Suggests specific test scenarios
- Generates coverage improvement plans

**Usage:**
```
@agent test-coverage-analyzer
```

### `test-generator`
Automated test generation for Spring Boot components.

**Capabilities:**
- Generates unit tests for services
- Creates integration tests for controllers
- Provides test data builders
- Includes security test scenarios

**Usage:**
```
@agent test-generator --class UserManagementService
@agent test-generator --controller AuthController
```

## Directory Structure

```
.claude/
├── README.md                    # This file
├── test-coverage.md            # Test coverage command documentation
├── agents/
│   ├── test-coverage-analyzer.md   # Coverage analysis agent
│   └── test-generator.md           # Test generation agent
└── scripts/
    └── test-coverage.sh            # Shell script for coverage analysis
```

## Setup

1. **JaCoCo Configuration**: Already added to `build.gradle`
2. **Coverage Thresholds**: Set to 80% by default
3. **Exclusions**: DTOs, entities, and config classes are excluded

## Workflow

1. Run coverage analysis:
   ```
   /test-coverage
   ```

2. If coverage is low, use the analyzer:
   ```
   @agent test-coverage-analyzer
   ```

3. Generate missing tests:
   ```
   @agent test-generator --class [ClassName]
   ```

4. Verify improvements:
   ```
   /test-coverage --full
   ```

## Coverage Goals

- **Overall**: 80% minimum
- **Service Layer**: 90% recommended
- **Controller Layer**: 85% recommended
- **Critical Business Logic**: 95% recommended

## Integration with CI/CD

Add to your pipeline:
```yaml
- name: Check test coverage
  run: ./gradlew test jacocoTestCoverageVerification
```

## Tips

1. Focus on business logic coverage first
2. Don't test framework code or simple getters/setters
3. Prioritize edge cases and error scenarios
4. Use parameterized tests for multiple scenarios
5. Mock external dependencies properly