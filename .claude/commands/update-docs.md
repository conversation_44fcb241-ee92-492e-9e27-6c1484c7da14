---
name: update-docs
description: Incremental documentation update
---

Perform an incremental documentation update:

1. **Detect changes** since last documentation update
2. **Spawn specialized sub-agents** for different areas:
   - API changes → api-documenter agent
   - Architecture changes → architecture-reviewer agent
   - New tests → test-documenter agent
   
3. **Coordinate updates** across documentation tiers
4. **Validate** documentation consistency
5. **Create PR** with documentation changes

Use git diff to identify changed files and focus updates accordingly.