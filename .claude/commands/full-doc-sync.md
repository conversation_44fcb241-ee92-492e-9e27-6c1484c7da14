---
name: full-doc-sync
description: Complete documentation synchronization
---

Perform a complete documentation synchronization:

THINK HARD about the documentation structure and dependencies.

1. **Create documentation plan** using sub-agents:
   - Spawn 4 parallel agents to analyze different aspects
   - Each agent creates a documentation update plan
   - Merge plans into cohesive strategy

2. **Execute updates** in dependency order:
   - Foundation docs first (Tier 1)
   - Component docs (Tier 2)
   - Feature docs (Tier 3)

3. **Cross-reference** all documentation
4. **Generate** documentation index and navigation

This is a comprehensive operation - ultrathink about optimization.