# Test Coverage Command

## Command: /test-coverage

Analyzes and reports test coverage for the backend Java/Spring Boot application.

## Usage

```
/test-coverage [options]
```

### Options:
- `--full` - Generate full HTML coverage report
- `--service` - Focus on service layer coverage
- `--controller` - Focus on controller layer coverage
- `--threshold <percentage>` - Set minimum coverage threshold (default: 80%)

### Examples:
```
/test-coverage
/test-coverage --full
/test-coverage --service --threshold 90
```

## What it does:

1. **Runs all backend tests** with coverage instrumentation
2. **Generates coverage report** showing:
   - Line coverage percentage
   - Branch coverage percentage
   - Class coverage percentage
   - Method coverage percentage
3. **Identifies untested code** and suggests test cases
4. **Checks coverage thresholds** and fails if below minimum
5. **Provides improvement recommendations**

## Output Format:

```
=== Backend Test Coverage Report ===

Overall Coverage: 75.3%
- Line Coverage: 78.2%
- Branch Coverage: 65.4%
- Method Coverage: 82.1%
- Class Coverage: 90.5%

Critical Uncovered Areas:
1. UserManagementService.deleteUser() - 45% coverage
   Missing: Foreign key error handling tests
   
2. AuthController.authenticateUser() - 60% coverage
   Missing: 2FA edge case tests

3. CsrdDataService.processData() - 0% coverage
   Missing: All test cases

Recommendations:
- Add integration tests for UserManagementService
- Implement error scenario tests for authentication
- Create unit tests for data processing logic

Coverage Threshold: FAILED (Required: 80%, Actual: 75.3%)
```

## Implementation:

The command uses:
- JaCoCo for Java code coverage
- Gradle test tasks with coverage enabled
- Custom analysis for identifying critical gaps
- Integration with Spring Boot Test framework