# .claude/commands/doc-audit.md
---
name: doc-audit
description: Comprehensive documentation audit
---

Please perform a comprehensive documentation audit:

1. **Scan all project files** to understand the current codebase structure.
2. **Identify documentation gaps** by comparing code to existing docs found in documentation/ .
3. **Create a priority list** of documentation needs.
4. **Check for all files in documentation/** and find critical missing or areas. If something is wrong, please make it correct. Always take the corresponding code as source of truth.

Use 4 sub-agents for parallel analysis:
- Code Analyzer: Map all functions, classes, and modules
- API Documenter: Extract all endpoints and contracts
- Architecture Reviewer: Identify patterns and dependencies
- Test Coverage Analyst: Document test strategies

This is a comprehensive operation - ultrathink about optimization.
Output a comprehensive report with actionable next steps.
