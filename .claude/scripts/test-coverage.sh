#!/bin/bash
# Test Coverage Script for Backend

set -e

echo "=== Backend Test Coverage Analysis ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
THRESHOLD=80
REPORT_TYPE="console"
FOCUS=""

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --full)
            REPORT_TYPE="full"
            shift
            ;;
        --service)
            FOCUS="service"
            shift
            ;;
        --controller)
            FOCUS="controller"
            shift
            ;;
        --threshold)
            THRESHOLD="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Clean and run tests with coverage
echo "Running tests with coverage analysis..."
./gradlew clean test jacocoTestReport

# Check if tests passed
if [ $? -ne 0 ]; then
    echo -e "${RED}Tests failed! Fix failing tests before analyzing coverage.${NC}"
    exit 1
fi

# Parse coverage results
COVERAGE_FILE="build/reports/jacoco/test/jacocoTestReport.csv"
if [ ! -f "$COVERAGE_FILE" ]; then
    echo -e "${RED}Coverage report not found. Make sure JaCoCo is configured correctly.${NC}"
    exit 1
fi

# Calculate coverage percentages
echo
echo "Calculating coverage metrics..."

# Extract coverage data (simplified - in real implementation would parse CSV properly)
TOTAL_LINES=$(tail -n +2 "$COVERAGE_FILE" | awk -F',' '{sum += $4 + $5} END {print sum}')
COVERED_LINES=$(tail -n +2 "$COVERAGE_FILE" | awk -F',' '{sum += $5} END {print sum}')
LINE_COVERAGE=$(awk "BEGIN {printf \"%.1f\", ($COVERED_LINES / $TOTAL_LINES) * 100}")

echo
echo "=== Coverage Summary ==="
echo "Line Coverage: ${LINE_COVERAGE}%"
echo "Threshold: ${THRESHOLD}%"

# Find uncovered critical classes
echo
echo "=== Critical Uncovered Areas ==="

# Focus on service layer if requested
if [ "$FOCUS" = "service" ]; then
    PATTERN="*Service.java"
elif [ "$FOCUS" = "controller" ]; then
    PATTERN="*Controller.java"
else
    PATTERN="*.java"
fi

# Find classes with low coverage
find src/main/java -name "$PATTERN" -type f | while read -r file; do
    CLASS_NAME=$(basename "$file" .java)
    # Check if this class has low coverage in the report
    grep -q "$CLASS_NAME" "$COVERAGE_FILE" || continue
    
    # Extract coverage for this class (simplified)
    CLASS_COVERAGE=$(grep "$CLASS_NAME" "$COVERAGE_FILE" | head -1 | awk -F',' '{
        if ($4 + $5 > 0) {
            coverage = ($5 / ($4 + $5)) * 100
            printf "%.0f", coverage
        } else {
            print "N/A"
        }
    }')
    
    if [ "$CLASS_COVERAGE" != "N/A" ] && [ "$CLASS_COVERAGE" -lt "$THRESHOLD" ]; then
        echo -e "${YELLOW}$CLASS_NAME: $CLASS_COVERAGE% coverage${NC}"
        
        # Suggest what might be missing
        if [[ "$CLASS_NAME" == *"Service" ]]; then
            echo "  Missing: Exception handling tests, edge cases"
        elif [[ "$CLASS_NAME" == *"Controller" ]]; then
            echo "  Missing: Security tests, error response tests"
        fi
    fi
done

# Check if coverage meets threshold
if (( $(echo "$LINE_COVERAGE < $THRESHOLD" | bc -l) )); then
    echo
    echo -e "${RED}COVERAGE FAILED: $LINE_COVERAGE% is below threshold of $THRESHOLD%${NC}"
    
    # Provide recommendations
    echo
    echo "=== Recommendations ==="
    echo "1. Run individual test coverage for problem classes:"
    echo "   ./gradlew test --tests 'UserManagementServiceTest'"
    echo
    echo "2. Use test generator agent to create missing tests:"
    echo "   @agent test-generator --class UserManagementService"
    echo
    echo "3. Focus on untested methods with high complexity"
    
    # Generate HTML report if requested
    if [ "$REPORT_TYPE" = "full" ]; then
        echo
        echo "Full HTML report available at: build/reports/jacoco/test/html/index.html"
        echo "Opening in browser..."
        open build/reports/jacoco/test/html/index.html 2>/dev/null || xdg-open build/reports/jacoco/test/html/index.html 2>/dev/null || echo "Please open manually"
    fi
    
    exit 1
else
    echo
    echo -e "${GREEN}COVERAGE PASSED: $LINE_COVERAGE% meets threshold of $THRESHOLD%${NC}"
fi

echo
echo "=== Next Steps ==="
echo "1. Review the full report: build/reports/jacoco/test/html/index.html"
echo "2. Run mutation testing for deeper quality analysis"
echo "3. Set up coverage gates in CI/CD pipeline"