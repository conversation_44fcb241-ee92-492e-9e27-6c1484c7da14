// main.tsx (Refactored)

import { Fragment } from 'react';
import ReactDOM from 'react-dom/client';
import './index.scss';
import './services/clearAuthData'; // Import to make clearAllAuthData available globally
import { He<PERSON>etProvider } from 'react-helmet-async';
import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom';
import Scrolltotop from './Scrolltotop';
import { Routingdata } from './ui_components/templateLogic/routingdata';

// --- All your component imports remain the same ---
import Forgotpassword from './ui_components/components/authentication/Forgotpassword.tsx';
import Lockscreen from './ui_components/components/authentication/Lockscreen.tsx';
import Resetpassword from './ui_components/components/authentication/Resetpassword.tsx';
import Signup from './ui_components/components/authentication/Signup.tsx';
import Login from "./ui_components/components/authentication/Login.tsx";
import Register from "./ui_components/components/authentication/Register.tsx";
import Error500 from "./ui_components/components/authentication/500error.tsx";
import Error501 from "./ui_components/components/authentication/501error.tsx";
import Underconstruction from "./ui_components/components/authentication/Underconstruction.tsx";
import StakeholderRoute from "./ui_components/components/DmaModule/stakeholder_navigation_module/navigation_modules/01_StakeholderRoute.tsx";
import StakeholderImpactFinancialAnalysis from "./ui_components/components/DmaModule/stakeholder_navigation_module/navigation_modules/02_StakeholderImpactFinancialAnalysis.tsx";
import ProtectedRoute from "./ui_components/components/authentication/ProtectedRoute.tsx";
import { Dashboard } from "@mui/icons-material"; // Assuming this is your dashboard component
import { AuthProvider } from "./services/AuthContext.tsx";
import Forbidden from "./ui_components/components/general_pages/forbidden.tsx";

// +++ IMPORT THE NEW LAYOUT COMPONENT +++
import ProtectedLayout from './ProtectedLayout';
import Setpassword from "./ui_components/components/authentication/Setpassword.tsx";


ReactDOM.createRoot(document.getElementById('root')!).render(
    <Fragment>
        <HelmetProvider>
            <BrowserRouter>
                <AuthProvider>
                    <Scrolltotop />
                    <Routes>
                        {/* ============================================= */}
                        {/*       UNPROTECTED ROUTES         */}
                        {/* ============================================= */}
                        <Route path={`${import.meta.env.BASE_URL}`} element={<Navigate to={`${import.meta.env.BASE_URL}authentication/login`} replace />} />
                        <Route path={`${import.meta.env.BASE_URL}authentication/login`} element={<Login />} />
                        <Route path={`${import.meta.env.BASE_URL}authentication/register`} element={<Register />} />
                        <Route path={`${import.meta.env.BASE_URL}pages/authentication/500error`} element={<Error500 />} />
                        <Route path={`${import.meta.env.BASE_URL}pages/authentication/501error`} element={<Error501 />} />
                        <Route path={`${import.meta.env.BASE_URL}pages/authentication/404error`} element={<Login />} />
                        <Route path={`${import.meta.env.BASE_URL}pages/authentication/forgotpassword`} element={<Forgotpassword />} />
                        <Route path={`${import.meta.env.BASE_URL}pages/authentication/lockscreen`} element={<Lockscreen />} />
                        <Route path={`${import.meta.env.BASE_URL}pages/authentication/resetpassword/:token`} element={<Resetpassword />} />
                        <Route path="pages/authentication/setpassword/:token" element={<Setpassword />} />

                        <Route path={`${import.meta.env.BASE_URL}pages/authentication/underconstruction/`} element={<Underconstruction />} />
                        <Route path={`${import.meta.env.BASE_URL}stakeholder/main/:token`} element={<StakeholderRoute />} />
                        <Route path={`${import.meta.env.BASE_URL}stakeholder/impact-financial-analysis/:token`} element={<StakeholderImpactFinancialAnalysis />} />
                        <Route path={`${import.meta.env.BASE_URL}forbidden`} element={<Forbidden />} />

                        {/* ============================================= */}
                        {/*   PROTECTED ROUTES (Refactored Structure)     */}
                        {/* ============================================= */}
                        <Route element={
                            <ProtectedRoute>
                                <ProtectedLayout />
                            </ProtectedRoute>
                        }>
                            {/* This parent route does one thing: checks for AUTHENTICATION.
                                If the user is logged in, it renders the ProtectedLayout,
                                which contains your Header, Sidebar, and the <Outlet /> for the child routes.
                                All routes nested below will render inside that <Outlet />.
                            */}

                            {/* A route that only needs authentication, no specific permissions. */}
                            <Route path={`${import.meta.env.BASE_URL}dashboard/dashboard`} element={<Dashboard />} />

                            {/* Map through routes that may require specific PERMISSIONS. */}
                            {Routingdata.map((route, index) => (
                                <Route
                                    key={index}
                                    path={route.path}
                                    element={
                                        // This inner ProtectedRoute now only handles AUTHORIZATION (permissions).
                                        // The outer one has already handled authentication. This is not redundant.
                                        <ProtectedRoute permissions={route.permissions}>
                                            {route.element}
                                        </ProtectedRoute>
                                    }
                                />
                            ))}
                        </Route>

                        {/* Redirect all unknown paths to 404 */}
                        <Route path="*" element={<Navigate to={`${import.meta.env.BASE_URL}pages/authentication/404error`} />} />
                    </Routes>
                </AuthProvider>
            </BrowserRouter>
        </HelmetProvider>
    </Fragment>
);