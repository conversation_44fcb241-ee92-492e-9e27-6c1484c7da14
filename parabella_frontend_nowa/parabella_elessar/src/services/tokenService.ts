/**
 * Secure Token Service
 * Provides secure storage and management of authentication tokens
 * with XSS protection, encryption, and defense-in-depth security measures.
 * 
 * Security Features:
 * - Access tokens stored in sessionStorage (cleared on tab close)
 * - Refresh tokens encrypted in localStorage with AES-GCM
 * - Token integrity validation with HMAC
 * - Memory-based storage option for maximum security
 * - Automatic token cleanup and rotation
 * - Session binding to prevent token theft
 */

import { User } from './authService';

// Storage keys with versioning for migration support
const ACCESS_TOKEN_KEY = 'parabella_access_token_v2';
const REFRESH_TOKEN_KEY = 'parabella_refresh_token_v2';
const USER_DATA_KEY = 'parabella_user_data_v2';
const TOKEN_EXPIRY_KEY = 'parabella_token_expiry_v2';
const ENCRYPTION_KEY = 'parabella_encryption_key';
const SESSION_FINGERPRINT_KEY = 'parabella_session_fp';

// Legacy keys for migration
const LEGACY_ACCESS_TOKEN_KEY = 'parabella_access_token';
const LEGACY_REFRESH_TOKEN_KEY = 'parabella_refresh_token';
const LEGACY_USER_DATA_KEY = 'parabella_user_data';
const LEGACY_TOKEN_EXPIRY_KEY = 'parabella_token_expiry';

// Configuration flags
const USE_MEMORY_STORAGE = false; // Set to true for maximum security (tokens lost on refresh)
const USE_ENCRYPTION = true;
const SESSION_BINDING_ENABLED = true;

/**
 * Crypto utilities for token encryption and integrity
 */
class CryptoUtils {
    private static async generateKey(): Promise<CryptoKey> {
        return window.crypto.subtle.generateKey(
            { name: 'AES-GCM', length: 256 },
            true,
            ['encrypt', 'decrypt']
        );
    }

    private static async getOrCreateEncryptionKey(): Promise<CryptoKey> {
        try {
            const storedKey = localStorage.getItem(ENCRYPTION_KEY);
            if (storedKey) {
                const keyData = JSON.parse(storedKey);
                return window.crypto.subtle.importKey(
                    'jwk',
                    keyData,
                    { name: 'AES-GCM' },
                    true,
                    ['encrypt', 'decrypt']
                );
            }
        } catch (error) {
            console.warn('Failed to load existing encryption key, generating new one');
        }

        // Generate new key
        const key = await this.generateKey();
        const keyData = await window.crypto.subtle.exportKey('jwk', key);
        localStorage.setItem(ENCRYPTION_KEY, JSON.stringify(keyData));
        return key;
    }

    static async encrypt(text: string): Promise<string> {
        if (!USE_ENCRYPTION) return text;
        
        try {
            const key = await this.getOrCreateEncryptionKey();
            const iv = window.crypto.getRandomValues(new Uint8Array(12));
            const encoded = new TextEncoder().encode(text);
            
            const encrypted = await window.crypto.subtle.encrypt(
                { name: 'AES-GCM', iv },
                key,
                encoded
            );
            
            const combined = new Uint8Array(iv.length + encrypted.byteLength);
            combined.set(iv);
            combined.set(new Uint8Array(encrypted), iv.length);
            
            return btoa(String.fromCharCode(...combined));
        } catch (error) {
            console.error('Encryption failed:', error);
            return text; // Fallback to unencrypted
        }
    }

    static async decrypt(encryptedText: string): Promise<string> {
        if (!USE_ENCRYPTION) return encryptedText;
        
        try {
            const key = await this.getOrCreateEncryptionKey();
            const combined = new Uint8Array(
                Array.from(atob(encryptedText), c => c.charCodeAt(0))
            );
            
            const iv = combined.slice(0, 12);
            const encrypted = combined.slice(12);
            
            const decrypted = await window.crypto.subtle.decrypt(
                { name: 'AES-GCM', iv },
                key,
                encrypted
            );
            
            return new TextDecoder().decode(decrypted);
        } catch (error) {
            console.error('Decryption failed:', error);
            return encryptedText; // Return as-is if decryption fails
        }
    }

    static generateSessionFingerprint(): string {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Security fingerprint', 2, 2);
        }
        
        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL()
        ].join('|');
        
        return btoa(fingerprint).slice(0, 32);
    }
}

/**
 * In-memory storage for maximum security (tokens cleared on page refresh)
 */
class MemoryStorage {
    private static storage = new Map<string, string>();
    
    static setItem(key: string, value: string): void {
        this.storage.set(key, value);
    }
    
    static getItem(key: string): string | null {
        return this.storage.get(key) || null;
    }
    
    static removeItem(key: string): void {
        this.storage.delete(key);
    }
    
    static clear(): void {
        this.storage.clear();
    }
}

/**
 * Secure Token Service with defense-in-depth security
 */
export const tokenService = {
    /**
     * Securely stores tokens with encryption and session binding
     */
    setTokens: async (accessToken: string, refreshToken: string): Promise<void> => {
        try {
            // Migrate from legacy storage if it exists
            await tokenService.migrateLegacyTokens();
            
            // Generate or validate session fingerprint
            if (SESSION_BINDING_ENABLED) {
                const fingerprint = CryptoUtils.generateSessionFingerprint();
                sessionStorage.setItem(SESSION_FINGERPRINT_KEY, fingerprint);
            }
            
            // Store access token in sessionStorage (cleared on tab close) or memory
            if (USE_MEMORY_STORAGE) {
                MemoryStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
            } else {
                sessionStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
            }
            
            // Encrypt and store refresh token in localStorage
            const encryptedRefreshToken = await CryptoUtils.encrypt(refreshToken);
            localStorage.setItem(REFRESH_TOKEN_KEY, encryptedRefreshToken);
            
            // Set token expiry tracking
            const accessTokenExpiry = Date.now() + (15 * 60 * 1000); // 15 minutes
            if (USE_MEMORY_STORAGE) {
                MemoryStorage.setItem(TOKEN_EXPIRY_KEY, accessTokenExpiry.toString());
            } else {
                sessionStorage.setItem(TOKEN_EXPIRY_KEY, accessTokenExpiry.toString());
            }
            
            console.log('Tokens stored securely with encryption');
        } catch (error) {
            console.error('Failed to store tokens securely:', error);
            // Fallback to basic storage if encryption fails
            try {
                sessionStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
                localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
            } catch (fallbackError) {
                console.error('Fallback token storage also failed:', fallbackError);
                throw new Error('Critical: Unable to store authentication tokens');
            }
        }
    },

    /**
     * Securely retrieves the access token with session validation
     */
    getAccessToken: (): string | null => {
        try {
            // Validate session fingerprint if enabled
            if (SESSION_BINDING_ENABLED && !tokenService.validateSessionFingerprint()) {
                console.warn('Session fingerprint validation failed, clearing tokens');
                tokenService.clearAll();
                return null;
            }
            
            let token: string | null;
            if (USE_MEMORY_STORAGE) {
                token = MemoryStorage.getItem(ACCESS_TOKEN_KEY);
            } else {
                token = sessionStorage.getItem(ACCESS_TOKEN_KEY);
            }
            
            return token || null;
        } catch (error) {
            console.error('Failed to retrieve access token:', error);
            return null;
        }
    },

    /**
     * Securely retrieves and decrypts the refresh token
     */
    getRefreshToken: async (): Promise<string | null> => {
        try {
            // Validate session fingerprint if enabled
            if (SESSION_BINDING_ENABLED && !tokenService.validateSessionFingerprint()) {
                console.warn('Session fingerprint validation failed for refresh token');
                tokenService.clearAll();
                return null;
            }
            
            const encryptedToken = localStorage.getItem(REFRESH_TOKEN_KEY);
            if (!encryptedToken) return null;
            
            const decryptedToken = await CryptoUtils.decrypt(encryptedToken);
            return decryptedToken || null;
        } catch (error) {
            console.error('Failed to retrieve refresh token:', error);
            return null;
        }
    },

    /**
     * Securely stores user data with encryption
     */
    setUser: async (user: User): Promise<void> => {
        try {
            const userData = JSON.stringify(user);
            const encryptedUserData = await CryptoUtils.encrypt(userData);
            
            if (USE_MEMORY_STORAGE) {
                MemoryStorage.setItem(USER_DATA_KEY, encryptedUserData);
            } else {
                sessionStorage.setItem(USER_DATA_KEY, encryptedUserData);
            }
        } catch (error) {
            console.error('Failed to store user data securely:', error);
            // Fallback to unencrypted storage
            try {
                sessionStorage.setItem(USER_DATA_KEY, JSON.stringify(user));
            } catch (fallbackError) {
                console.error('Fallback user data storage failed:', fallbackError);
            }
        }
    },

    /**
     * Securely retrieves and decrypts user data
     */
    getUser: async (): Promise<User | null> => {
        try {
            let encryptedUserData: string | null;
            
            if (USE_MEMORY_STORAGE) {
                encryptedUserData = MemoryStorage.getItem(USER_DATA_KEY);
            } else {
                encryptedUserData = sessionStorage.getItem(USER_DATA_KEY);
            }
            
            if (!encryptedUserData) return null;
            
            const decryptedUserData = await CryptoUtils.decrypt(encryptedUserData);
            return decryptedUserData ? JSON.parse(decryptedUserData) : null;
        } catch (error) {
            console.error('Failed to retrieve user data:', error);
            return null;
        }
    },

    /**
     * Checks if the access token is about to expire (within 2 minutes)
     */
    isAccessTokenExpiring: (): boolean => {
        try {
            let expiryStr: string | null;
            
            if (USE_MEMORY_STORAGE) {
                expiryStr = MemoryStorage.getItem(TOKEN_EXPIRY_KEY);
            } else {
                expiryStr = sessionStorage.getItem(TOKEN_EXPIRY_KEY);
            }
            
            if (!expiryStr) return true;
            
            const expiry = parseInt(expiryStr);
            const now = Date.now();
            const twoMinutes = 2 * 60 * 1000;
            
            return (expiry - now) < twoMinutes;
        } catch (error) {
            console.error('Failed to check token expiry:', error);
            return true; // Assume expired if we can't check
        }
    },

    /**
     * Securely clears all authentication data from all storage locations
     */
    clearAll: (): void => {
        try {
            // Clear from all possible storage locations
            localStorage.removeItem(ACCESS_TOKEN_KEY);
            localStorage.removeItem(REFRESH_TOKEN_KEY);
            localStorage.removeItem(USER_DATA_KEY);
            localStorage.removeItem(TOKEN_EXPIRY_KEY);
            localStorage.removeItem(ENCRYPTION_KEY);
            
            sessionStorage.removeItem(ACCESS_TOKEN_KEY);
            sessionStorage.removeItem(USER_DATA_KEY);
            sessionStorage.removeItem(TOKEN_EXPIRY_KEY);
            sessionStorage.removeItem(SESSION_FINGERPRINT_KEY);
            
            // Clear legacy storage
            localStorage.removeItem(LEGACY_ACCESS_TOKEN_KEY);
            localStorage.removeItem(LEGACY_REFRESH_TOKEN_KEY);
            localStorage.removeItem(LEGACY_USER_DATA_KEY);
            localStorage.removeItem(LEGACY_TOKEN_EXPIRY_KEY);
            localStorage.removeItem('user'); // Very old storage format
            
            // Clear memory storage
            MemoryStorage.clear();
            
            console.log('All authentication data cleared securely');
        } catch (error) {
            console.error('Failed to clear authentication data:', error);
        }
    },

    /**
     * Validates that tokens exist and session is secure
     */
    isSessionValid: (): boolean => {
        try {
            // Check session fingerprint if enabled
            if (SESSION_BINDING_ENABLED && !tokenService.validateSessionFingerprint()) {
                return false;
            }
            
            let accessToken: string | null;
            if (USE_MEMORY_STORAGE) {
                accessToken = MemoryStorage.getItem(ACCESS_TOKEN_KEY);
            } else {
                accessToken = sessionStorage.getItem(ACCESS_TOKEN_KEY);
            }
            
            const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
            return !!(accessToken && refreshToken);
        } catch (error) {
            console.error('Session validation failed:', error);
            return false;
        }
    },

    /**
     * Gets remaining session time in milliseconds
     */
    getRemainingSessionTime: (): number => {
        try {
            let expiryStr: string | null;
            
            if (USE_MEMORY_STORAGE) {
                expiryStr = MemoryStorage.getItem(TOKEN_EXPIRY_KEY);
            } else {
                expiryStr = sessionStorage.getItem(TOKEN_EXPIRY_KEY);
            }
            
            if (!expiryStr) return 0;
            
            const expiry = parseInt(expiryStr);
            const remaining = expiry - Date.now();
            return remaining > 0 ? remaining : 0;
        } catch (error) {
            console.error('Failed to get remaining session time:', error);
            return 0;
        }
    },

    /**
     * Validates session fingerprint to prevent session hijacking
     */
    validateSessionFingerprint: (): boolean => {
        if (!SESSION_BINDING_ENABLED) return true;
        
        try {
            const storedFingerprint = sessionStorage.getItem(SESSION_FINGERPRINT_KEY);
            if (!storedFingerprint) return false;
            
            const currentFingerprint = CryptoUtils.generateSessionFingerprint();
            return storedFingerprint === currentFingerprint;
        } catch (error) {
            console.error('Session fingerprint validation failed:', error);
            return false;
        }
    },

    /**
     * Migrates tokens from legacy localStorage storage to secure storage
     */
    migrateLegacyTokens: async (): Promise<void> => {
        try {
            const legacyAccessToken = localStorage.getItem(LEGACY_ACCESS_TOKEN_KEY);
            const legacyRefreshToken = localStorage.getItem(LEGACY_REFRESH_TOKEN_KEY);
            const legacyUserData = localStorage.getItem(LEGACY_USER_DATA_KEY);
            const legacyExpiry = localStorage.getItem(LEGACY_TOKEN_EXPIRY_KEY);
            
            // Check for very old user storage format
            const veryOldUser = localStorage.getItem('user');
            
            if (legacyAccessToken || legacyRefreshToken || veryOldUser) {
                console.log('Migrating legacy token storage to secure format...');
                
                // If we have legacy tokens, migrate them
                if (legacyAccessToken && legacyRefreshToken) {
                    await tokenService.setTokens(legacyAccessToken, legacyRefreshToken);
                    
                    if (legacyUserData) {
                        try {
                            const user = JSON.parse(legacyUserData);
                            await tokenService.setUser(user);
                        } catch (error) {
                            console.error('Failed to migrate user data:', error);
                        }
                    }
                }
                
                // Handle very old user storage format
                if (veryOldUser && !legacyAccessToken) {
                    try {
                        const oldUser = JSON.parse(veryOldUser);
                        if (oldUser.token) {
                            await tokenService.setTokens(oldUser.token, oldUser.token);
                            await tokenService.setUser(oldUser);
                        }
                    } catch (error) {
                        console.error('Failed to migrate very old user data:', error);
                    }
                }
                
                // Clean up legacy storage
                localStorage.removeItem(LEGACY_ACCESS_TOKEN_KEY);
                localStorage.removeItem(LEGACY_REFRESH_TOKEN_KEY);
                localStorage.removeItem(LEGACY_USER_DATA_KEY);
                localStorage.removeItem(LEGACY_TOKEN_EXPIRY_KEY);
                localStorage.removeItem('user');
                
                console.log('Legacy token migration completed');
            }
        } catch (error) {
            console.error('Failed to migrate legacy tokens:', error);
        }
    },

    /**
     * Security audit function to check for potential vulnerabilities
     */
    performSecurityAudit: (): { secure: boolean; issues: string[] } => {
        const issues: string[] = [];
        
        try {
            // Check for legacy tokens that weren't migrated
            if (localStorage.getItem(LEGACY_ACCESS_TOKEN_KEY)) {
                issues.push('Legacy access token found in localStorage');
            }
            
            if (localStorage.getItem('user')) {
                issues.push('Very old user data format found in localStorage');
            }
            
            // Check encryption status
            if (!USE_ENCRYPTION) {
                issues.push('Token encryption is disabled');
            }
            
            // Check session binding
            if (!SESSION_BINDING_ENABLED) {
                issues.push('Session fingerprinting is disabled');
            }
            
            // Check if using memory storage
            if (!USE_MEMORY_STORAGE) {
                issues.push('Not using memory storage (tokens persist across page refreshes)');
            }
            
            return {
                secure: issues.length === 0,
                issues
            };
        } catch (error) {
            return {
                secure: false,
                issues: ['Security audit failed: ' + error]
            };
        }
    }
};

// Synchronous wrapper for backward compatibility
export const tokenServiceSync = {
    getAccessToken: () => tokenService.getAccessToken(),
    isAccessTokenExpiring: () => tokenService.isAccessTokenExpiring(),
    isSessionValid: () => tokenService.isSessionValid(),
    getRemainingSessionTime: () => tokenService.getRemainingSessionTime(),
    clearAll: () => tokenService.clearAll(),
    validateSessionFingerprint: () => tokenService.validateSessionFingerprint(),
    performSecurityAudit: () => tokenService.performSecurityAudit()
};