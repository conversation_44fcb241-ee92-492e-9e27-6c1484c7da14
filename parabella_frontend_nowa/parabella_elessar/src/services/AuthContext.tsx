// src/contexts/AuthContext.tsx
import React, { createContext, useState, useContext, useEffect, useCallback, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
// Import the new verification function
import { apiLogout, getCurrentUser, User, verifyAuth } from "./authService.ts";
import { isInvitationLink } from "./invitationHandler.ts";

interface AuthContextType {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    login: (user: User) => void;
    logout: () => void;
    hasPermission: (permissionKey: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [user, setUser] = useState<User | null>(null);
    const [isLoading, setIsLoading] = useState(true); // Start as true
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        // Check for logout parameter in URL
        const urlParams = new URLSearchParams(location.search);
        const shouldLogout = urlParams.get('logout') === 'true';
        const isInvitationLogout = urlParams.get('action') === 'invitation_logout';
        
        // If URL contains logout parameter, logout first
        if (shouldLogout || isInvitationLogout) {
            logout().then(() => {
                // Clean up URL parameters
                const newUrl = window.location.pathname;
                window.history.replaceState({}, '', newUrl);
            });
            return;
        }

        // On initial load, try to verify the existing token
        const checkLoggedInUser = async () => {
            try {
                const verifiedUser = await verifyAuth(); // This now handles API validation
                setUser(verifiedUser); // Will be the user object or null

                // MINIMAL REDIRECT LOGIC - Only redirect from root paths
                // Don't redirect if user is already on an app page
                if (verifiedUser) {
                    // Only auto-redirect from the root or login page
                    const isRootPath = location.pathname === '/' || 
                                      location.pathname === `${import.meta.env.BASE_URL}` ||
                                      location.pathname === '/authentication/login' ||
                                      location.pathname.includes('authentication/login');
                    
                    // Special case: don't redirect from invitation links
                    const isOnInvitationRoute = isInvitationLink(location.pathname);
                    
                    if (isRootPath && !isOnInvitationRoute) {
                        navigate(`${import.meta.env.BASE_URL}mithril/CreateNewProjectDMA`);
                    }
                    // If user is on any other route, let them stay there
                }
            } catch (error) {
                // In case verifyAuth itself throws an unhandled error
                console.error("Auth check failed", error);
                setUser(null);
            } finally {
                setIsLoading(false);
            }
        };

        checkLoggedInUser();
    }, [location.pathname, location.search]); // Re-run when pathname or search params change

    // This function is called by the Login component upon a successful API response
    const login = (loggedInUser: User) => {
        setUser(loggedInUser);
        // Navigate to the main application page after successful login
        navigate(`${import.meta.env.BASE_URL}mithril/CreateNewProjectDMA`);
    };

    const logout = async () => {
        await apiLogout();
        setUser(null);
        // Redirect to login page after logout
        navigate('/authentication/login');
    };

    const hasPermission = useCallback((permissionKey: string): boolean => {
        if (!user || !user.permissions) {
            return false;
        }
        // Admin role has all permissions
        if (user.roles?.includes("Admin")) {
            return true;
        }
        return user.permissions.includes(permissionKey);
    }, [user]);

    const value = {
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        logout,
        hasPermission
    };

    // Render children only when not loading to prevent showing the login page
    // for a split second before redirecting a logged-in user.
    return (
        <AuthContext.Provider value={value}>
            {!isLoading && children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};