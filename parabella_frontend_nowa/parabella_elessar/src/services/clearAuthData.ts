/**
 * Utility to clear all authentication data
 * Run this in browser console if you need to clear all auth data:
 * clearAllAuthData()
 */
export const clearAllAuthData = () => {
    // Clear all possible auth-related keys
    const authKeys = [
        'user',
        'parabella_access_token', 
        'parabella_refresh_token',
        'parabella_user_data',
        'parabella_token_expiry',
        'token_expiry',
        'parabella_session_fp'
    ];
    
    authKeys.forEach(key => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
    });
    
    console.log('All authentication data cleared');
};

/**
 * Debug function to check current auth state
 */
export const debugAuthState = () => {
    console.log('=== AUTH DEBUG ===');
    console.log('Access Token:', localStorage.getItem('parabella_access_token')?.substring(0, 20) + '...');
    console.log('Refresh Token:', localStorage.getItem('parabella_refresh_token')?.substring(0, 20) + '...');
    console.log('User Data:', localStorage.getItem('parabella_user_data'));
    console.log('Token Expiry:', localStorage.getItem('parabella_token_expiry'));
    console.log('Old User:', localStorage.getItem('user'));
    console.log('Current URL:', window.location.pathname);
    console.log('==================');
};

// Make it available globally for debugging
if (typeof window !== 'undefined') {
    (window as any).clearAllAuthData = clearAllAuthData;
    (window as any).debugAuthState = debugAuthState;
}