// src/config/api.ts
import axios, { AxiosError } from 'axios';
import {API_BASE_URL} from "../config/APIEndpoints.ts";
import {apiLogout, refreshAccessToken} from "./authService.ts";
import { tokenService, tokenServiceSync } from './tokenService';


/**
 * Create a dedicated Axios instance for API requests.
 * This allows us to centralize configuration and interceptors.
 */
const api = axios.create({
    baseURL: API_BASE_URL,

});

// Track if we're currently refreshing the token
let isRefreshing = false;
let failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });
    
    failedQueue = [];
};

/**
 * Request Interceptor:
 * This runs before each request is sent. Its job is to attach the
 * authentication token to the request headers.
 */
api.interceptors.request.use(
    async (config) => {
        // Check if token is expiring soon and refresh proactively
        if (tokenServiceSync.isAccessTokenExpiring() && !isRefreshing) {
            isRefreshing = true;
            try {
                await refreshAccessToken();
            } catch (error) {
                console.error("Proactive token refresh failed:", error);
            } finally {
                isRefreshing = false;
            }
        }
        
        const token = tokenServiceSync.getAccessToken();
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        // Handle request errors (e.g., network issues)
        return Promise.reject(error);
    }
);

/**
 * Response Interceptor:
 * This runs after a response is received. Its main job here is to
 * handle global errors, especially authentication errors (401).
 */
api.interceptors.response.use(
    (response) => {
        // If the request was successful, just return the response
        return response;
    },
    async (error: AxiosError) => {
        const originalRequest = error.config as any;
        
        // Check if the error is a 401 Unauthorized response
        if (error.response?.status === 401 && !originalRequest._retry) {
            // Don't retry refresh endpoints
            if (originalRequest.url?.includes('/auth/refresh') || 
                originalRequest.url?.includes('/auth/signin')) {
                // If refresh itself failed, logout
                await apiLogout();
                window.location.href = '/authentication/login';
                return Promise.reject(error);
            }
            
            if (isRefreshing) {
                // If we're already refreshing, queue this request
                return new Promise((resolve, reject) => {
                    failedQueue.push({ resolve, reject });
                }).then(token => {
                    originalRequest.headers['Authorization'] = 'Bearer ' + token;
                    return api(originalRequest);
                }).catch(err => {
                    return Promise.reject(err);
                });
            }
            
            originalRequest._retry = true;
            isRefreshing = true;
            
            try {
                const newToken = await refreshAccessToken();
                if (newToken) {
                    // Process queued requests with new token
                    processQueue(null, newToken);
                    
                    // Retry original request with new token
                    originalRequest.headers['Authorization'] = 'Bearer ' + newToken;
                    return api(originalRequest);
                }
            } catch (refreshError) {
                // Refresh failed, process queue with error
                processQueue(refreshError, null);
                
                // Logout and redirect
                await apiLogout();
                window.location.href = '/authentication/login';
                return Promise.reject(refreshError);
            } finally {
                isRefreshing = false;
            }
        }

        // For all other errors, just pass them along
        return Promise.reject(error);
    }
);

export default api;