/**
 * Invitation Handler Utility
 * Provides utilities for handling user invitation flows
 */

import { tokenService } from './tokenService';

/**
 * Checks if the current URL is an invitation or special authentication link
 */
export const isInvitationLink = (pathname: string): boolean => {
    // Be very specific about invitation routes to avoid false positives
    const invitationPatterns = [
        /^\/pages\/authentication\/setpassword\/[^\/]+$/,  // /pages/authentication/setpassword/{token}
        /^\/pages\/authentication\/resetpassword\/[^\/]+$/, // /pages/authentication/resetpassword/{token}
        /^\/stakeholder\/main\/[^\/]+$/,                    // /stakeholder/main/{token}
        /^\/stakeholder\/impact-financial-analysis\/[^\/]+$/ // /stakeholder/impact-financial-analysis/{token}
    ];
    
    return invitationPatterns.some(pattern => pattern.test(pathname));
};

/**
 * Handles invitation link access when user is already authenticated
 */
export const handleInvitationConflict = (pathname: string): {
    hasConflict: boolean;
    message: string;
    action: 'clear_session' | 'continue' | 'none';
} => {
    const hasActiveSession = !!tokenService.getAccessToken();
    
    if (!hasActiveSession || !isInvitationLink(pathname)) {
        return {
            hasConflict: false,
            message: '',
            action: 'none'
        };
    }
    
    // Determine the type of invitation
    let invitationType = 'user account';
    if (pathname.includes('/setpassword/')) {
        invitationType = 'new user account';
    } else if (pathname.includes('/resetpassword/')) {
        invitationType = 'password reset';
    } else if (pathname.includes('/stakeholder/')) {
        invitationType = 'stakeholder access';
    }
    
    return {
        hasConflict: true,
        message: `You're currently logged in, but this link is for ${invitationType} setup. You may want to clear your current session first.`,
        action: 'clear_session'
    };
};

/**
 * Clears current session for invitation handling
 */
export const clearSessionForInvitation = (): void => {
    tokenService.clearAll();
    console.log('Session cleared for invitation handling');
};

/**
 * Creates a specialized logout URL for invitation conflicts
 */
export const createInvitationLogoutUrl = (returnUrl: string): string => {
    const baseUrl = window.location.origin;
    const encodedReturnUrl = encodeURIComponent(returnUrl);
    return `${baseUrl}/authentication/login?return=${encodedReturnUrl}&action=invitation_logout`;
};