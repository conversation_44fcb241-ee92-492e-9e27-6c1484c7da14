/**
 * Security Test Suite
 * Tests the security improvements and validates XSS protection
 */

import { tokenServiceSync } from './tokenService';

export const runSecurityTests = () => {
    console.group('🔒 Security Audit Results');
    
    // Run security audit
    const audit = tokenServiceSync.performSecurityAudit();
    
    console.log('📊 Security Status:', audit.secure ? '✅ SECURE' : '❌ VULNERABLE');
    
    if (audit.issues.length > 0) {
        console.warn('⚠️ Security Issues Found:');
        audit.issues.forEach((issue, index) => {
            console.warn(`   ${index + 1}. ${issue}`);
        });
    } else {
        console.log('✅ No security issues detected');
    }
    
    // Test XSS protection
    console.group('🛡️ XSS Protection Tests');
    
    // Test 1: Chart innerHTML protection
    const testElement = document.createElement('div');
    testElement.className = 'cmeta';
    const spanElement = document.createElement('span');
    spanElement.className = 'commits';
    testElement.appendChild(spanElement);
    document.body.appendChild(testElement);
    
    // Simulate chart update with potentially malicious content
    const maliciousContent = '<script>alert("XSS")</script>123';
    spanElement.textContent = maliciousContent; // Safe method
    
    const actualContent = spanElement.textContent;
    if (actualContent === maliciousContent) {
        console.log('✅ Chart XSS protection: textContent correctly sanitizes input');
    } else {
        console.error('❌ Chart XSS protection: Failed');
    }
    
    // Cleanup
    document.body.removeChild(testElement);
    
    console.groupEnd();
    
    // Test token storage security
    console.group('🔐 Token Storage Security');
    
    // Check if legacy tokens exist
    const hasLegacyTokens = localStorage.getItem('parabella_access_token') || 
                          localStorage.getItem('user');
    
    if (hasLegacyTokens) {
        console.warn('⚠️ Legacy tokens detected - migration recommended');
    } else {
        console.log('✅ No legacy tokens found');
    }
    
    // Check session fingerprinting
    const fingerprintValid = tokenServiceSync.validateSessionFingerprint();
    console.log('🔍 Session fingerprinting:', fingerprintValid ? '✅ Active' : '⚠️ Inactive');
    
    console.groupEnd();
    console.groupEnd();
    
    return {
        secure: audit.secure && !hasLegacyTokens,
        issues: audit.issues,
        recommendations: [
            ...(hasLegacyTokens ? ['Migrate legacy tokens'] : []),
            ...(audit.issues.length > 0 ? ['Address security audit issues'] : []),
            'Consider enabling memory storage for maximum security',
            'Implement httpOnly cookies on backend for refresh tokens'
        ]
    };
};

// Auto-run in development
if (process.env.NODE_ENV === 'development') {
    // Run security tests after a brief delay to ensure services are initialized
    setTimeout(() => {
        runSecurityTests();
    }, 1000);
}