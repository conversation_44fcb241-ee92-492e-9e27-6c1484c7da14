// src/services/authService.ts
import axios from 'axios';
import { API_BASE_URL } from "../config/APIEndpoints.ts";
import { jwtDecode } from "jwt-decode";
import api from "./api.ts";
import { tokenService, tokenServiceSync } from './tokenService';

const API_AUTH_URL = `${API_BASE_URL}/auth`;

export interface User {
    id: number;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    token: string;
}

// Helper to decode the JWT and get user details
const decodeToken = (token: string): Omit<User, 'token'> | null => {
    try {
        const decoded: any = jwtDecode(token);
        return {
            id: decoded.id,
            username: decoded.sub, // 'sub' is standard for subject/username in JWT
            email: decoded.email,
            roles: decoded.roles || [],
            permissions: decoded.permissions || [],
        };
    } catch (error) {
        console.error("Failed to decode JWT:", error);
        return null;
    }
};

/**
 * Handles the multi-step login process, including 2FA.
 * Instead of multiple functions, we use one function that returns the state of the login process.
 */
export const apiLogin = async (username: string, password: string, totpCode?: string) => {
    try {
        const response = await axios.post(`${API_AUTH_URL}/signin`, { username, password, totpCode });
        console.log("Login response:", response.data);
        
        // Check for different possible response formats
        let accessToken = response.data.accessToken || response.data.token;
        let refreshToken = response.data.refreshToken;
        
        // If we have an access token but no refresh token, create a temporary one
        if (accessToken && !refreshToken) {
            console.warn("No refresh token received, using access token as temporary refresh token");
            refreshToken = accessToken; // Temporary fallback
        }
        
        if (accessToken) {
            console.log("Tokens received - Access:", accessToken?.substring(0, 20) + "...", "Refresh:", refreshToken?.substring(0, 20) + "...");
            
            const decodedUser = decodeToken(accessToken);
            if (!decodedUser) {
                throw new Error("Invalid token received from server.");
            }
            
            // Store tokens securely
            await tokenService.setTokens(accessToken, refreshToken || accessToken);
            
            // Store user data
            const user: User = { ...decodedUser, token: accessToken };
            await tokenService.setUser(user);
            
            console.log("User logged in:", user);
            
            // Verify tokens were stored
            console.log("Stored access token:", tokenServiceSync.getAccessToken()?.substring(0, 20) + "...");
            const storedRefreshToken = await tokenService.getRefreshToken();
            console.log("Stored refresh token:", storedRefreshToken?.substring(0, 20) + "...");
            
            return { success: true, user };
        }
        // This case should ideally not be hit if backend is consistent
        console.error("No access token in response:", response.data);
        return { success: false, error: 'No access token received from server. Please check backend configuration.' };

    } catch (err: any) {
        if (err.response && err.response.data) {
            const { message, qrCodeUrl } = err.response.data;
            const msg = message.toLowerCase();

            if (msg.includes('2fa setup required')) {
                return { success: false, stage: '2fa_setup', qrCodeUrl, username };
            }
            if (msg.includes('invalid or missing totp code')) {
                return { success: false, stage: '2fa_verify' };
            }
            return { success: false, error: message || 'Invalid credentials' };
        }
        return { success: false, error: 'A network error occurred.' };
    }
};


/**
 * Verifies the 2FA setup with the code from the authenticator app.
 */
export const verify2FASetup = async (username: string, code: string) => {
    try {
        await axios.post(`${API_AUTH_URL}/verify2fa`, { username, code });
        return { success: true };
    } catch (err: any) {
        return { success: false, error: err.response?.data?.message || "Failed to verify 2FA code." };
    }
};
/**
 * Refreshes the access token using the refresh token
 */
export const refreshAccessToken = async (): Promise<string | null> => {
    const refreshToken = await tokenService.getRefreshToken();
    if (!refreshToken) {
        return null;
    }

    try {
        const response = await axios.post(`${API_AUTH_URL}/refresh`, { refreshToken });
        const { accessToken, refreshToken: newRefreshToken } = response.data;
        
        if (accessToken && newRefreshToken) {
            // Update tokens
            await tokenService.setTokens(accessToken, newRefreshToken);
            
            // Update user data with new token
            const decodedUser = decodeToken(accessToken);
            if (decodedUser) {
                const user: User = { ...decodedUser, token: accessToken };
                await tokenService.setUser(user);
            }
            
            return accessToken;
        }
        return null;
    } catch (error) {
        console.error("Failed to refresh token:", error);
        // If refresh fails, clear all auth data
        apiLogout();
        return null;
    }
};

/**
 * NEW: Verifies the token with the backend and fetches user data.
 * This is used for the "auto-login" flow on app load.
 */
export const verifyAuth = async (): Promise<User | null> => {
    // Try to migrate from old storage format if exists
    const oldUserData = localStorage.getItem('user');
    if (oldUserData && !tokenService.getAccessToken()) {
        try {
            const oldUser = JSON.parse(oldUserData);
            if (oldUser.token) {
                // Migrate old token to new storage
                console.log("Migrating old session data...");
                tokenService.setTokens(oldUser.token, oldUser.token); // Use same token for both temporarily
                tokenService.setUser(oldUser);
            }
        } catch (e) {
            console.error("Failed to migrate old session:", e);
        }
        localStorage.removeItem('user'); // Remove old user key after migration attempt
    }
    
    const token = tokenService.getAccessToken();
    console.log("Verifying auth with token:", token ? token.substring(0, 20) + "..." : null);
    
    if (!token) {
        return null; // No token, no session
    }

    try {
        // Check if token is expiring soon and refresh if needed
        if (tokenService.isAccessTokenExpiring()) {
            console.log("Token expiring soon, refreshing...");
            const newToken = await refreshAccessToken();
            if (!newToken) {
                return null;
            }
        }

        // Try to validate with backend, but fallback to token decode if it fails
        try {
            const response = await api.get(`${API_AUTH_URL}/me`);
            
            if (response.data) {
                const userData = response.data;
                const currentToken = tokenService.getAccessToken();
                const user: User = {
                    id: userData.id,
                    username: userData.username,
                    email: userData.email,
                    roles: userData.roles || [],
                    permissions: userData.permissions || [],
                    token: currentToken || ''
                };
                
                tokenService.setUser(user);
                return user;
            }
        } catch (apiError) {
            console.warn("API validation failed, falling back to token decode:", apiError);
            
            // Fallback: decode token locally if API fails
            const currentToken = tokenService.getAccessToken();
            if (currentToken) {
                const decodedUser = decodeToken(currentToken);
                if (decodedUser) {
                    const user: User = { ...decodedUser, token: currentToken };
                    tokenService.setUser(user);
                    return user;
                }
            }
        }
        
        return null;

    } catch (error) {
        console.error("Token validation failed:", error);
        // Token is invalid or expired, so clear the stored data
        tokenService.clearAll();
        return null;
    }
};

/**
 * Logs the user out by clearing stored data and revoking tokens on backend.
 */
export const apiLogout = async () => {
    try {
        // Call backend to revoke refresh tokens
        await api.post(`${API_AUTH_URL}/logout`);
    } catch (error) {
        console.error("Logout API call failed:", error);
        // Continue with local cleanup even if API call fails
    }
    
    // Clear all local auth data
    tokenServiceSync.clearAll();
};

/**
 * Gets the current user from secure storage.
 * This is a synchronous fallback for backward compatibility.
 * For new code, use getCurrentUserAsync() for better security.
 */
export const getCurrentUser = (): User | null => {
    try {
        // Try to get user data from the new secure storage first
        const userData = sessionStorage.getItem('parabella_user_data_v2');
        if (userData) {
            return JSON.parse(userData);
        }
        
        // Fallback to legacy storage for migration
        const legacyUserData = localStorage.getItem('parabella_user_data');
        if (legacyUserData) {
            return JSON.parse(legacyUserData);
        }
        
        // Last resort: decode from access token
        const accessToken = sessionStorage.getItem('parabella_access_token_v2') || 
                          localStorage.getItem('parabella_access_token');
        if (accessToken) {
            const decoded = decodeToken(accessToken);
            if (decoded) {
                return { ...decoded, token: accessToken };
            }
        }
        
        return null;
    } catch (error) {
        console.error('Error getting current user:', error);
        return null;
    }
};

/**
 * Gets the current user from secure storage (async version).
 * Recommended for new code as it supports full encryption/decryption.
 */
export const getCurrentUserAsync = async (): Promise<User | null> => {
    return await tokenService.getUser();
};

/**
 * Gets just the authentication token from secure storage.
 */
export const getAuthToken = (): string | null => {
    return tokenService.getAccessToken();
};

export const getAssignableUsers = async (): Promise<User[]> => {
    // This endpoint should return a list of users, e.g., those with 'ADMIN' or 'SUPER_USER' roles.
    // The backend controller for this needs to be created.
    const response = await api.get('/users/assignable');
    return response.data;
};

