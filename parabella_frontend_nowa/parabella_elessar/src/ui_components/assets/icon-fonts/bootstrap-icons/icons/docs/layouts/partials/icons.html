<div class="my-5">
  <div class="d-flex mb-4">
    <h2 id="icons" class="mb-0">Icons</h2>
    <form class="subnav-search d-flex flex-nowrap ms-auto">
      <label for="search" class="visually-hidden">Search for icons</label>
      <input class="form-control search mb-0" id="search" type="search" placeholder="Start typing to filter..." autocomplete="off">
    </form>
  </div>
  <ul class="row row-cols-3 row-cols-sm-4 row-cols-lg-6 row-cols-xl-8 list-unstyled list">
    {{ range (where .Site.RegularPages "Type" "icons") -}}
      {{- $filename := .File.TranslationBaseName -}}
      {{- with .Site.GetPage .File.Path }}
      <li class="col mb-4"{{ with .Params.tags }} data-tags="{{ delimit . " " }}"{{ end }}{{ with .Params.categories }} data-categories="{{ delimit . " " | lower }}"{{ end }}>
        <a class="d-block text-dark text-decoration-none" href="{{ .RelPermalink }}">
          <div class="p-3 py-4 mb-2 bg-light text-center rounded">
            <svg class="bi" width="1em" height="1em" fill="currentColor">
              <use xlink:href="bootstrap-icons.svg#{{ $filename }}"/>
            </svg>
          </div>
          <div class="name text-muted text-decoration-none text-center pt-1">{{ $filename }}</div>
        </a>
      </li>
      {{- end }}
    {{- end }}
  </ul>
</div>
