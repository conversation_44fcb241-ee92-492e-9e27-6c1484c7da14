import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { getRoles, getPermissions, updateRolePermissions, createRole, deleteRole } from './settingsApi'; // Assuming API functions exist
import { Role, Permission } from './types'; // Assuming types exist
import { Spinner, Alert, Form, Button, Modal, Table, Tooltip, OverlayTrigger, Container } from 'react-bootstrap';
import { Trash, PlusCircleFill, ArrowCounterclockwise } from 'react-bootstrap-icons';
import {useAuth} from "../../../../services/AuthContext.tsx";

type RolePermissionState = Record<number, Set<number>>;
type DirtyRoles = Set<number>;

const RoleManagement: React.FC = () => {
    const { hasPermission } = useAuth();
    const canViewRoles = hasPermission('role.view');
    const canCreateRoles = hasPermission('role.create');
    const canEditRoles = hasPermission('role.edit');
    const canDeleteRoles = hasPermission('role.delete');
    
    const [roles, setRoles] = useState<Role[]>([]);
    const [permissions, setPermissions] = useState<Permission[]>([]);
    const [originalRolePermissions, setOriginalRolePermissions] = useState<RolePermissionState>({});
    const [rolePermissions, setRolePermissions] = useState<RolePermissionState>({});
    const [dirtyRoles, setDirtyRoles] = useState<DirtyRoles>(new Set());
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [newRoleName, setNewRoleName] = useState('');
    const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);

    const fetchData = useCallback(async () => {
        if (!canViewRoles) {
            setIsLoading(false);
            return;
        }
        
        setIsLoading(true);
        setError(null);
        try {
            const [rolesData, permissionsData] = await Promise.all([getRoles(), getPermissions()]);
            setRoles(rolesData);
            setPermissions(permissionsData);
            const initialState: RolePermissionState = {};
            rolesData.forEach(role => {
                initialState[role.id] = new Set(role.permissions.map(p => p.id));
            });
            setRolePermissions(initialState);
            // Deep copy for original state to prevent mutation
            setOriginalRolePermissions(JSON.parse(JSON.stringify(initialState)));
            setDirtyRoles(new Set());
        } catch (err) {
            setError('Failed to load role data. Please refresh the page.');
        } finally {
            setIsLoading(false);
        }
    }, [canViewRoles]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const groupedPermissions = useMemo(() => {
        return permissions.reduce<Record<string, Permission[]>>((acc, p) => {
            (acc[p.category] = acc[p.category] || []).push(p);
            return acc;
        }, {});
    }, [permissions]);

    const handleToggle = (roleId: number, permissionId: number) => {
        setRolePermissions(prevState => {
            const newState = { ...prevState };
            const permissionsForRole = new Set(newState[roleId]);
            if (permissionsForRole.has(permissionId)) {
                permissionsForRole.delete(permissionId);
            } else {
                permissionsForRole.add(permissionId);
            }
            newState[roleId] = permissionsForRole;
            return newState;
        });
        setDirtyRoles(prev => new Set(prev).add(roleId));
    };

    const handleSaveChanges = async () => {
        setIsSaving(true);
        try {
            const updatePromises = Array.from(dirtyRoles).map(roleId => {
                const permissionIds = Array.from(rolePermissions[roleId]);
                return updateRolePermissions({ roleId, permissionIds });
            });
            await Promise.all(updatePromises);
            await fetchData(); // Refresh all data to be safe
        } catch (err) {
            setError('An error occurred while saving.');
        } finally {
            setIsSaving(false);
        }
    };

    const handleCreateRole = async () => {
        if (!newRoleName.trim()) return;
        setIsSaving(true);
        try {
            await createRole(newRoleName, []);
            setShowCreateModal(false);
            setNewRoleName('');
            await fetchData();
        } catch (err) {
            setError('Failed to create role.');
        } finally {
            setIsSaving(false);
        }
    };

    const handleDeleteRole = async () => {
        if (!roleToDelete) return;
        setIsSaving(true);
        try {
            await deleteRole(roleToDelete.id);
            setRoleToDelete(null);
            await fetchData();
        } catch (err) {
            const errorMessage = (err as any)?.response?.data?.message || 'Role might be in use.';
            setError(`Failed to delete role: ${errorMessage}`);
        } finally {
            setIsSaving(false);
        }
    };

    const handleDiscardChanges = () => {
        // Deep copy from original state to restore
        setRolePermissions(JSON.parse(JSON.stringify(originalRolePermissions)));
        setDirtyRoles(new Set());
    };

    if (isLoading) return <div className="centered-spinner"><Spinner animation="border" /></div>;
    
    if (!canViewRoles) {
        return (
            <Container className="text-center mt-5">
                <h1>Access Denied</h1>
                <p>You do not have permission to view roles and permissions. Please contact your administrator if you need access.</p>
            </Container>
        );
    }
    
    if (error) return <Alert variant="danger">{error}</Alert>;

    return (
        <div className="settings-view-container">
            <div className="view-header">
                <h2 className="view-title">Role Management</h2>
                <Button variant="primary" onClick={() => setShowCreateModal(true)}>
                    <PlusCircleFill className="me-2" />Create Role
                </Button>
            </div>

            <div className="table-responsive">
                <Table hover className="modern-table role-management-table">
                    <thead>
                    <tr>
                        <th>Function / Permission</th>
                        {roles.map(role => (
                            <th key={role.id} className="text-center">
                                <div className="d-flex align-items-center justify-content-center">
                                    <span>{role.name}</span>
                                    <OverlayTrigger overlay={<Tooltip>Delete Role</Tooltip>}>
                                        <Button variant="link" className="icon-button-danger ms-2 p-0" onClick={() => setRoleToDelete(role)}>
                                            <Trash size={16} />
                                        </Button>
                                    </OverlayTrigger>
                                </div>
                            </th>
                        ))}
                    </tr>
                    </thead>
                    <tbody>
                    {Object.entries(groupedPermissions).map(([category, perms]) => (
                        <React.Fragment key={category}>
                            <tr className="table-category-header">
                                <td colSpan={roles.length + 1}><strong>{category}</strong></td>
                            </tr>
                            {perms.map(permission => {
                                // --- CHANGE START ---
                                // Check if the permission belongs to a system-managed category.
                                const disabledKeywords = ['Materiality', 'CSRD', 'Analytics'];
                                const isSystemManagedPermission = disabledKeywords.some(keyword => category.includes(keyword));
                                // --- CHANGE END ---

                                return (
                                    <tr key={permission.id}>
                                        <td>{permission.functionName}</td>
                                        {roles.map(role => (
                                            <td key={`${role.id}-${permission.id}`} className="text-center">
                                                {/* Conditionally render with a tooltip if it's a disabled system permission */}
                                                {isSystemManagedPermission ? (
                                                    <OverlayTrigger
                                                        placement="top"
                                                        overlay={<Tooltip>These permissions are managed automatically and cannot be changed here.</Tooltip>}
                                                    >
                                                        {/* This span is required for the tooltip to work on a disabled element */}
                                                        <span className="d-inline-block">
                                                        <Form.Check
                                                            type="switch"
                                                            id={`switch-${role.id}-${permission.id}`}
                                                            checked={rolePermissions[role.id]?.has(permission.id) || false}
                                                            onChange={() => {}} // No-op
                                                            disabled={true}
                                                            readOnly
                                                        />
                                                    </span>
                                                    </OverlayTrigger>
                                                ) : (
                                                    <Form.Check
                                                        type="switch"
                                                        id={`switch-${role.id}-${permission.id}`}
                                                        checked={rolePermissions[role.id]?.has(permission.id) || false}
                                                        onChange={() => handleToggle(role.id, permission.id)}
                                                        disabled={isSaving}
                                                    />
                                                )}
                                            </td>
                                        ))}
                                    </tr>
                                )})}
                        </React.Fragment>
                    ))}
                    </tbody>
                </Table>
            </div>

            {dirtyRoles.size > 0 && (
                <div className="save-changes-footer">
                    <Button variant="light" disabled={isSaving} onClick={handleDiscardChanges}>
                        <ArrowCounterclockwise className="me-2" />
                        Discard
                    </Button>
                    <Button variant="primary" disabled={isSaving} onClick={handleSaveChanges}>
                        {isSaving ? 'Saving...' : `Save Changes (${dirtyRoles.size})`}
                    </Button>
                </div>
            )}

            <Modal centered show={showCreateModal} onHide={() => setShowCreateModal(false)}>
                <Modal.Header closeButton><Modal.Title>Create New Role</Modal.Title></Modal.Header>
                <Modal.Body>
                    <Form.Group>
                        <Form.Label>Role Name</Form.Label>
                        <Form.Control type="text" value={newRoleName} onChange={(e) => setNewRoleName(e.target.value)} placeholder="e.g., Project Manager" autoFocus />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowCreateModal(false)}>Cancel</Button>
                    <Button variant="primary" onClick={handleCreateRole} disabled={isSaving || !newRoleName.trim()}>
                        {isSaving ? 'Creating...' : 'Create Role'}
                    </Button>
                </Modal.Footer>
            </Modal>

            <Modal centered show={!!roleToDelete} onHide={() => setRoleToDelete(null)}>
                <Modal.Header closeButton><Modal.Title>Confirm Deletion</Modal.Title></Modal.Header>
                <Modal.Body>
                    <p>Are you sure you want to delete the role "<strong>{roleToDelete?.name}</strong>"? This action cannot be undone.</p>
                    <Alert variant="warning" className="mt-3">Note: You cannot delete a role if it is currently assigned to any users.</Alert>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setRoleToDelete(null)}>Cancel</Button>
                    <Button variant="danger" onClick={handleDeleteRole} disabled={isSaving}>
                        {isSaving ? 'Deleting...' : 'Delete Role'}
                    </Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
};

export default RoleManagement;