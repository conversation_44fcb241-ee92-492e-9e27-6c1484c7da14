// src/modules/csrd/api/csrdApi.ts
import {
    AutofillDatapointRequestDTO,
    AutofillDatapointResponseDTO, CoverageAnalysisResultDto, CsrdDatapointResponseDTO, CsrdSubtopicGeneratedTextDTO,
    ProcessedDocumentChunkDTO
} from "./csrdApiTypes.tsx";
import api from "../../../../services/api.ts";
import {
    CompanyInfo,
    CsrdProject,
    CsrdSubtopic,
    CsrdTopic, JobStatusApi,
    ReportDetails,
    ReportListItem
} from "../main/modules/types.ts";
import axios, {AxiosError} from "axios";
import {API_PYTHON_AI_URL} from "../../../../config/APIEndpoints.ts"; // Adjust path as needed

// import {API_BASE_URL} from "../../../../config/APIEndpoints.ts"; // Not used directly here if api instance is configured


export async function fetchCsrdTopics(): Promise<CsrdTopic[]> {
    try {
        const { data } = await api.get<CsrdTopic[]>('/csrd/topics');
        return data;
    } catch (error: any) {
        if (error && error.response) {
            throw new Error(
                `Failed to fetch topics. Status: ${error.response.status}, Message: ${error.response.data}`
            );
        } else {
            throw new Error(`Failed to fetch topics: ${error.message || error}`);
        }
    }
}

export async function fetchSubtopicDetails(subtopicId: number | string): Promise<CsrdSubtopic> { // Allow string ID
    const response= await api.get(`/csrd/subtopics/${subtopicId}`);
    return response.data;
}

export const fetchCsrdData = async (): Promise<CsrdTopic[]> => {
    try {
        const response = await api.get<CsrdTopic[]>(`csrd/data`);
        return response.data;
    } catch (error) {
        console.error("Error fetching CSRD data:", error);
        throw error;
    }
};

interface CsrdProjectDTO {
    id: number;
    userId: number;
    projectName: string;
    projectDescription: string;
    projectType: 'COMPANY' | 'COMPANY_GROUP';
    createdAt: string;
    updatedAt: string;
    companyInfo?: CompanyInfoDTO | null;
}

interface CompanyInfoDTO {
    id?: number;
    companyName: string;
    revenue: string;
    industry: string;
    size: string;
    numberOfEmployees: string;
}

const mapCompanyInfoDTOToType = (dto: CompanyInfoDTO | null | undefined): CompanyInfo | null => {
    if (!dto) return null;
    return {
        companyName: dto.companyName,
        revenue: dto.revenue,
        industry: dto.industry,
        size: dto.size,
        numberOfEmployees: dto.numberOfEmployees,
    };
};

export const fetchUserCsrdProjects = async (userId: number): Promise<CsrdProject[]> => {
    const response = await api.get<CsrdProjectDTO[]>(`/csrd-projects/user/${userId}`);
    return response.data.map(dto => ({
        ...dto,
        id: dto.id.toString(), // Ensure ID is string if types expect it
        companyInfo: mapCompanyInfoDTOToType(dto.companyInfo)
    }));
};

export const createCsrdProjectApi = async (projectData: {
    userId: number;
    projectName: string;
    projectDescription: string;
    projectType: 'company' | 'companyGroup';
}): Promise<CsrdProject> => {
    const payload = {
        ...projectData,
        projectType: projectData.projectType === 'companyGroup' ? 'COMPANY_GROUP' : 'COMPANY'
    };
    const response = await api.post<CsrdProjectDTO>('/csrd-projects/create', payload);
    return {
        ...response.data,
        id: response.data.id.toString(),
        companyInfo: mapCompanyInfoDTOToType(response.data.companyInfo)
    };
};

export const saveCompanyInfoApi = async (projectId: number | string, companyInfoData: CompanyInfo): Promise<CompanyInfo> => {
    const response = await api.put<CompanyInfoDTO>(`/csrd-projects/${projectId}/company-info`, companyInfoData);
    return mapCompanyInfoDTOToType(response.data)!;
};

export const fetchCompanyInfoForProject = async (projectId: number | string): Promise<CompanyInfo | null> => {
    try {
        const response = await api.get<CompanyInfoDTO>(`/csrd-projects/${projectId}/company-info`);
        return mapCompanyInfoDTOToType(response.data);
    } catch (error: any) {
        if (error.response && error.response.status === 404) {
            return null;
        }
        console.error("Error fetching company info:", error);
        throw error;
    }
};

export const fetchCsrdProjectDetails = async (projectId: number | string): Promise<CsrdProject> => {
    const response = await api.get<CsrdProjectDTO>(`/csrd-projects/${projectId}`);
    return {
        ...response.data,
        id: response.data.id.toString(),
        companyInfo: mapCompanyInfoDTOToType(response.data.companyInfo)
    };
};

export const fetchRelevantChunks = async (
    projectId: number | string, // Allow string ID
    disclosureRequirement: string,
    sourceId: string
): Promise<ProcessedDocumentChunkDTO[]> => {
    try {
        const response = await api.get<ProcessedDocumentChunkDTO[]>(`/csrd/document-chunks/relevant`, {
            params: {
                projectId,
                disclosureRequirement: disclosureRequirement || '',
                sourceId: sourceId || ''
            }
        });
        return response.data;
    } catch (error) {
        console.error(`API Error fetching relevant chunks for project ${projectId}:`, error);
        throw error;
    }
};

export const autofillDatapointWithAI = async (
    payload: AutofillDatapointRequestDTO
): Promise<AutofillDatapointResponseDTO> => {
    try {
        const response = await api.post<AutofillDatapointResponseDTO>(`/ai/autofill-datapoint`, payload);
        return response.data;
    } catch (error) {
        console.error(`API Error during AI autofill for datapoint ${payload.datapointId}:`, error);
        throw error;
    }
};

export const fetchCoverageAnalysis = async (
    projectId: number | string // Allow string ID
): Promise<CoverageAnalysisResultDto> => {
    const endpointPath = `/projects/${projectId}/coverage-analysis`;
    try {
        const response = await api.get<CoverageAnalysisResultDto>(endpointPath);
        return response.data;
    } catch (error) {
        console.error(`API Error fetching coverage analysis for project ${projectId}:`, error);
        throw error;
    }
};


/**
 * Creates or updates a CSRD datapoint response.
 * This function now always uses POST, sending the data to the backend's
 * single "upsert" endpoint.
 */
export async function saveOrUpdateCsrdDatapointResponse(
    responseDto: CsrdDatapointResponseDTO
): Promise<CsrdDatapointResponseDTO> {
    try {
        const endpoint = `/csrd/datapoint-responses`;
        // Always POST to the single endpoint; the backend handles the create/update logic.
        const response = await api.post<CsrdDatapointResponseDTO>(endpoint, responseDto);
        return response.data;
    } catch (error) {
        // The detailed error logging here is good and remains unchanged.
        console.error(`API Error saving/updating datapoint response:`, error);
        throw error;
    }
}

export async function fetchAllCsrdDatapointResponsesForProject(
    projectId: number | string,
    reportingYear?: number
): Promise<CsrdDatapointResponseDTO[]> {
    try {
        const params: { [key: string]: any } = { projectId };
        if (reportingYear) {
            params.reportingYear = reportingYear;
        }
        const response = await api.get<CsrdDatapointResponseDTO[]>(`/csrd/datapoint-responses/project/${projectId}`, { params });
        return response.data;
    } catch (error) {
        console.error(`API Error fetching all datapoint responses for project ${projectId}:`, error);
        throw error;
    }
}

export async function getCsrdSubtopicGeneratedText(
    projectId: number | string,
    subtopicId: number | string, // This is the Long ID of the CsrdSubtopic entity
    reportingYear?: number
): Promise<CsrdSubtopicGeneratedTextDTO> {
    try {
        const params: { [key: string]: any } = { };
        if (reportingYear) {
            params.reportingYear = reportingYear;
        }
        const response = await api.get<CsrdSubtopicGeneratedTextDTO>(
            `/csrd/subtopic-texts/project/${projectId}/subtopic/${subtopicId}`, { params }
        );
        return response.data;
    } catch (error) {
        console.error(`API Error fetching generated text for subtopic ${subtopicId}:`, error);
        throw error;
    }
}

export async function triggerCsrdSubtopicTextGeneration(
    projectId: number | string,
    subtopicId: number | string, // This is the Long ID of the CsrdSubtopic entity
    reportingYear?: number
): Promise<CsrdSubtopicGeneratedTextDTO> {
    try {
        const params: { [key: string]: any } = { };
        if (reportingYear) {
            params.reportingYear = reportingYear;
        }
        const response = await api.post<CsrdSubtopicGeneratedTextDTO>(
            `/csrd/subtopic-texts/generate/project/${projectId}/subtopic/${subtopicId}`, null, { params } // POST with empty body
        );
        return response.data;
    } catch (error) {
        console.error(`API Error triggering text generation for subtopic ${subtopicId}:`, error);
        throw error;
    }
}



/**
 * Fetches a list of generated CSRD reports (SIMPLIFIED/MOCK FOR NOW).
 * In a real scenario, this would hit a backend endpoint that lists CSRD_GENERATOR jobs.
 */
export async function getReportsList(): Promise<ReportListItem[]> {
    console.log(`API: Fetching reports list (simplified mock)`);
    await new Promise(resolve => setTimeout(resolve, 5)); // Simulate delay

    // TODO: Replace with an actual API call to list CSRD_GENERATOR jobs.
    // For now, return one or two known job IDs that you can test the viewer with.
    // Make sure these job_ids exist and have data in your backend.
    const mockReports: ReportListItem[] = [
        {
            job_id: "job-7e5798db-d95d-46f7-b632-ab03f171fa54", // USE AN ACTUAL JOB ID FROM YOUR LOGS
            display_name: "Test Report",
            status: JobStatusApi.UNKNOWN, // Or COMPLETED, based on your test job
            generated_at: new Date().toISOString(),
        },
        // Add another if you have a successfully completed job ID for testing
        // {
        //     job_id: "YOUR_SUCCESSFUL_JOB_ID_HERE",
        //     display_name: "Successful Test Report",
        //     status: JobStatusApi.COMPLETED,
        //     generated_at: new Date(Date.now() - 86400000).toISOString(),
        // },
    ];
    if (mockReports[0].job_id === "job-1087cc13-8e29-4144-ad43-a444c9893df9" && mockReports[0].status === JobStatusApi.FAILED) {
        console.warn("Mock data includes a FAILED job. Ensure this job ID has corresponding data or the viewer might show errors/empty state for it.");
    }

    return Promise.resolve(mockReports);
}
export interface CSRDGeneratorRequestPayload {
    company_profile_id: string;
    reporting_year?: number | null;
    output_format?: 'docx' | 'pdf' | 'txt' | null;
    max_tokens_per_disclosure?: number | null;
    chat_model_alias?: string | null;
}
// --- API Functions ---
const API_BASE_URL = `${API_PYTHON_AI_URL}/api/v1`;


/**
 * Fetches the detailed data for a specific CSRD report generation job.
 * This calls the /reports/{job_id}/details endpoint defined in the backend.
 */
export async function getReportDetails(jobId: string): Promise<ReportDetails> {
    // This path MUST match your FastAPI backend endpoint structure for get_csrd_report_details
    const endpointPath = `/reports/${jobId}/details`; // Relative to API_BASE_URL

    // Create a new Axios instance for this specific request
    const apiClient = axios.create({
        baseURL: API_BASE_URL,
        // You can add other default configurations here if needed, e.g., headers
        // headers: { 'Content-Type': 'application/json' },
    });

    try {
        console.log(`API: Fetching report details for jobId: ${jobId} from ${API_BASE_URL}${endpointPath}`);
        const { data } = await apiClient.get<ReportDetails>(endpointPath); // Expects ReportDetails type
        return data;
    } catch (error: any) {
        console.error(`API Error fetching report details for job ${jobId}:`, error);
        let errorMessage = `Failed to fetch report details for job ${jobId}.`;
        if (axios.isAxiosError(error)) { // Type guard for AxiosError
            const axiosError = error as AxiosError<any>; // Cast to AxiosError with any data type for detail
            if (axiosError.response) {
                errorMessage += ` Status: ${axiosError.response.status}, Message: ${JSON.stringify(axiosError.response.data?.detail || axiosError.response.data)}`;
            } else if (axiosError.request) {
                errorMessage += ` No response received from server.`;
            } else {
                errorMessage += ` Error: ${axiosError.message}`;
            }
        } else {
            errorMessage += ` Error: ${error.message || String(error)}`;
        }
        throw new Error(errorMessage);
    }
}

export async function createCsrdReportJob(payload: CSRDGeneratorRequestPayload): Promise<any> {
    // Assuming this endpoint is also relative to API_BASE_URL
    // If it's under /api/v1, adjust API_BASE_URL or endpointPath
    const endpointPath = '/jobs/generate_csrd'; // Relative to API_BASE_URL

    // Create a new Axios instance for this specific request
    const apiClient = axios.create({
        baseURL: API_BASE_URL,
        headers: { 'Content-Type': 'application/json' }, // POST requests often need this
    });

    try {
        console.log(`API: Creating CSRD report job with payload:`, payload, `to ${API_BASE_URL}${endpointPath}`);
        const { data } = await apiClient.post<any>(endpointPath, payload);
        return data;
    } catch (error: any) {
        console.error("API Error creating CSRD report job:", error);
        // You can add more detailed error handling similar to getReportDetails if desired
        if (axios.isAxiosError(error)) {
            const axiosError = error as AxiosError<any>;
            let detailMessage = "An unknown error occurred.";
            if (axiosError.response) {
                detailMessage = `Status: ${axiosError.response.status}, Data: ${JSON.stringify(axiosError.response.data?.detail || axiosError.response.data)}`;
            } else if (axiosError.request) {
                detailMessage = "No response received from server.";
            } else {
                detailMessage = axiosError.message;
            }
            throw new Error(`Failed to create CSRD report job: ${detailMessage}`);
        }
        throw new Error(`Failed to create CSRD report job: ${error.message || String(error)}`);
    }
}




