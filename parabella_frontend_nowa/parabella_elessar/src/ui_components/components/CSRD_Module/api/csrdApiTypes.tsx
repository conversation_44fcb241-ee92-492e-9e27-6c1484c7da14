// src/modules/csrd/api/csrdApiTypes.tsx

export interface CsrdFieldOption {
    id: number;
    optionValue: string;
    child_field_id:number;
    parent_field_id:number;
}

export interface CsrdConditionalField {
    id: number;
    parentOptionValue: string;
    parentField: CsrdField;
    childField: CsrdField;
}

export interface CsrdField {
    id: number;
    fieldType: string;
    label: string | null;
    options: CsrdFieldOption[];
    sections: CsrdSubtopicSection;
}

export interface CsrdSubtopicSection {
    id: number;
    sectionId: string;
    sectionTitle: string;
    fields: CsrdField[];
}

export interface ProcessedDocumentChunkDTO {
    id: number;
    projectId: number;
    uploadedByUserId?: string | null;
    documentName?: string | null;
    chunkIndex?: number | null;
    chunkText?: string | null;
    chunkSummary?: string | null;
    disclosureRequirement?: string | null;
    sourceId?: string | null;
    dataSourceIds?: any[] | null;
    matchScore?: number | null;
    metadata?: any | null;
    createdAt?: string | null;
}

interface AutofillDatapointRequestChunkDTO {
    documentName?: string | null;
    chunkIndex?: number | null;
    chunkText?: string | null;
    chunkSummary?: string | null;
}

export interface AutofillDatapointRequestDTO {
    projectId: number;
    companyName: string;
    companyIndustry: string;
    datapointId: number;
    datapointLabel: string;
    disclosureRequirement: string;
    sourceId: string;
    datapointDataType: string;
    documentChunks?: AutofillDatapointRequestChunkDTO[];
}

export interface AutofillDatapointResponseDTO {
    datapointId: number;
    generatedText: string;
}

export interface CoverageDatapointDto {
    esrsDatapointId: number;
    sourceId: string | null;
    esrsStandard: string;
    disclosureRequirement: string;
    dataPointName: string;
    covered: boolean;
    coveringChunkIds: number[];
    coveringDocumentNames: string[];
}

export interface CategoryCoverageStatsDto {
    totalDatapoints: number;
    coveredDatapoints: number;
    coveragePercentage: number;
}

export interface CoverageAnalysisResultDto {
    projectId: number;
    totalEsrsDatapoints: number;
    totalCoveredDatapoints: number;
    totalUncoveredDatapoints: number;
    overallCoveragePercentage: number;
    coverageByStandard: Record<string, CategoryCoverageStatsDto>;
    datapoints: CoverageDatapointDto[];
}

// --- NEW DTOs for Persistent Data Saving and Generated Text ---

export type DataResponseSource = 'USER_DOC' | 'AI_OPEN_DATA' | 'MANUAL';

export interface CsrdDatapointResponseDTO {
    id?: number; // Optional for creation, required for updates
    esrsDatapointId: number;
    csrdProjectId: number;
    dataResponse: string | null;
    dataResponseSource: DataResponseSource | null;
    reportingYear?: number;
    createdAt?: string;
    updatedAt?: string;
}

export type GenerationStatus = 'PENDING' | 'COMPLETED' | 'FAILED';

export interface CsrdSubtopicGeneratedTextDTO {
    id?: number;
    csrdSubtopicId: number; // This is the Long ID of the CsrdSubtopic entity
    csrdProjectId: number;
    generatedText: string | null;
    reportingYear?: number;
    generationStatus?: GenerationStatus;
    createdAt?: string;
    updatedAt?: string;
}