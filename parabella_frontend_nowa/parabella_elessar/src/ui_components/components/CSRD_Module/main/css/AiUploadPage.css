/* src/modules/csrd/components/css/AiUploadPage.css */

/* --- General <PERSON> Styles --- */
.ai-upload-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #f8f9fc;
    color: #343a40;
}

.ai-upload-page h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}
.ai-upload-page h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
}
.ai-upload-page p, .ai-upload-page .text-muted {
    color: #6b7280;
    line-height: 1.6;
}

/* --- Dropzone Enhancements --- */
.dropzone-area.modern-dropzone {
    border-radius: 0.75rem;
    border: 2px dashed #d1d5db;
    background-color: #ffffff;
    padding: 2.5rem;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 1px 0 rgba(0, 0, 0, 0.02);
}
.dropzone-area.modern-dropzone:hover,
.dropzone-area.modern-dropzone.active {
    border-color: #3b82f6; /* Blue-500 */
    background-color: #eff6ff; /* Blue-50 */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05), 0 2px 4px 0 rgba(0, 0, 0, 0.03);
}
.dropzone-area.modern-dropzone.accept { border-color: #10b981; background-color: #f0fdf4; }
.dropzone-area.modern-dropzone.reject { border-color: #ef4444; background-color: #fef2f2; }
.dropzone-area.modern-dropzone.disabled-dropzone,
.dropzone-area.modern-dropzone.disabled-dropzone:hover {
    border-color: #e5e7eb;
    background-color: #f3f4f6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}
.dropzone-icon-bg {
    width: 64px;
    height: 64px;
    background: rgba(59, 130, 246, 0.08);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    transition: all 0.25s ease;
}
.dropzone-icon-bg svg { color: #3b82f6; }
.dropzone-area.modern-dropzone.disabled-dropzone .dropzone-icon-bg svg { color: #9ca3af !important; }

.dropzone-area.modern-dropzone h5 { font-size: 1.125rem; color: #1f2937; font-weight: 500; }
.dropzone-area.modern-dropzone p.text-muted { font-size: 0.9rem; color: #4b5563; }

@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
.spinning { animation: spin 1.2s linear infinite; }

@keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
.pulsing { animation: pulse 1.8s infinite cubic-bezier(0.4, 0, 0.2, 1); }

/* --- Upload List Redesign --- */
.uploads-list-container .upload-list-item {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: none;
    transition: all 0.2s ease-in-out;
}
.uploads-list-container .upload-list-item:hover {
    box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -2px rgba(0,0,0,0.1);
    border-color: #d1d5db;
    transform: translateY(-2px);
}
.upload-item-status .spinner-border { width: 1.1rem; height: 1.1rem; border-width: 0.18em;}
.upload-item-icon { position: relative; }
.upload-item-icon .file-ext-badge {
    position: absolute;
    bottom: -2px;
    right: -5px;
    font-size: 0.6rem;
    font-weight: 700;
    color: white;
    background-color: #dc3545;
    padding: 0.1rem 0.3rem;
    border-radius: 0.25rem;
}
.upload-item-details .file-name { font-weight: 500; color: #111827; font-size: 0.9rem; }
.upload-item-details .file-meta { font-size: 0.8rem; color: #6b7280; }
.upload-item-details .progress { height: 6px; border-radius: 6px; }
.esg-badge {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
    font-weight: 500;
    border-radius: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    line-height: 1;
}
.action-icon-btn {
    color: #6b7280;
    padding: 0.375rem;
    border-radius: 50%;
    transition: color 0.2s ease, background-color 0.2s ease;
    line-height: 1;
}
.action-icon-btn svg { display: block; }
.action-icon-btn:hover { background-color: #e5e7eb; }
.action-icon-btn.text-primary:hover { color: #2563eb !important; background-color: #dbeafe !important; }
.action-icon-btn.text-warning:hover { color: #d97706 !important; background-color: #fef3c7 !important; }
.action-icon-btn.text-danger:hover { color: #dc2626 !important; background-color: #fee2e2 !important; }
.action-icon-btn[disabled] { cursor: not-allowed; color: #d1d5db !important; background-color: transparent !important; opacity: 0.6 !important; }

/* --- Modal Enhancements --- */
.modal-dialog.wide-modal { max-width: 95vw !important; width: 95vw !important; }
.modal-content { border-radius: 0.75rem; border: none; overflow: hidden; }
.modal-header { background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; padding: 1rem 1.5rem; }
.modal-header .modal-title { font-size: 1.15rem; font-weight: 600; color: #111827; }
.modal-header .modal-title .fw-normal { font-weight: 400 !important; color: #4b5563; }
.pdf-pane { height: 85vh; background: #e9ecef; position: relative; }
.data-pane { height: 85vh; overflow-y: auto; background: #F9FAFB; }
.modal-footer { background-color: #f9fafb; border-top: 1px solid #e5e7eb; }

/* Modal Data Cards */
.modern-card { border-radius: 0.5rem; border: 1px solid #e5e7eb; background-color: #ffffff; }
.modern-card .card-header { background-color: #f9fafb; border-bottom: 1px solid #e5e7eb; padding: 0.75rem 1.25rem; }
.modern-card .card-header h6 { font-size: 0.95rem; font-weight: 600; color: #1f2937; }
.modern-card .card-header .badge { background-color: #e5e7eb !important; color: #374151 !important; }

/* Modal Data Point List Items */
.data-point-list .list-group-item { border-bottom: 1px solid #f3f4f6; }
.data-point-list .list-group-item:last-child { border-bottom: none; }
.data-point-list .list-group-item:hover { background-color: #f9fafb; }

.data-point-list .fw-medium { color: #111827; font-weight: 500 !important; font-size: 0.9rem; }
.data-point-list .data-value { font-size: 1.375rem !important; color: #1f2937; font-weight: 600; line-height: 1.2; }
.data-point-list .data-value + .text-muted { font-size: 0.8rem; color: #6b7280 !important; position: relative; top: -0.1em; }

.data-point-list .small { color: #4b5563 !important; font-size: 0.75rem; font-weight: 500; }
.data-point-list .text-dark { color: #374151 !important; font-size: 0.8rem; }
.data-point-list .badge.font-monospace { padding: 0.25em 0.5em; font-size: 0.75rem; }
.data-point-list .badge[class*="-subtle"] { border: 1px solid transparent; font-weight: 500; }

.tooltip-inner { background-color: #1f2937; color: #f9fafb; padding: 0.4rem 0.8rem; border-radius: 0.25rem; font-size: 0.8rem;}
.tooltip.bs-tooltip-top .tooltip-arrow::before { border-top-color: #1f2937; }
.tooltip.bs-tooltip-bottom .tooltip-arrow::before { border-bottom-color: #1f2937; }
.tooltip.bs-tooltip-start .tooltip-arrow::before { border-left-color: #1f2937; }
.tooltip.bs-tooltip-end .tooltip-arrow::before { border-right-color: #1f2937; }

/* Ensure correct pane heights and layout */
.pdf-pane.border-end { border-right: 1px solid #dee2e6 !important; }
.modal-body.p-0 { padding: 0 !important; }