/* src/modules/csrd/components/css/CsrdCoverageAnalysisPage.css */

.coverage-analysis-page h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1f2937;
}

.coverage-analysis-page p, .coverage-analysis-page .text-muted {
    color: #6b7280;
}

.progress-bar-modern {
    height: 12px;
    border-radius: 12px;
    background-color: #e9ecef;
}

.progress-bar-modern .progress-bar {
    border-radius: 12px;
}
.progress-bar-modern .progress-bar-striped {
    background-size: 1rem 1rem;
}


.stat-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: none;
    transition: all 0.2s ease-in-out;
}
.stat-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -2px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}
.stat-card .card-body {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
}
.stat-card .stat-content {
    display: flex;
    flex-direction: column;
}
.stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    line-height: 1.2;
}
.stat-card .stat-label {
    font-size: 0.8rem;
    color: #6b7280;
}


.modern-accordion .accordion-item {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem !important;
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0,0,0,0.03);
}
.modern-accordion .accordion-header .accordion-button {
    border-radius: calc(0.5rem - 1px) !important;
    font-weight: 500;
}
.modern-accordion .accordion-button:not(.collapsed) {
    color: #3b82f6; /* Blue-600 */
    background-color: #eff6ff; /* Blue-50 */
    box-shadow: inset 0 -1px 0 rgba(0,0,0,.125);
}
.modern-accordion .accordion-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.2);
}

.category-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    border-left-width: 4px !important;
    box-shadow: none;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
}
.category-card:hover {
    box-shadow: 0 2px 4px -1px rgba(0,0,0,0.06), 0 1px 2px -1px rgba(0,0,0,0.06);
    transform: translateY(-1px);
}
.category-card .card-body { padding: 1rem; }
.category-card .card-title { font-size: 0.95rem; font-weight: 600; }
.category-card .card-text { font-size: 0.85rem; }


.coverage-table thead th {
    font-weight: 500;
    font-size: 0.8rem;
    text-transform: none;
    letter-spacing: 0.025em;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    color: #4b5563;
    padding: 0.75rem 1rem;
}
.coverage-table tbody tr {
    transition: background-color 0.15s ease-in-out;
}
.coverage-table tbody tr:hover {
    background-color: #f9fafb;
}
.coverage-table td {
    vertical-align: middle;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
    border-top: 1px solid #f3f4f6;
}
.standard-badge {
    font-size: 0.7rem;
    padding: 0.35em 0.7em;
    font-weight: 600;
}

.filter-input-group, .filter-select {
    max-width: 220px;
    font-size: 0.85rem;
}
.filter-input-group .form-control,
.filter-input-group .input-group-text,
.filter-select {
    background-color: #fff;
    border-color: #d1d5db;
}

.info-icon-button { color: #6b7280; }
.info-icon-button:hover { color: #3b82f6; }