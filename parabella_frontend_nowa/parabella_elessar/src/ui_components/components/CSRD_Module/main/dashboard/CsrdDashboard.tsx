import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    Container, Row, Col, Card, Button, Modal, Spinner, Alert, Nav, ListGroup,
    Badge as BsBadge, OverlayTrigger, Tooltip, Placeholder, ProgressBar, Form,
    InputGroup, Tab, Tabs
} from 'react-bootstrap';
import {
    ChevronDown, ChevronUp, Compass, Eye, Zap, Users as UsersIcon, Scale, Leaf,
    AlertCircle, Info, Building, FileText as FileTextIconLucideReal, DollarSign, // Renamed
    Grid as SubtopicGridIcon, BarChart3, LayoutDashboard, ClipboardCopy, Lightbulb,
    AlertOctagon, FileCode2, CheckCircle, Sparkles, TrendingUp, CheckSquare, Package,
    Search, Filter as FilterIcon, Link as LinkIcon, Target,
    Activity, BookOpen, Wand2
} from 'lucide-react';
import { useNavigate } from "react-router-dom";

// Use the specific Lucide icon for FileText, not the alias
const FileTextIconLucide = FileTextIconLucideReal;


import { useCsrdProjectContext, getErrorMessage } from "../../context/CsrdProjectProvider.tsx";
import {
    autofillDatapointWithAI,
    fetchCsrdData as newFetchCsrdData,
    fetchRelevantChunks,
    fetchCoverageAnalysis,
    saveOrUpdateCsrdDatapointResponse,
    fetchAllCsrdDatapointResponsesForProject,
    triggerCsrdSubtopicTextGeneration,
    getCsrdSubtopicGeneratedText
} from '../../api/csrdApi.ts';
import { AutofillDatapointRequestDTO, ProcessedDocumentChunkDTO, CoverageAnalysisResultDto, CoverageDatapointDto, CsrdDatapointResponseDTO, DataResponseSource, CsrdSubtopicGeneratedTextDTO } from '../../api/csrdApiTypes.tsx';
import SubtopicDetailModal from "./SubtopicDetailModal.tsx";
import '../css/Dashboard.css';
import { AiInsight, CsrdSubtopic, CsrdTopic, EsrsDatapoint } from '../modules/types.ts';


type CategoryType = 'General' | 'Environmental' | 'Social' | 'Governance';
interface ESGCategory {
    category: CategoryType;
    icon: React.ReactNode;
}
const esgCategories: ESGCategory[] = [
    { category: 'General', icon: <Compass size={18} /> },
    { category: 'Environmental', icon: <Leaf size={18} /> },
    { category: 'Social', icon: <UsersIcon size={18} /> },
    { category: 'Governance', icon: <Scale size={18} /> },
];

export interface EnhancedEsrsDatapoint extends EsrsDatapoint {
    coverageStatus?: 'covered' | 'uncovered' | 'not_applicable';
    coveringDocumentNames?: string[];
}
export interface EnhancedCsrdSubtopic extends CsrdSubtopic {
    datapoints: EnhancedEsrsDatapoint[];
    coverageStats?: {
        required: number;
        covered: number;
        percentage: number;
    }
}
export interface EnhancedCsrdTopic extends CsrdTopic {
    subtopics: EnhancedCsrdSubtopic[];
    coverageStats?: {
        required: number;
        covered: number;
        percentage: number;
    }
}

const esrsCodeSort = (a: string, b: string): number => {
    const regex = /([A-Z]+(?:_[A-Z\d]+)*)-?(\d+)(?:[.-]?([A-Z\d]+))?/;
    const parseCode = (code: string = '') => {
        const upperCode = code.toUpperCase();
        const match = upperCode.match(regex);
        if (!match) {
            const parts = upperCode.split(/[-_ ]/);
            const numPart = parts.find(p => /^\d+$/.test(p));
            return {
                prefix: parts[0] || upperCode,
                num: numPart ? parseInt(numPart, 10) : Infinity,
                suffix: parts.slice(1).filter(p => !/^\d+$/.test(p)).join('') || ''
            };
        }
        return {
            prefix: match[1],
            num: parseInt(match[2], 10),
            suffix: match[3] || ''
        };
    };
    const partsA = parseCode(a);
    const partsB = parseCode(b);
    if (partsA.prefix < partsB.prefix) return -1;
    if (partsA.prefix > partsB.prefix) return 1;
    if (partsA.num < partsB.num) return -1;
    if (partsA.num > partsB.num) return 1;
    if (partsA.suffix < partsB.suffix) return -1;
    if (partsA.suffix > partsB.suffix) return 1;
    return 0;
};
const sortTopics = (topics: EnhancedCsrdTopic[]): EnhancedCsrdTopic[] => {
    return [...topics].sort((a, b) => esrsCodeSort(a.topicCode || '', b.topicCode || ''));
};
const sortSubtopics = (subtopics: EnhancedCsrdSubtopic[]): EnhancedCsrdSubtopic[] => {
    return [...subtopics].sort((a, b) => esrsCodeSort(a.csrd_subtopic_id || '', b.csrd_subtopic_id || ''));
};
function determineCategoryFromCode(topicCodeValue: string): CategoryType {
    const upperCode = (topicCodeValue || '').toUpperCase();
    if (upperCode.startsWith('ESRS_2') || upperCode.startsWith('ESRS 2')) return 'General';
    if (upperCode.startsWith('ESRS_E') || upperCode.startsWith('ESRS E')) return 'Environmental';
    if (upperCode.startsWith('ESRS_S') || upperCode.startsWith('ESRS S')) return 'Social';
    if (upperCode.startsWith('ESRS_G') || upperCode.startsWith('ESRS G')) return 'Governance';
    return 'General';
}
function groupByCategory(topics: EnhancedCsrdTopic[]): Record<CategoryType, EnhancedCsrdTopic[]> {
    const grouped: Record<CategoryType, EnhancedCsrdTopic[]> = { General: [], Environmental: [], Social: [], Governance: [] };
    topics.forEach((t) => {
        if (t && t.topicCode) {
            const cat = determineCategoryFromCode(t.topicCode);
            grouped[cat].push(t);
        } else {
            console.warn("Skipping topic due to missing topicCode or undefined topic:", t);
        }
    });
    for (const key in grouped) {
        grouped[key as CategoryType] = sortTopics(grouped[key as CategoryType]);
    }
    return grouped;
}

const normalizeDataType = (rawDataType: string | null | undefined): string => {
    const lowerType = rawDataType?.toLowerCase().trim();
    if (!lowerType || lowerType === 'nan' || lowerType === '') {
        return 'narrative';
    }
    return lowerType;
};

const parseNumericAIResponse = (text: string, dataType: string): string => {
    if (!text) return '';
    const numericMatch = text.match(/-?\d+(\.\d+)?/);
    if (numericMatch) {
        const parsed = parseFloat(numericMatch[0]);
        if (!isNaN(parsed)) {
            if (dataType === 'integer' || dataType === 'gyear') {
                return Math.round(parsed).toString();
            }
            return parsed.toString();
        }
    }
    if (text.toLowerCase() === 'nan') return 'NaN';
    console.warn(`Could not parse numeric value from AI response "${text}" for dataType "${dataType}". Returning empty.`);
    return '';
};

const CSRDDashboard: React.FC = () => {
    const {
        companyInfo, currentProject, isLoading: contextIsLoading,
        error: contextError, clearError
    } = useCsrdProjectContext();
    const navigate = useNavigate();
    const [topics, setTopics] = useState<EnhancedCsrdTopic[]>([]);
    const [groupedTopics, setGroupedTopics] = useState<Record<CategoryType, EnhancedCsrdTopic[]>>({ General: [], Environmental: [], Social: [], Governance: [] });
    const [isLoadingTopics, setIsLoadingTopics] = useState<boolean>(true);
    const [errorTopics, setErrorTopics] = useState<string | null>(null);
    const [coverageData, setCoverageData] = useState<CoverageAnalysisResultDto | null>(null);
    const [isLoadingCoverage, setIsLoadingCoverage] = useState<boolean>(true);
    const [errorCoverage, setErrorCoverage] = useState<string | null>(null);
    const isLoading = contextIsLoading || isLoadingTopics || isLoadingCoverage;
    const globalError = contextError || errorTopics || errorCoverage;
    const [expandedCategory, setExpandedCategory] = useState<CategoryType | null>('Environmental');
    const [selectedTopicId, setSelectedTopicId] = useState<string | number | null>(null);
    const [showSubtopicModal, setShowSubtopicModal] = useState(false);
    const [selectedSubtopic, setSelectedSubtopic] = useState<EnhancedCsrdSubtopic | null>(null);
    const [loadingModalContent, setLoadingModalContent] = useState<boolean>(false);
    const [errorModalContent, setErrorModalContent] = useState<string | null>(null);
    const [focusedDatapointId, setFocusedDatapointId] = useState<number | null>(null);
    const [isAutofillingDpId, setIsAutofillingDpId] = useState<number | null>(null);
    const [aiInsightsForFocusedDP, setAiInsightsForFocusedDP] = useState<AiInsight[]>([]);
    const [relevantDocumentChunks, setRelevantDocumentChunks] = useState<ProcessedDocumentChunkDTO[]>([]);
    const [loadingChunks, setLoadingChunks] = useState<boolean>(false);
    const [errorChunks, setErrorChunks] = useState<string | null>(null);
    const [autoFillingTopicIds, setAutoFillingTopicIds] = useState<Set<number | string>>(new Set());
    const [autoFillingSubtopicId, setAutoFillingSubtopicId] = useState<string | number | null>(null);

    const [dashboardStats, setDashboardStats] = useState({
        totalTopics: 0,
        requiredDatapoints: 0,
        completedDatapoints: 0,
        totalPossibleDatapoints: 0,
        coveredDatapoints: 0,
        overallProgress: 0,
        overallCoverage: 0,
        aiAssistsUsed: 0,
    });

    useEffect(() => {
        if (!contextIsLoading) {
            if (!currentProject) { navigate(`${import.meta.env.BASE_URL}csrd/create`); }
            else if (!companyInfo || !companyInfo.companyName) { navigate(`${import.meta.env.BASE_URL}csrd/company-info`); }
        }
    }, [currentProject, companyInfo, contextIsLoading, navigate]);

    useEffect(() => {
        if (currentProject?.id && companyInfo?.companyName) {
            const loadAllData = async () => {
                setIsLoadingTopics(true); setIsLoadingCoverage(true);
                setErrorTopics(null); setErrorCoverage(null);
                setTopics([]); setCoverageData(null);

                try {
                    const results = await Promise.allSettled([
                        newFetchCsrdData(),
                        fetchCoverageAnalysis(currentProject.id),
                        fetchAllCsrdDatapointResponsesForProject(currentProject.id)
                    ]);

                    let topicsResult: CsrdTopic[] = [];
                    let coverageResult: CoverageAnalysisResultDto | null = null;
                    let responsesResult: CsrdDatapointResponseDTO[] = [];

                    if (results[0].status === 'fulfilled') {
                        topicsResult = results[0].value;
                    } else {
                        setErrorTopics(`Failed to load CSRD topics: ${getErrorMessage(results[0].reason)}`);
                    }

                    if (results[1].status === 'fulfilled') {
                        coverageResult = results[1].value;
                        setCoverageData(coverageResult);
                    } else {
                        setErrorCoverage(`Failed to load coverage analysis: ${getErrorMessage(results[1].reason)}`);
                    }

                    if (results[2].status === 'fulfilled') {
                        responsesResult = results[2].value;
                    } else {
                        console.warn(`Could not load saved datapoint responses: ${getErrorMessage(results[2].reason)}`);
                    }

                    if (topicsResult.length > 0) {
                        const coverageMap = coverageResult
                            ? new Map<string, CoverageDatapointDto>(coverageResult.datapoints.map(dp => [dp.sourceId!, dp]))
                            : new Map<string, CoverageDatapointDto>();

                        const responseMap = new Map<number, CsrdDatapointResponseDTO>(responsesResult.map(r => [r.esrsDatapointId, r]));

                        const enhancedTopics: EnhancedCsrdTopic[] = topicsResult.map(topic => {
                            let topicRequired = 0; let topicCovered = 0;
                            const enhancedSubtopics: EnhancedCsrdSubtopic[] = topic.subtopics?.map(sub => {
                                let subtopicRequired = 0; let subtopicCovered = 0;
                                const enhancedDatapoints: EnhancedEsrsDatapoint[] = sub.datapoints?.map(dp => {
                                    const coverageInfo = dp.sourceId ? coverageMap.get(dp.sourceId) : undefined;
                                    const savedResponse = responseMap.get(dp.id);
                                    let coverageStatus: EnhancedEsrsDatapoint['coverageStatus'] = 'not_applicable';

                                    if (coverageInfo) {
                                        topicRequired++; subtopicRequired++;
                                        if (coverageInfo.covered) {
                                            coverageStatus = 'covered'; topicCovered++; subtopicCovered++;
                                        } else { coverageStatus = 'uncovered'; }
                                    } else if (dp.sourceId && coverageResult) {
                                        coverageStatus = 'uncovered'; topicRequired++; subtopicRequired++;
                                    }

                                    return {
                                        ...dp,
                                        dataResponse: savedResponse?.dataResponse || null,
                                        dataResponseSource: savedResponse?.dataResponseSource || null,
                                        coverageStatus,
                                        coveringDocumentNames: coverageInfo?.covered ? coverageInfo.coveringDocumentNames : []
                                    };
                                }) || [];
                                enhancedDatapoints.sort((a, b) => esrsCodeSort(a.sourceId || '', b.sourceId || ''));
                                return { ...sub, datapoints: enhancedDatapoints, coverageStats: { required: subtopicRequired, covered: subtopicCovered, percentage: subtopicRequired > 0 ? Math.round((subtopicCovered / subtopicRequired) * 100) : (subtopicRequired === 0 && coverageResult ? 100 : 0) } };
                            }) || [];
                            enhancedSubtopics.sort((a, b) => esrsCodeSort(a.csrd_subtopic_id || '', b.csrd_subtopic_id || ''));
                            return { ...topic, subtopics: enhancedSubtopics, coverageStats: { required: topicRequired, covered: topicCovered, percentage: topicRequired > 0 ? Math.round((topicCovered / topicRequired) * 100) : (topicRequired === 0 && coverageResult ? 100: 0) } };
                        });

                        const finalGroupedTopics = groupByCategory(enhancedTopics);
                        setTopics(enhancedTopics);
                        setGroupedTopics(finalGroupedTopics);

                        let currentExpandedCatToSet = expandedCategory;
                        let currentSelectedTopicIdToSet = selectedTopicId;
                        if (!currentExpandedCatToSet || !finalGroupedTopics[currentExpandedCatToSet] || finalGroupedTopics[currentExpandedCatToSet].length === 0) {
                            currentExpandedCatToSet = esgCategories.find(cat => finalGroupedTopics[cat.category]?.length > 0)?.category || null;
                        }
                        if (currentExpandedCatToSet) {
                            const categoryTopics = finalGroupedTopics[currentExpandedCatToSet];
                            if (categoryTopics && categoryTopics.length > 0) {
                                if (!currentSelectedTopicIdToSet || !categoryTopics.find(t => t.id === currentSelectedTopicIdToSet)) {
                                    currentSelectedTopicIdToSet = categoryTopics[0].id;
                                }
                            } else { currentSelectedTopicIdToSet = null; }
                        } else { currentExpandedCatToSet = null; currentSelectedTopicIdToSet = null; }
                        setExpandedCategory(currentExpandedCatToSet);
                        setSelectedTopicId(currentSelectedTopicIdToSet);
                    }

                } catch (err: any) {
                    const msg = `Failed to process dashboard data: ${getErrorMessage(err)}`;
                    setErrorTopics(msg);
                } finally {
                    setIsLoadingTopics(false); setIsLoadingCoverage(false);
                }
            };
            loadAllData();
        } else {
            setIsLoadingTopics(false); setIsLoadingCoverage(false);
            setTopics([]); setCoverageData(null);
            setGroupedTopics({ General: [], Environmental: [], Social: [], Governance: [] });
            setSelectedTopicId(null);
        }
    }, [currentProject?.id, companyInfo?.companyName]);


    useEffect(() => {
        let totalDPs = 0;
        let completedDPs = 0;
        topics.forEach(topic => {
            topic.subtopics?.forEach(sub => {
                totalDPs += sub.datapoints?.length || 0;
                completedDPs += sub.datapoints?.filter(dp => dp.dataResponse && dp.dataResponse.trim() !== '').length || 0;
            });
        });

        setDashboardStats({
            totalTopics: topics.length,
            requiredDatapoints: coverageData?.totalEsrsDatapoints || 0,
            completedDatapoints: completedDPs,
            totalPossibleDatapoints: totalDPs,
            coveredDatapoints: coverageData?.totalCoveredDatapoints || 0,
            overallProgress: totalDPs > 0 ? Math.round((completedDPs / totalDPs) * 100) : 0,
            overallCoverage: coverageData?.overallCoveragePercentage || 0,
            aiAssistsUsed: 0,
        });
    }, [topics, coverageData]);

    const handleClearLocalErrors = useCallback(() => {
        setErrorTopics(null); setErrorCoverage(null); setErrorModalContent(null); setErrorChunks(null); clearError?.();
    }, [clearError]);

    const handleSelectCategory = (cat: CategoryType) => {
        setExpandedCategory(prevCat => {
            const newExpandedCat = prevCat === cat ? null : cat;
            if (newExpandedCat) {
                const topicsInNewCategory = groupedTopics[newExpandedCat];
                if (!selectedTopicId || !topicsInNewCategory?.some(t => t.id === selectedTopicId)) {
                    setSelectedTopicId(topicsInNewCategory?.[0]?.id || null);
                }
            } else {
                if(prevCat === expandedCategory) setSelectedTopicId(null);
            }
            return newExpandedCat;
        });
    };

    const handleSelectTopic = (topicId: string | number) => { setSelectedTopicId(topicId); };

    const handleOpenSubtopic = (sub: EnhancedCsrdSubtopic) => {
        setLoadingModalContent(true); setErrorModalContent(null);
        setSelectedSubtopic(sub);
        setFocusedDatapointId(null);
        setAiInsightsForFocusedDP([]);
        setRelevantDocumentChunks([]);
        setErrorChunks(null);
        setShowSubtopicModal(true);
        setLoadingModalContent(false);
        if (sub.datapoints && sub.datapoints.length > 0) {
            handleDatapointFocus(sub.datapoints[0].id);
        }
    };

    const handleCloseSubtopicModal = () => {
        setSelectedSubtopic(null); setShowSubtopicModal(false);
        setErrorModalContent(null); setFocusedDatapointId(null);
        setAiInsightsForFocusedDP([]);
        setRelevantDocumentChunks([]);
        setErrorChunks(null);
    };

    const handleDatapointUpdate = useCallback(async (
        datapointId: number,
        updatesOrFieldName: Partial<Pick<EnhancedEsrsDatapoint, 'dataResponse' | 'dataResponseSource'>> | string,
        value?: any
    ) => {
        if (!currentProject?.id) return;

        const updates: Partial<Pick<EnhancedEsrsDatapoint, 'dataResponse' | 'dataResponseSource'>> =
            typeof updatesOrFieldName === 'object' && updatesOrFieldName !== null
                ? updatesOrFieldName
                : { [updatesOrFieldName as string]: value };

        const performOptimisticUpdate = (datapoint: EnhancedEsrsDatapoint): EnhancedEsrsDatapoint => {
            const newDatapointState = { ...datapoint, ...updates };
            if ('dataResponse' in updates && !('dataResponseSource' in updates)) {
                newDatapointState.dataResponseSource = 'MANUAL';
            }
            return newDatapointState;
        };

        setTopics(prevTopics => {
            const newTopics = prevTopics.map(topic => ({
                ...topic,
                subtopics: topic.subtopics?.map(sub => ({
                    ...sub,
                    datapoints: sub.datapoints?.map(dp =>
                        dp.id === datapointId ? performOptimisticUpdate(dp) : dp
                    )
                }))
            }));
            setGroupedTopics(groupByCategory(newTopics));
            return newTopics;
        });

        setSelectedSubtopic(prevSubtopic => {
            if (!prevSubtopic || !prevSubtopic.datapoints?.some(dp => dp.id === datapointId)) {
                return prevSubtopic;
            }
            return {
                ...prevSubtopic,
                datapoints: prevSubtopic.datapoints.map(dp =>
                    dp.id === datapointId ? performOptimisticUpdate(dp) : dp
                )
            };
        });

        try {
            const originalDatapoint = topics.flatMap(t => t.subtopics).flatMap(s => s.datapoints).find(dp => dp.id === datapointId);

            if (originalDatapoint) {
                const finalData = {
                    dataResponse: originalDatapoint.dataResponse,
                    dataResponseSource: originalDatapoint.dataResponseSource || 'MANUAL',
                    ...updates
                };

                if ('dataResponse' in updates && !('dataResponseSource' in updates)) {
                    finalData.dataResponseSource = 'MANUAL';
                }

                const payload: CsrdDatapointResponseDTO = {
                    csrdProjectId: parseInt(currentProject.id, 10),
                    esrsDatapointId: datapointId,
                    dataResponse: finalData.dataResponse,
                    dataResponseSource: finalData.dataResponseSource,
                };

                await saveOrUpdateCsrdDatapointResponse(payload);
            }
        } catch (error) {
            console.error("Failed to save datapoint update:", error);
            setErrorModalContent(`Failed to save changes for datapoint ${datapointId}: ${getErrorMessage(error)}`);
        }
    }, [currentProject?.id, topics]);

    const handleDatapointFocus = useCallback(async (datapointId: number) => {
        setFocusedDatapointId(datapointId);
        setAiInsightsForFocusedDP([]); setRelevantDocumentChunks([]);
        setLoadingChunks(true); setErrorChunks(null);
        const dp = selectedSubtopic?.datapoints?.find(d => d.id === datapointId);
        if (dp) {
            const dummyInsights: AiInsight[] = [
                { id: `ai1-${dp.id}`, type: 'suggestion', title: `Suggestion for "${dp.dataPointName}"`, content: `Consider reporting on metrics related to ${dp.dataPointName.toLowerCase()} specific to the ${companyInfo?.industry || 'general'} sector.`, relevanceScore: 0.85, source: "AI ESG Analyst" },
                dp.coverageStatus === 'uncovered' ? { id: `ai-warn-${dp.id}`, type: 'risk', title: 'Coverage Gap Detected', content: 'This ESRS datapoint is marked as required but lacks linked document evidence. Review source documents or provide manual justification.', relevanceScore: 0.95, source: 'Coverage Analysis Engine' } : null,
                dp.coverageStatus === 'covered' ? { id: `ai-ok-${dp.id}`, type: 'info', title: 'Coverage Confirmed', content: `Datapoint appears covered by linked document sections. Verify relevance.`, relevanceScore: 0.92, source: 'Coverage Analysis Engine' } : null,
                { id: `ai2-${dp.id}`, type: 'reference', title: 'ESRS Guidance Ref.', content: `Refer to ESRS Standard ${selectedSubtopic?.csrd_subtopic_id?.split('-')[0]} Annex for detailed reporting criteria on ${dp.dataPointName}.`, relevanceScore: 0.9, source: 'ESRS Documentation Linker' },
            ].filter(Boolean) as AiInsight[];
            setAiInsightsForFocusedDP(dummyInsights.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0)));

            if (currentProject?.id && dp.disclosureRequirement && dp.sourceId && dp.coverageStatus !== 'not_applicable') {
                try {
                    const chunks = await fetchRelevantChunks(currentProject.id, dp.disclosureRequirement, dp.sourceId);
                    setRelevantDocumentChunks(chunks);
                    if (chunks.length === 0 && dp.coverageStatus === 'uncovered') { setErrorChunks("No relevant document sections found automatically."); }
                } catch (err: any) {
                    setErrorChunks(`Failed to load document context: ${getErrorMessage(err)}`);
                    setRelevantDocumentChunks([]);
                } finally { setLoadingChunks(false); }
            } else {
                if (dp.coverageStatus !== 'not_applicable') { setErrorChunks("Cannot fetch document context: Missing required identifiers or project context."); }
                else { setErrorChunks(null); }
                setRelevantDocumentChunks([]); setLoadingChunks(false);
            }
        } else { setLoadingChunks(false); }
    }, [selectedSubtopic, currentProject?.id, companyInfo?.industry]);

    const handleApplyAiSuggestion = (suggestionContent: string) => {
        if (focusedDatapointId) {
            handleDatapointUpdate(focusedDatapointId, {
                dataResponse: suggestionContent,
                dataResponseSource: 'AI_OPEN_DATA'
            });
        }
    };

    const handleAutofillDatapoint = async (datapoint: EnhancedEsrsDatapoint, currentDocChunks: ProcessedDocumentChunkDTO[]) => {
        if (!currentProject?.id || !datapoint) { setErrorModalContent("Missing project/datapoint info."); return; }
        setIsAutofillingDpId(datapoint.id); setErrorModalContent(null);
        try {
            const aiSource: DataResponseSource = currentDocChunks.length > 0 ? 'USER_DOC' : 'AI_OPEN_DATA';
            const requestPayload: AutofillDatapointRequestDTO = {
                projectId: parseInt(currentProject.id, 10), companyName: companyInfo?.companyName || "N/A",
                companyIndustry: companyInfo?.industry || "N/A", datapointId: datapoint.id,
                datapointLabel: datapoint.dataPointName, disclosureRequirement: datapoint.disclosureRequirement || "",
                sourceId: datapoint.sourceId || "",
                datapointDataType: normalizeDataType(datapoint.dataType),
                documentChunks: aiSource === 'USER_DOC' ? currentDocChunks.slice(0, 5).map(chunk => ({
                    documentName: chunk.documentName, chunkIndex: chunk.chunkIndex,
                    chunkText: chunk.chunkText, chunkSummary: chunk.chunkSummary,
                })) : undefined
            };
            const result = await autofillDatapointWithAI(requestPayload);

            await handleDatapointUpdate(datapoint.id, {
                dataResponse: result.generatedText,
                dataResponseSource: aiSource
            });

        } catch (err: any) { setErrorModalContent(`AI Autofill failed: ${getErrorMessage(err)}`); }
        finally { setIsAutofillingDpId(null); }
    };

    const handleAutoFillTopic = async (topicToFill: EnhancedCsrdTopic) => {
        handleClearLocalErrors();
        if (!companyInfo?.companyName || !currentProject?.id) { setErrorTopics("Company Information missing."); return; }
        if (!topicToFill.subtopics || topicToFill.subtopics.length === 0) { setErrorTopics(`Topic ${topicToFill.topicLabel} has no subtopics.`); return; }

        setAutoFillingTopicIds((prev) => new Set(prev).add(topicToFill.id));
        try {
            for (const subtopic of topicToFill.subtopics || []) {
                await handleGlobalSubtopicAiFill(subtopic, false);
            }
        } catch (error: any) { setErrorTopics(`AI Assist failed for ${topicToFill.topicCode}: ${getErrorMessage(error)}`); }
        finally { setAutoFillingTopicIds((prev) => { const n = new Set(prev); n.delete(topicToFill.id); return n; }); }
    };

    const handleGlobalSubtopicAiFill = async (subtopicToFill: EnhancedCsrdSubtopic, showModalFeedback: boolean = true) => {
        if (!currentProject?.id || !companyInfo?.companyName) {
            setErrorTopics("Project or company information is missing for AI Fill."); return;
        }
        const fillableDatapoints = subtopicToFill.datapoints?.filter(dp => !['boolean', 'alternative', 'table'].includes(normalizeDataType(dp.dataType))) || [];
        if (fillableDatapoints.length === 0) {
            console.warn(`Subtopic ${subtopicToFill.csrd_subtopic_id} has no fillable datapoints.`); return;
        }
        if(showModalFeedback) setAutoFillingSubtopicId(subtopicToFill.id);

        for (const dp of fillableDatapoints) {
            if(showModalFeedback) setIsAutofillingDpId(dp.id);
            try {
                let docChunksForDp: ProcessedDocumentChunkDTO[] = [];
                let aiSource: DataResponseSource ='AI_OPEN_DATA';
                if (currentProject?.id && dp.disclosureRequirement && dp.sourceId && dp.coverageStatus !== 'not_applicable') {
                    try {
                        docChunksForDp = await fetchRelevantChunks(currentProject.id, dp.disclosureRequirement, dp.sourceId);
                        if (docChunksForDp.length > 0) { aiSource = 'USER_DOC'; }
                    } catch (chunkError) { console.warn(`Failed to fetch chunks for DP ${dp.id} during global fill: ${getErrorMessage(chunkError)}`); }
                }

                const requestPayload: AutofillDatapointRequestDTO = {
                    projectId: parseInt(currentProject.id, 10), companyName: companyInfo.companyName,
                    companyIndustry: companyInfo.industry || "N/A", datapointId: dp.id,
                    datapointLabel: dp.dataPointName, disclosureRequirement: dp.disclosureRequirement || "",
                    sourceId: dp.sourceId || "", datapointDataType: normalizeDataType(dp.dataType),
                    documentChunks: aiSource === 'USER_DOC' ? docChunksForDp.slice(0, 5).map(chunk => ({
                        documentName: chunk.documentName, chunkIndex: chunk.chunkIndex,
                        chunkText: chunk.chunkText, chunkSummary: chunk.chunkSummary,
                    })) : undefined,
                };
                const result = await autofillDatapointWithAI(requestPayload);
                let finalResponse = result.generatedText;

                const numericTypes = ['integer', 'decimal', 'monetary', 'mass', 'volume', 'energy', 'intensity', 'ghgemissions', 'mdr-p', 'percent', 'gyear'];
                if (numericTypes.includes(normalizeDataType(dp.dataType))) {
                    finalResponse = parseNumericAIResponse(result.generatedText, normalizeDataType(dp.dataType));
                }

                await handleDatapointUpdate(dp.id, {
                    dataResponse: finalResponse,
                    dataResponseSource: aiSource
                });

            } catch (fillError: any) {
                console.error(`AI Autofill failed for DP ${dp.id} (${dp.dataPointName}): ${getErrorMessage(fillError)}`);
                if(showModalFeedback) setErrorModalContent(`AI fill failed for ${dp.dataPointName}.`);
            }
            finally { if(showModalFeedback) setIsAutofillingDpId(null); }
        }

        if(showModalFeedback) setAutoFillingSubtopicId(null);
    };

    const handleTriggerSummaryGeneration = async (subtopicId: number | string) => {
        if (!currentProject?.id) throw new Error("No project selected");
        return await triggerCsrdSubtopicTextGeneration(currentProject.id, subtopicId);
    };

    const handleGetSummary = async (subtopicId: number | string) => {
        if (!currentProject?.id) throw new Error("No project selected");
        return await getCsrdSubtopicGeneratedText(currentProject.id, subtopicId);
    };

    const renderCoverageIcon = (status: EnhancedEsrsDatapoint['coverageStatus'], size: number = 16) => {
        const commonProps = { size: size, className: "coverage-icon flex-shrink-0" };
        switch (status) {
            case 'covered':
                return <OverlayTrigger placement="top" overlay={<Tooltip>Covered by documents</Tooltip>}>
                    <Target {...commonProps} className="text-success" />
                </OverlayTrigger>;
            case 'uncovered':
                return <OverlayTrigger placement="top" overlay={<Tooltip>Required, but not covered</Tooltip>}>
                    <Target {...commonProps} className="text-danger" />
                </OverlayTrigger>;
            case 'not_applicable':
            default:
                return <span style={{ display: 'inline-block', width: `${size}px` }} title="Not an ESRS-required datapoint"></span>;
        }
    };

    const renderDashboardStats = () => {
        const statItems = [
            { title: "Total Topics", value: dashboardStats.totalTopics, icon: <Package size={24} />, unit: "Topics" },
            { title: "Completion", value: dashboardStats.overallProgress, icon: <CheckSquare size={24} />, unit: "%", progress: dashboardStats.overallProgress, denominator: dashboardStats.totalPossibleDatapoints, numerator: dashboardStats.completedDatapoints },
            { title: "ESRS Coverage", value: dashboardStats.overallCoverage, icon: <Target size={24} />, unit: "%", progress: dashboardStats.overallCoverage, denominator: dashboardStats.requiredDatapoints, numerator: dashboardStats.coveredDatapoints },
        ];
        const showPlaceholder = isLoading && !globalError;
        const getProgressVariant = (progress: number, denominator: number): string => {
            if (denominator === 0) return 'light';
            if (progress >= 95) return 'success';
            if (progress >= 70) return 'info';
            if (progress >= 40) return 'warning';
            return 'danger';
        };

        return (
            <Row className="mb-4 dashboard-stats-row g-3">
                {statItems.map((stat, index) => (
                    <Col key={stat.title} md={6} lg={4}>
                        <Card className="h-100 stat-card modern-stat-card border-0">
                            <Card.Body className="d-flex align-items-center p-3">
                                <div className={`stat-icon-box flex-shrink-0 me-3 bg-${index === 0 ? 'secondary' : index === 1 ? 'success' : 'info'}-subtle text-${index === 0 ? 'secondary' : index === 1 ? 'success' : 'info'}-emphasis`}>
                                    {stat.icon}
                                </div>
                                <div className="flex-grow-1">
                                    <div className="d-flex justify-content-between align-items-baseline">
                                        <p className="mb-0 text-muted small text-uppercase stat-title">{stat.title}</p>
                                        {stat.denominator !== undefined && !showPlaceholder && (
                                            <span className="small text-muted ms-2">
                                                {stat.numerator} / {stat.denominator}
                                            </span>
                                        )}
                                    </div>
                                    <h4 className="mb-0 fw-semibold stat-value d-flex align-items-baseline">
                                        {showPlaceholder ? <Placeholder xs={4} /> : (
                                            <>
                                                {stat.progress !== undefined ? stat.value.toFixed(0) : stat.value}
                                                {stat.unit && <span className="small fw-normal text-muted ms-1">{stat.unit}</span>}
                                            </>
                                        )}
                                    </h4>
                                    {stat.progress !== undefined && (
                                        <ProgressBar
                                            now={showPlaceholder ? 0 : stat.progress}
                                            variant={getProgressVariant(stat.progress, stat.denominator ?? 1)}
                                            className={`stat-progress-bar mt-2 ${showPlaceholder ? 'placeholder' : ''}`}
                                            style={{ height: '6px' }}
                                            title={!showPlaceholder ? `${stat.progress.toFixed(1)}%` : 'Loading...'}
                                        />
                                    )}
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>
        );
    };

    const renderSelectedTopicDetails = (topic: EnhancedCsrdTopic) => {
        if (!topic) return <Alert variant="light" className="text-center p-5">Select a topic from the sidebar.</Alert>;

        const isAutoFillingThisTopic = autoFillingTopicIds.has(topic.id);
        const sortedSubtopicsToDisplay = topic.subtopics || [];
        const categoryClass = `topic-border-${determineCategoryFromCode(topic.topicCode).toLowerCase()}`;

        return (
            <div className="selected-topic-details">
                <Card className={`mb-4 topic-summary-card ${categoryClass} border-start-0`}>
                    <Card.Body className="p-4">
                        <Row className="align-items-center">
                            <Col md={8}>
                                <Card.Title as="h3" className="mb-1 topic-main-title">{topic.topicCode} - {topic.topicLabel}</Card.Title>
                                <Card.Subtitle className="text-muted small d-flex flex-wrap align-items-center gap-2 mt-2">
                                    <span>{topic.subtopics?.length || 0} Subtopics</span>
                                    {topic.coverageStats && topic.coverageStats.required > 0 && (
                                        <span className="ms-md-2 ps-md-2 border-start-md d-inline-flex align-items-center text-info-emphasis">
                                            <Target size={12} className='me-1 flex-shrink-0' />
                                            {topic.coverageStats.covered} / {topic.coverageStats.required} Covered ({topic.coverageStats.percentage.toFixed(0)}%)
                                        </span>
                                    )}
                                    {topic.coverageStats && topic.coverageStats.required === 0 && (
                                        <span className="ms-md-2 ps-md-2 border-start-md d-inline-flex align-items-center text-muted fst-italic">
                                            <Target size={12} className='me-1 opacity-50 flex-shrink-0' />
                                            No required points
                                        </span>
                                    )}
                                </Card.Subtitle>
                            </Col>
                            <Col md={4} className="text-md-end mt-3 mt-md-0">
                                <Button variant="outline-secondary" size="sm" onClick={() => handleAutoFillTopic(topic)}
                                        disabled={isAutoFillingThisTopic || !topic.subtopics?.length}
                                        className="ai-assist-button flex-shrink-0 ms-md-3 px-3 py-2">
                                    {isAutoFillingThisTopic ? (<><Spinner as="span" animation="border" size="sm" className="me-2" /> Working...</>) : (<><Zap size={16} className="me-1" /> AI Assist Topic</>)}
                                </Button>
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>

                <h4 className="mb-3 subtopic-grid-title"><SubtopicGridIcon size={20} className="me-2 text-gradient" />Subtopics Overview</h4>

                {sortedSubtopicsToDisplay.length > 0 ? (
                    <Row xs={1} md={2} xl={3} className="g-3">
                        {sortedSubtopicsToDisplay.map(sub => {
                            const totalDatapoints = sub.datapoints?.length || 0;
                            const fillableDatapointsCount = sub.datapoints?.filter(dp => !['boolean', 'alternative', 'table'].includes(normalizeDataType(dp.dataType))).length || 0;
                            const completedDatapoints = sub.datapoints?.filter(dp => dp.dataResponse && dp.dataResponse.trim() !== '').length || 0;
                            const progressPercentage = totalDatapoints > 0 ? Math.round((completedDatapoints / totalDatapoints) * 100) : (totalDatapoints === 0 ? 100 : 0);
                            const isComplete = totalDatapoints > 0 && completedDatapoints === totalDatapoints;
                            const coverageStats = sub.coverageStats;
                            let coverageText = 'N/A'; let coverageTooltipText = 'Coverage not applicable';
                            let coverageVariant: 'light' | 'success' | 'info' | 'warning' | 'danger' = 'light';
                            let coverageTextClass = 'text-muted';

                            if (coverageStats) {
                                if (coverageStats.required > 0) {
                                    coverageText = `${coverageStats.covered}/${coverageStats.required} Covered`;
                                    coverageTooltipText = `${coverageStats.percentage.toFixed(0)}% Coverage`;
                                    coverageVariant = coverageStats.percentage === 100 ? 'success' : coverageStats.percentage >= 70 ? 'info' : coverageStats.percentage >= 40 ? 'warning' : 'danger';
                                    coverageTextClass = `${coverageVariant}-emphasis`;
                                } else {
                                    coverageText = `No Required`; coverageTooltipText = `No ESRS datapoints`;
                                    coverageVariant = 'light'; coverageTextClass = 'text-muted';
                                }
                            }
                            const isCurrentlyFillingThisSubtopic = autoFillingSubtopicId === sub.id;

                            return (
                                <Col key={sub.id}>
                                    <Card className="h-100 subtopic-card-item border-light-subtle">
                                        <Card.Body className="d-flex flex-column p-3">
                                            <div className="flex-grow-1 mb-2">
                                                <div className="d-flex justify-content-between align-items-start mb-1 gap-2">
                                                    <p className="fw-semibold mb-0 subtopic-id-label flex-grow-1 pe-1 text-truncate" title={sub.csrd_subtopic_id}>{sub.csrd_subtopic_id}</p>
                                                    <OverlayTrigger placement="top" overlay={<Tooltip>{coverageTooltipText}</Tooltip>}>
                                                        <BsBadge pill bg={`${coverageVariant}-subtle`} className={`fw-medium small subtopic-coverage-badge flex-shrink-0 ${coverageTextClass}`}>
                                                            <Target size={11} className="me-1 icon-inline" /> {coverageText}
                                                        </BsBadge>
                                                    </OverlayTrigger>
                                                </div>
                                                <p className="small text-dark mb-2 subtopic-title-label three-line-clamp" title={sub.csrd_subtopic_label}>{sub.csrd_subtopic_label}</p>
                                            </div>
                                            <div className="subtopic-progress-section mb-3">
                                                {totalDatapoints > 0 ? (
                                                    <>
                                                        <div className="d-flex justify-content-between align-items-center mb-1">
                                                            <span className="small text-muted d-flex align-items-center">
                                                                <CheckSquare size={13} className="me-1 flex-shrink-0" /> Completion
                                                            </span>
                                                            <span className={`small fw-bold ${isComplete ? 'text-success' : 'text-secondary'}`}>
                                                                {completedDatapoints} / {totalDatapoints}
                                                            </span>
                                                        </div>
                                                        <ProgressBar
                                                            now={progressPercentage}
                                                            variant={isComplete ? "success" : progressPercentage > 0 ? "secondary" : "light"}
                                                            className="subtopic-progress-bar" style={{ height: '5px' }}
                                                            title={`${progressPercentage}% Complete`}
                                                        />
                                                    </>
                                                ) : (
                                                    <p className="small text-muted text-center mt-1 mb-0 fst-italic">No datapoints.</p>
                                                )}
                                            </div>
                                            <div className="mt-auto d-flex justify-content-end align-items-center pt-2 border-top border-light-subtle gap-2 flex-wrap">
                                                <Button size="sm" onClick={() => handleOpenSubtopic(sub)} className="view-subtopic-btn flex-grow-1" variant={"outline-secondary"}>
                                                    <Eye size={14} className="me-1" /> Details
                                                </Button>
                                                <OverlayTrigger placement="top" overlay={<Tooltip>{fillableDatapointsCount === 0 ? "No autofillable datapoints" : "AI Fill all datapoints"}</Tooltip>}>
                                                    <Button
                                                        variant="light"
                                                        size="sm"
                                                        onClick={(e) => { e.stopPropagation(); handleGlobalSubtopicAiFill(sub); }}
                                                        disabled={isCurrentlyFillingThisSubtopic || fillableDatapointsCount === 0 || isLoading}
                                                        className="ai-assist-topic-btn icon-button"
                                                    >
                                                        {isCurrentlyFillingThisSubtopic ? (
                                                            <Spinner as="span" animation="border" size="sm" />
                                                        ) : (
                                                            <Sparkles size={14} />
                                                        )}
                                                    </Button>
                                                </OverlayTrigger>
                                            </div>
                                        </Card.Body>
                                    </Card>
                                </Col>
                            );
                        })}
                    </Row>
                ) : (<Alert variant="secondary" className="text-center py-4 mt-3 bg-secondary-subtle border-secondary-subtle">
                    <Info size={30} className="mb-2 text-muted" />
                    <p className="mb-0 text-secondary-emphasis">No subtopics found for this topic.</p>
                </Alert>)}
            </div>
        );
    };


    if (isLoading && !globalError) {
        return (<Container fluid className="vh-100 d-flex justify-content-center align-items-center bg-light"><Spinner animation="border" variant="secondary" style={{ width: '4rem', height: '4rem' }} /><span className='ms-3 fs-4 text-muted'>Loading Dashboard...</span></Container>);
    }
    if (!contextIsLoading && (!currentProject || !companyInfo || !companyInfo.companyName)) {
        return (
            <Container fluid className="text-center py-5 vh-100 d-flex flex-column justify-content-center align-items-center bg-light">
                <AlertCircle size={48} className="mb-3 text-warning"/>
                <Alert variant="warning" className="col-md-6 shadow-sm">
                    <Alert.Heading>Setup Required</Alert.Heading>
                    Missing project or company information. Please complete the setup to proceed.
                </Alert>
                <Button variant="secondary" size="lg" onClick={() => navigate(currentProject ? `${import.meta.env.BASE_URL}csrd/company-info` : `${import.meta.env.BASE_URL}csrd/create`)}>
                    Go to Setup <Info size={16} className="ms-1"/>
                </Button>
            </Container>
        );
    }
    if (globalError && !isLoading) {
        return (
            <Container fluid className="py-4 px-lg-5">
                <Alert variant="danger" onClose={handleClearLocalErrors} dismissible>
                    <Alert.Heading>Error Loading Dashboard Data</Alert.Heading>
                    <p>Could not load all necessary data. Some features might be unavailable.</p>
                    <hr />
                    <p className="mb-0 small"><strong>Details:</strong> {globalError}</p>
                </Alert>
                {renderDashboardStats()}
            </Container>
        );
    }
    if (!isLoading && !globalError && topics.length === 0 && dashboardStats.requiredDatapoints === 0) {
        return (
            <Container fluid className="py-4 px-lg-5">
                {renderDashboardStats()}
                <Alert variant="secondary" className="mt-4 text-center bg-secondary-subtle border-secondary-subtle">
                    <Info size={24} className="me-2 text-secondary" />
                    <span className="text-secondary-emphasis">No CSRD topics configured or no required datapoints found.</span>
                </Alert>
            </Container>
        );
    }

    const currentSelectedTopicObject = expandedCategory && selectedTopicId ? groupedTopics[expandedCategory]?.find(t => t.id === selectedTopicId) : null;

    return (
        <Container fluid className="esg-command-center-layout vh-100 d-flex flex-column p-0 integrated-dashboard">
            <Row className="flex-grow-1 g-0 main-row-wrapper" style={{ minHeight: 0 }}>
                <Col md={4} lg={3} xl={2} className="esg-sidebar p-3 border-end d-flex flex-column">
                    <Card className="mb-3 company-project-sidebar-info border-0 bg-transparent flex-shrink-0">
                        <Card.Body className="p-2">
                            <div className="d-flex align-items-center mb-2">
                                <Activity size={18} className="me-2 text-secondary flex-shrink-0" />
                                <div><small className="text-muted d-block lh-1 label-sidebar">PROJECT</small><strong className="lh-1 project-name-sidebar">{currentProject?.projectName}</strong></div>
                            </div>
                            <div className="d-flex align-items-center">
                                <Building size={18} className="me-2 text-secondary flex-shrink-0" />
                                <div><small className="text-muted d-block lh-1 label-sidebar">COMPANY</small><strong className="lh-1 company-name-sidebar">{companyInfo?.companyName}</strong></div>
                            </div>
                        </Card.Body>
                    </Card>

                    <h6 className="sidebar-heading text-muted text-uppercase small px-1 mt-2 mb-2 flex-shrink-0">ESG Categories</h6>
                    <div className='flex-grow-1 overflow-auto sidebar-nav-scroll'>
                        <Nav variant="pills" className="flex-column esg-category-nav nav-pills-custom">
                            {esgCategories.map(cat => {
                                const hasTopics = groupedTopics[cat.category]?.length > 0;
                                return (
                                    <React.Fragment key={cat.category}>
                                        <Nav.Item>
                                            <Nav.Link
                                                active={expandedCategory === cat.category}
                                                onClick={() => handleSelectCategory(cat.category)}
                                                className="d-flex justify-content-between align-items-center category-nav-link"
                                                disabled={!hasTopics && !isLoadingTopics}
                                            >
                                                <span className="d-flex align-items-center">
                                                    {React.cloneElement(cat.icon as React.ReactElement, { className: "category-icon me-2" })}
                                                    <span className="category-text">{cat.category}</span>
                                                </span>
                                                {hasTopics && (
                                                    expandedCategory === cat.category ? <ChevronUp size={18} /> : <ChevronDown size={18} />
                                                )}
                                            </Nav.Link>
                                        </Nav.Item>
                                        {expandedCategory === cat.category && (
                                            <ListGroup variant="flush" className="topic-list-group ps-3 my-1">
                                                {isLoadingTopics ?
                                                    Array.from({length:3}).map((_, idx) => ( <ListGroup.Item key={idx} className="topic-nav-item topic-nav-item-placeholder border-0 px-2 py-1" disabled> <Placeholder as="span" animation="glow" className="w-100 d-flex align-items-center"> <Placeholder xs={1} bg="secondary" className="me-2 rounded-circle" style={{height:'12px', width:'12px'}}/> <Placeholder xs={9} bg="secondary" /> </Placeholder> </ListGroup.Item> ))
                                                    : hasTopics ?
                                                        groupedTopics[cat.category].map(topic => {
                                                            const topicCoverageStats = topic.coverageStats;
                                                            let coverageIndicatorColor = 'text-muted opacity-25';
                                                            let coverageTooltip = 'No required points';
                                                            if (topicCoverageStats && topicCoverageStats.required > 0) {
                                                                coverageIndicatorColor = topicCoverageStats.percentage === 100 ? 'text-success' : topicCoverageStats.percentage >= 70 ? 'text-info' : topicCoverageStats.percentage >= 40 ? 'text-warning' : 'text-danger';
                                                                coverageTooltip = `${topicCoverageStats.covered}/${topicCoverageStats.required} Covered (${topicCoverageStats.percentage.toFixed(0)}%)`;
                                                            }

                                                            return (
                                                                <ListGroup.Item
                                                                    key={topic.id}
                                                                    action
                                                                    active={selectedTopicId === topic.id}
                                                                    onClick={() => handleSelectTopic(topic.id)}
                                                                    className="topic-nav-item d-flex justify-content-between align-items-center border-0 px-2 py-1"
                                                                >
                                                                    <div className="topic-item-text-wrapper flex-grow-1 overflow-hidden me-2">
                                                                        <span className="topic-code d-block text-truncate" title={topic.topicCode}>{topic.topicCode}</span>
                                                                        <small className="topic-label d-block text-muted text-truncate" title={topic.topicLabel}>{topic.topicLabel}</small>
                                                                    </div>
                                                                    <OverlayTrigger placement="right" overlay={<Tooltip>{coverageTooltip}</Tooltip>}>
                                                                        <Target size={14} className={`ms-auto flex-shrink-0 ${coverageIndicatorColor}`} />
                                                                    </OverlayTrigger>
                                                                </ListGroup.Item>
                                                            )
                                                        })
                                                        : <ListGroup.Item disabled className="text-muted small fst-italic px-2 py-1 border-0">No topics found.</ListGroup.Item>}
                                            </ListGroup>
                                        )}
                                    </React.Fragment>
                                )
                            })}
                        </Nav>
                    </div>
                </Col>

                <Col md={8} lg={9} xl={10} className="main-content-area p-3 p-lg-4 d-flex flex-column">
                    <div className='flex-shrink-0'>
                        {renderDashboardStats()}
                    </div>

                    <div className="flex-grow-1 mt-2 overflow-auto main-content-scroll">
                        {isLoadingTopics && !errorTopics && (<div className="d-flex justify-content-center align-items-center h-50"><Spinner animation="border" variant="secondary" style={{width: '3rem', height: '3rem'}} /> <span className="ms-2 fs-5 text-muted">Loading Topics...</span></div>)}
                        {!isLoadingTopics && errorTopics && (<Alert variant="danger" className="m-3">{errorTopics}</Alert>)}
                        {!isLoadingTopics && !errorTopics && !currentSelectedTopicObject && (
                            <div className="d-flex flex-column align-items-center justify-content-center h-100 text-muted text-center placeholder-view rounded border border-dashed p-5 bg-white">
                                <LayoutDashboard size={50} className="mb-3 text-black-50 placeholder-icon" />
                                <h4 className="mb-2 fw-light">ESRS Command Center</h4>
                                <p className="small col-md-8 col-lg-6 text-secondary">
                                    Select an ESRS topic from the sidebar to manage subtopics, fill datapoints, and review document coverage.
                                </p>
                                {topics.length === 0 && <Alert variant="light" className="mt-3 small">No topics available for this project.</Alert>}
                            </div>
                        )}
                        {!isLoadingTopics && !errorTopics && currentSelectedTopicObject && renderSelectedTopicDetails(currentSelectedTopicObject)}
                    </div>
                </Col>
            </Row>

            {showSubtopicModal && selectedSubtopic && (
                <SubtopicDetailModal
                    show={showSubtopicModal}
                    onHide={handleCloseSubtopicModal}
                    selectedSubtopic={selectedSubtopic}
                    loadingModalContent={loadingModalContent}
                    errorModalContent={errorModalContent}
                    focusedDatapointId={focusedDatapointId}
                    onDatapointFocus={handleDatapointFocus}
                    onDatapointUpdate={handleDatapointUpdate}
                    onAutofillDatapoint={handleAutofillDatapoint}
                    isAutofillingDpId={isAutofillingDpId}
                    relevantDocumentChunks={relevantDocumentChunks}
                    loadingChunks={loadingChunks}
                    companyProfileId={currentProject.id}
                    errorChunks={errorChunks}
                    aiInsightsForFocusedDP={aiInsightsForFocusedDP}
                    onApplyAiSuggestion={handleApplyAiSuggestion}
                    onGenerateSummary={handleTriggerSummaryGeneration}
                    onGetSummary={handleGetSummary}
                />
            )}
        </Container>
    );
};

export default CSRDDashboard;