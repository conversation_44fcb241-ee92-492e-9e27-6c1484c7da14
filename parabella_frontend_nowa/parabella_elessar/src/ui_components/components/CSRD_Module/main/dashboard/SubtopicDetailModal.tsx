import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Accordion, Dropdown, Badge as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OverlayTrigger, Row, Col } from 'react-bootstrap';
import {
    FileText as FileTextIconLucide,
    Sparkles,
    Info,
    Wand2,
    LayoutGrid,
    BookOpen,
    BrainCircuit,
    AlertTriangle,
    SearchCheck,
    AlertOctagon,
    X as XIcon,
    ChevronDown,
    ShieldCheck
} from 'lucide-react';
import axios from "axios";

import "../css/Dashboard.css"

// Project-specific imports
import { ProcessedDocumentChunkDTO, CsrdSubtopicGeneratedTextDTO } from '../../api/csrdApiTypes';
import DataPointRenderer from './DataPointRenderer';
import { EnhancedCsrdSubtopic, EnhancedEsrsDatapoint } from "./CsrdDashboard";
import { getErrorMessage } from '../../context/CsrdProjectProvider';

// --- API Client & Type Definitions ---
interface AnomalyFinding { severity: 'High' | 'Medium' | 'Low' | 'Info'; description: string; anomaly_type: string; }
interface CoverageGapFinding { severity: 'High' | 'Medium' | 'Low' | 'Info'; description: string; concern_type: string; }
interface AnalysisResults { anomalies: AnomalyFinding[]; coverage_gaps: CoverageGapFinding[]; }

// API functions
export const submitSnippetAnalysisJob = async (text_snippet: string, disclosure_code: string, company_profile_id: string, datapoint_name?: string): Promise<AnalysisResults> => {
    const response = await axios.post('http://localhost:8000/api/v1/jobs/analyze_snippet', { text_snippet, disclosure_code, company_profile_id, datapoint_name });
    if (response.data?.status === 'COMPLETED' && response.data.results?.findings) { return response.data.results.findings; }
    throw new Error(response.data?.error_message || "Analysis job failed.");
};

// --- Component Props ---
interface SubtopicDetailModalProps {
    show: boolean;
    onHide: () => void;
    selectedSubtopic: EnhancedCsrdSubtopic | null;
    companyProfileId: string;
    loadingModalContent: boolean;
    errorModalContent: string | null;
    focusedDatapointId: number | null;
    onDatapointFocus: (datapointId: number) => void;
    onDatapointUpdate: (datapointId: number, fieldName: string, newValue: any) => void;
    onAutofillDatapoint: (datapoint: EnhancedEsrsDatapoint, relevantChunks: ProcessedDocumentChunkDTO[]) => Promise<void>;
    isAutofillingDpId: number | null;
    relevantDocumentChunks: ProcessedDocumentChunkDTO[];
    loadingChunks: boolean;
    errorChunks: string | null;
    onGenerateSummary: (subtopicId: number | string) => Promise<any>;
    onGetSummary: (subtopicId: number | string) => Promise<CsrdSubtopicGeneratedTextDTO>;
}

// --- Style Component for the Professional Redesign ---
const ProfessionalUIDesign = () => (
    <style type="text/css">{`
    :root {
      --primary-accent: #4f46e5;
      --background-light: #f9fafb;
      --background-main: #ffffff;
      --border-color: #e5e7eb;
      --text-primary: var(--bs-primary);
      --text-secondary: var(--bs-primary);
      --transition-speed: 0.2s;
    }
    .modal-pro .modal-content {
      border-radius: 0.75rem; box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25);
      border: 1px solid var(--border-color); height: 90vh;
      width: 150vw;  max-width: none; /* Remove the limit */
      overflow: hidden;
      width: 2000px;
    }
    
   
    .modal-pro .modal-body { display: flex; padding: 0; overflow-y: hidden }
    .pane { padding: 1.5rem; }
    .datapoints-pane-pro {
      flex: 0 0 60%;
      background-color: var(--background-main);
      min-width: 60%;
      overflow-y: auto;
    }
    .inspector-pane {
      flex: 1 1 40%;
      display: flex;
      background-color: var(--background-light);
      border-left: 1px solid var(--border-color);
      position: sticky; top: 0; height: 90vh; align-self: flex-start;
    }
    .content-pane-pro { flex-grow: 1; position: relative; overflow: hidden; }
    .vertical-toolbar {
      flex: 0 0 60px; padding: 1rem 0; display: flex; flex-direction: column;
      align-items: center; gap: 0.5rem; background-color: #fff;
      border-left: 1px solid var(--border-color);
    }
    .toolbar-btn-vertical {
        width: 44px; height: 44px; border-radius: 0.5rem; color: var(--text-secondary);
        background: transparent; border: none; transition: all var(--transition-speed) ease;
        display: flex; align-items: center; justify-content: center; position: relative;
    }
    .toolbar-btn-vertical:hover { background-color: #e5e7eb; color: var(--text-primary); }
    .toolbar-btn-vertical.active { background-color: var(--bs-primary); color: #fff; box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3); }

    /* ✅ NEW: Dynamic, color-coded focus styles */
    .adv-datapoint-focused-manual {
      border-color: var(--text-secondary) !important;
      box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.4);
    }
    .adv-datapoint-focused-doc-ai {
      --focus-color: #22c55e; /* Green */
      border-color: var(--focus-color) !important;
      box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.4);
    }
    .adv-datapoint-focused-ai-draft {
      --focus-color: #f59e0b; /* Yellow / Amber */
      border-color: var(--focus-color) !important;
      box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.4);
    }

    .view-pane-pro {
      position: absolute; top: 0; left: 0; width: 100%; height: 100%;
      padding: 1.5rem; opacity: 0; transform: translateX(15px);
      visibility: hidden;
      transition: opacity var(--transition-speed) ease-in-out, transform var(--transition-speed) ease-in-out, visibility var(--transition-speed);
      overflow-y: auto;
    }
    .view-pane-pro.visible { opacity: 1; transform: translateX(0); visibility: visible; }
    .view-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid var(--border-color); }

    /* ✅ NEW: Minimalist button style */
    .ai-button-minimal {
        display: flex; align-items: center; gap: 0.5rem; padding: 0.375rem 0.75rem;
        font-weight: 500; font-size: 0.875rem; border-radius: 0.375rem;
        color: var(--bs-primary); background-color: transparent;
        border: 1px solid transparent; cursor: pointer;
        transition: all var(--transition-speed) ease;
    }
    .ai-button-minimal:hover:not(:disabled) {
        background-color: var(--border-color);
        color: var(--text-primary);
    }
    .ai-button-minimal.primary { color: var(--bs-primary); }
    .ai-button-minimal.primary:hover:not(:disabled) { background-color: rgba(79, 70, 229, 0.1); }
    .ai-button-minimal:disabled { opacity: 0.5; cursor: not-allowed; }

    .copilot-placeholder { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center; padding: 2rem; }
    .placeholder-feature-card { background: var(--background-main); border: 1px solid var(--border-color); padding: 1.5rem; border-radius: 0.5rem; height: 100%; }
  `}</style>
);


const SubtopicDetailModal: React.FC<SubtopicDetailModalProps> = (props) => {
    const { show, onHide, selectedSubtopic, companyProfileId, loadingModalContent, errorModalContent,
        focusedDatapointId, onDatapointFocus, onDatapointUpdate, onAutofillDatapoint,
        isAutofillingDpId, relevantDocumentChunks, loadingChunks, errorChunks,
        onGenerateSummary, onGetSummary } = props;

    const [activeView, setActiveView] = useState<'copilot' | 'summary' | 'context'>('copilot');
    const [summary, setSummary] = useState<CsrdSubtopicGeneratedTextDTO | null>(null);
    const [isLoadingSummary, setIsLoadingSummary] = useState(false);
    const [summaryError, setSummaryError] = useState<string | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [analysisError, setAnalysisError] = useState<string | null>(null);
    const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
    const focusedDatapoint = selectedSubtopic?.datapoints?.find(dp => dp.id === focusedDatapointId);

    const handleRunAnalysis = async (textToAnalyze: string, analysisTargetName: string) => {
        if (!selectedSubtopic?.csrd_subtopic_id || !companyProfileId) return;
        setIsAnalyzing(true); setAnalysisError(null); setAnalysisResults(null);
        try {
            const results = await submitSnippetAnalysisJob(textToAnalyze, selectedSubtopic.csrd_subtopic_id, companyProfileId, analysisTargetName);
            setAnalysisResults(results);
        } catch (error) { setAnalysisError(getErrorMessage(error)); }
        finally { setIsAnalyzing(false); }
    };

    useEffect(() => {
        const fetchInitialData = async () => {
            if (!show || !selectedSubtopic) return;
            setIsLoadingSummary(true); setSummary(null); setSummaryError(null);
            setAnalysisResults(null); setAnalysisError(null);
            try {
                const summaryResult = await onGetSummary(selectedSubtopic.id);
                setSummary(summaryResult);
            } catch (err) {
                if (!axios.isAxiosError(err) || err.response?.status !== 404) {
                    setSummaryError(`Failed to load summary: ${getErrorMessage(err)}`);
                }
                setSummary(null);
            } finally {
                setIsLoadingSummary(false);
            }
        };
        fetchInitialData();
    }, [show, selectedSubtopic, onGetSummary]);

    const handleGenerateSummaryClick = async () => {
        if (!selectedSubtopic) return;
        setIsLoadingSummary(true); setSummaryError(null);
        try {
            await onGenerateSummary(selectedSubtopic.id);
            setTimeout(async () => {
                const summaryData = await onGetSummary(selectedSubtopic.id);
                setSummary(summaryData);
                setIsLoadingSummary(false);
                setActiveView('summary'); // Switch to summary view after generation
                if (summaryData?.generatedText) {
                    // Optionally auto-run analysis
                    // await handleRunAnalysis(summaryData.generatedText, 'Analysis of Regenerated Summary');
                    // setActiveView('copilot');
                }
            }, 2500);
        } catch (error) {
            setSummaryError(`Failed to start summary generation: ${getErrorMessage(error)}`);
            setIsLoadingSummary(false);
        }
    };

    const CoPilotPlaceholder = () => (
        <div className="copilot-placeholder">
            <Sparkles size={48} className="mb-4 text-secondary" />
            <h4 className="fw-bold text-secondary">Unlock AI-Powered Insights</h4>
            <p className="text-secondary mb-4 col-md-10 mx-auto">
                Run the Co-Pilot to check your text for potential coverage gaps, data anomalies, and inconsistencies against ESRS requirements.
            </p>
            <Row className="g-3 mb-4 text-start">
                <Col md={4}><div className="placeholder-feature-card"><SearchCheck size={24} className="mb-2 text-primary" style={{'--bs-primary': 'var(--primary-accent)'}}/><h6 className="fw-semibold mb-1">Find Gaps</h6><p className="small text-secondary mb-0">Identify missing ESRS-required disclosures.</p></div></Col>
                <Col md={4}><div className="placeholder-feature-card"><AlertTriangle size={24} className="mb-2 text-warning" /><h6 className="fw-semibold mb-1">Identify Anomalies</h6><p className="small text-secondary mb-0">Spot inconsistencies in quantitative data.</p></div></Col>
                <Col md={4}><div className="placeholder-feature-card"><ShieldCheck size={24} className="mb-2 text-success" /><h6 className="fw-semibold mb-1">Ensure Consistency</h6><p className="small text-secondary mb-0">Check for conflicting narrative statements.</p></div></Col>
            </Row>
            <p className="text-secondary small">Analyze the narrative summary or a specific datapoint to begin.</p>
        </div>
    );

    const renderedDataPoints = useMemo(() => {
        return selectedSubtopic?.datapoints?.map(dp => (
            <DataPointRenderer
                key={dp.id} datapoint={dp} isFocused={focusedDatapointId === dp.id} onFocus={onDatapointFocus}
                onChange={onDatapointUpdate}
                onAutofill={async () => await onAutofillDatapoint(dp, relevantDocumentChunks || [])}
                isAutofilling={isAutofillingDpId === dp.id} isReadOnly={false}
            />
        ));
    }, [selectedSubtopic, focusedDatapointId, onDatapointFocus, onDatapointUpdate, onAutofillDatapoint, isAutofillingDpId, relevantDocumentChunks]);

    const getSeverityBadge = (severity: string) => {
        switch (severity?.toLowerCase()) {
            case 'high': return { variant: 'danger', icon: <AlertOctagon size={14} /> };
            case 'medium': return { variant: 'warning', icon: <AlertTriangle size={14} /> };
            case 'low': return { variant: 'info', icon: <Info size={14} /> };
            default: return { variant: 'secondary', icon: <Info size={14} /> };
        }
    };

    const toolbarItems = [
        { key: 'copilot', label: 'AI Co-Pilot', icon: <Sparkles size={20} /> },
        { key: 'summary', label: 'Narrative Summary', icon: <BookOpen size={20} /> },
        { key: 'context', label: 'Document Context', icon: <FileTextIconLucide size={20} /> },
    ];

    return (
        <>
            <ProfessionalUIDesign />
            <Modal show={show} onHide={onHide} size={"xl"} centered dialogClassName="modal-pro">
                <Modal.Body>
                    {/* Left Pane: Datapoints (Scrolling) */}
                    <div className="datapoints-pane-pro pane">
                        <div className="view-header pb-2 border-bottom mb-3">
                            <h5 className="mb-0">{selectedSubtopic?.csrd_subtopic_id}</h5>
                            <Button variant="light" size="sm" onClick={onHide} className="p-1" style={{lineHeight: 0}}><XIcon size={18}/></Button>
                        </div>
                        {loadingModalContent ? <div className="text-center p-5"><Spinner /></div>
                            : errorModalContent ? <Alert variant="danger">{errorModalContent}</Alert>
                                : selectedSubtopic?.datapoints?.length > 0 ? renderedDataPoints
                                    : <div className="d-flex flex-column align-items-center justify-content-center h-100 text-muted p-3"><Info size={40} className="mb-3 opacity-50" /><h6 className="fw-normal">No datapoints defined.</h6></div>
                        }
                    </div>

                    {/* Right Pane: Inspector (Sticky) */}
                    <div className="inspector-pane">
                        <div className="content-pane-pro">
                            <div className={`view-pane-pro ${activeView === 'copilot' ? 'visible' : ''}`}>
                                <div className="view-header">
                                    <h5 className="mb-0">AI Co-Pilot</h5>
                                    {/* ✅ REDESIGNED BUTTONS */}
                                    <div className="d-flex align-items-center">
                                        <button className="ai-button-minimal" onClick={() => focusedDatapoint?.dataResponse && handleRunAnalysis(focusedDatapoint.dataResponse, `Datapoint: ${focusedDatapoint.dataPointName}`)} disabled={isAnalyzing || !focusedDatapoint?.dataResponse}>
                                            <BrainCircuit size={16} /> Datapoint
                                        </button>
                                        <div className="vr mx-2"></div>
                                        <button className="ai-button-minimal primary" onClick={() => summary?.generatedText && handleRunAnalysis(summary.generatedText, 'Analysis of Summary')} disabled={isAnalyzing || !summary?.generatedText}>
                                            {isAnalyzing ? <Spinner as="span" size="sm" /> : <Sparkles size={16} />}
                                            Analyze Narrative
                                        </button>
                                    </div>
                                </div>
                                {isAnalyzing
                                    ? <div className="d-flex flex-column align-items-center justify-content-center h-100 text-muted"><Spinner /><p className="mt-3">The AI is reviewing your text...</p></div>
                                    : analysisError ? <Alert variant="danger">{analysisError}</Alert>
                                        : analysisResults ? (
                                                <Accordion defaultActiveKey={['0', '1']} alwaysOpen>
                                                    <Accordion.Item eventKey="0"><Accordion.Header><SearchCheck className="me-2 text-danger"/>Anomalies ({analysisResults.anomalies.length})</Accordion.Header>
                                                        <Accordion.Body>{analysisResults.anomalies.length > 0 ? analysisResults.anomalies.map((item, i) => { const sev = getSeverityBadge(item.severity); return (
                                                            <Card key={`anom-${i}`} className="mb-2" style={{borderLeft: `3px solid var(--bs-${sev.variant})`}}><Card.Body className='p-2'><div className="d-flex justify-content-between"><BsBadge pill bg={sev.variant + '-subtle'} text={sev.variant}>{sev.icon} {item.severity}</BsBadge><span className="small text-muted">{item.anomaly_type}</span></div><p className="small mb-0 mt-1">{item.description}</p></Card.Body></Card>
                                                        )}) : <p className="text-muted small fst-italic p-2">No anomalies found.</p>}
                                                        </Accordion.Body>
                                                    </Accordion.Item>
                                                    <Accordion.Item eventKey="1"><Accordion.Header><FileTextIconLucide className="me-2 text-warning"/>Coverage Gaps ({analysisResults.coverage_gaps.length})</Accordion.Header>
                                                        <Accordion.Body>{analysisResults.coverage_gaps.length > 0 ? analysisResults.coverage_gaps.map((item, i) => { const sev = getSeverityBadge(item.severity); return (
                                                            <Card key={`gap-${i}`} className="mb-2" style={{borderLeft: `3px solid var(--bs-${sev.variant})`}}><Card.Body className='p-2'><div className="d-flex justify-content-between"><BsBadge pill bg={sev.variant + '-subtle'} text={sev.variant}>{sev.icon} {item.severity}</BsBadge><span className="small text-muted">{item.concern_type}</span></div><p className="small mb-0 mt-1">{item.description}</p></Card.Body></Card>
                                                        )}) : <p className="text-muted small fst-italic p-2">No coverage gaps found.</p>}
                                                        </Accordion.Body>
                                                    </Accordion.Item>
                                                </Accordion>
                                            )
                                            : <CoPilotPlaceholder />
                                }
                            </div>
                            <div className={`view-pane-pro d-flex flex-column ${activeView === 'summary' ? 'visible' : ''}`}>
                                <div className="view-header flex-shrink-0">
                                    <h5 className="mb-0">Narrative Summary</h5>
                                    <button className="ai-button-minimal" onClick={handleGenerateSummaryClick} disabled={isLoadingSummary || isAnalyzing}>
                                        <Wand2 size={16}/>
                                        {summary?.generatedText ? 'Regenerate' : 'Generate'}
                                    </button>
                                </div>
                                <div className="flex-grow-1 overflow-y-auto p-3 bg-white border rounded-2">
                                    {isLoadingSummary ? <div className="text-center pt-5"><Spinner /></div>
                                        : summaryError ? <Alert variant="danger">{summaryError}</Alert>
                                            : summary?.generatedText ? <div style={{ whiteSpace: 'pre-wrap' }}>{summary.generatedText}</div>
                                                : <div className="d-flex flex-column align-items-center justify-content-center h-100 text-muted"><Info size={40} className="mb-3 opacity-50" /><h6 className="fw-normal">No summary generated.</h6><p className="small">Click 'Generate' to create a summary.</p></div>
                                    }
                                </div>
                            </div>
                            <div className={`view-pane-pro ${activeView === 'context' ? 'visible' : ''}`}>
                                <div className="view-header"><h5 className="mb-0">Relevant Document Context</h5></div>
                                {loadingChunks ? <div className="text-center"><Spinner size="sm" /></div>
                                    : errorChunks ? <Alert variant="warning">{errorChunks}</Alert>
                                        : focusedDatapointId && relevantDocumentChunks?.length > 0 ? (
                                            <div>{relevantDocumentChunks.map((chunk) => (
                                                <Card key={chunk.id} className="mb-2"><Card.Body className="p-2 small">{chunk.documentName && (<p className="small text-muted mb-1"><em>Source: {chunk.documentName}</em></p>)}<p className="mb-1">{chunk.chunkSummary || chunk.chunkText}</p></Card.Body></Card>
                                            ))}</div>
                                        ) : (<div className="d-flex flex-column align-items-center justify-content-center h-100 text-muted"><Info size={40} className="mb-3 opacity-50" /><h6 className="fw-normal">{focusedDatapointId ? "No relevant context found." : "Select a datapoint to see context."}</h6></div>)
                                }
                            </div>
                        </div>
                        <div className="vertical-toolbar">
                            {toolbarItems.map(item => {
                                const isContextButton = item.key === 'context';
                                const showContextBadge = isContextButton && focusedDatapointId && relevantDocumentChunks?.length > 0;

                                return (
                                    <OverlayTrigger key={item.key} placement="left" overlay={<Tooltip>{item.label}</Tooltip>}>
                                        <button
                                            className={`toolbar-btn-vertical ${activeView === item.key ? 'active' : ''}`}
                                            onClick={() => setActiveView(item.key as any)}
                                        >
                                            {item.icon}
                                            {showContextBadge && (
                                                <BsBadge
                                                    pill
                                                    bg="danger"
                                                    className="position-absolute top-0 start-100 translate-middle"
                                                    style={{ fontSize: '0.6em', padding: '0.4em 0.6em', border: '2px solid #f9fafb' }}
                                                >
                                                    {relevantDocumentChunks.length}
                                                </BsBadge>
                                            )}
                                        </button>
                                    </OverlayTrigger>
                                );
                            })}
                        </div>
                    </div>
                </Modal.Body>
            </Modal>
        </>
    );
};

export default SubtopicDetailModal;