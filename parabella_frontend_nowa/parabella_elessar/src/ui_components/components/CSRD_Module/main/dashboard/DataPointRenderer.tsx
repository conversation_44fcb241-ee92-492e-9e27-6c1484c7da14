import React, { useState, ChangeEvent, useEffect, useMemo, useRef } from 'react';
import { Form, InputGroup, Card, Badge, Button, <PERSON>ert, <PERSON>dal, Spinner, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { EsrsDatapoint } from '../modules/types.ts';
import {
    Type as TypeIconLucide,
    Tag,
    GitBranch,
    FileText as FileTextIconLucideReal,
    Hash,
    Percent as PercentIcon,
    CalendarDays,
    Table2,
    ToggleLeft,
    HelpCircle,
    Info,
    DollarSign,
    Thermometer,
    Ruler,
    Database,
    Sigma,
    Palette,
    Wand2,
    Sparkles,
    Edit3
} from 'lucide-react';
import {TextareaAutosize} from "@mui/material";

const FileTextIconLucide = FileTextIconLucideReal;


const NAN_SENTINEL_VALUES = ["Not Applicable / Value is NaN", "NaN", "N/A", "Not Applicable"];
const isEffectivelyNaNValue = (value: string | null | undefined): boolean => {
    if (value === null || value === undefined) return false;
    const trimmedValue = value.trim();
    return NAN_SENTINEL_VALUES.some(sentinel => sentinel.toLowerCase() === trimmedValue.toLowerCase());
};

const normalizeDataType = (rawDataType: string | null | undefined): string => {
    const lowerType = rawDataType?.toLowerCase().trim();
    if (!lowerType || lowerType === 'nan' || lowerType === '') {
        return 'narrative';
    }
    return lowerType;
};

const getDataTypeIcon = (dataType: string, dataUnit?: string | null): React.ReactNode => {
    switch (dataType) {
        case 'narrative': case 'semi-narrative': case 'text': case 'mdr-a': case 'mdr-t': return <TypeIconLucide size={16} />;
        case 'integer': case 'decimal': return <Sigma size={16} />;
        case 'monetary': return <DollarSign size={16} />;
        case 'mass': case 'volume': return <Ruler size={16} />;
        case 'energy': return <Thermometer size={16} />;
        case 'intensity': return <Thermometer size={16} />;
        case 'ghgemissions': return <Ruler size={16} />;
        case 'mdr-p': return <PercentIcon size={16} />;
        case 'percent': return <PercentIcon size={16} />;
        case 'date': case 'gyear': return <CalendarDays size={16} />;
        case 'boolean': return <ToggleLeft size={16} />;
        case 'alternative': return <Palette size={16} />;
        case 'table': return <Table2 size={16} />;
        default: return <Database size={16} />;
    }
};

interface DataPointRendererProps {
    datapoint: EsrsDatapoint;
    onChange?: (id: number, field: keyof EsrsDatapoint | string, value: any) => void;
    onFocus?: (datapointId: number) => void;
    onAutofill?: () => Promise<void>;
    isAutofilling?: boolean;
    isFocused?: boolean;
    isReadOnly?: boolean;
}

interface MockTableEditorModalProps {
    show: boolean;
    onHide: () => void;
    datapointName: string;
    initialData: string;
    onSave: (newData: string) => void;
    isReadOnly?: boolean;
}
const MockTableEditorModal: React.FC<MockTableEditorModalProps> = ({ show, onHide, datapointName, initialData, onSave, isReadOnly }) => {
    const [tableJson, setTableJson] = useState(initialData);
    useEffect(() => setTableJson(initialData), [initialData]);

    const handleSave = () => {
        try {
            JSON.parse(tableJson);
            onSave(tableJson);
            onHide();
        } catch (e) {
            alert("Invalid JSON format for table data. Please provide a valid JSON array of objects.");
        }
    };
    return (
        <Modal show={show} onHide={onHide} size="lg" centered backdrop="static">
            <Modal.Header closeButton>
                <Modal.Title><Table2 size={20} className="me-2" />Edit Table: {datapointName}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Alert variant="info" className="small d-flex align-items-center">
                    <Info size={18} className="me-2 flex-shrink-0"/>
                    <span>
                        Enter table data as a JSON array of objects. Each object represents a row.
                        Example: <code>{`[{"Column A": "Value1", "Column B": 10}, {"Column A": "Value2", "Column B": 20}]`}</code>
                    </span>
                </Alert>
                <Form.Control
                    as="textarea"
                    rows={12}
                    value={tableJson}
                    onChange={(e) => setTableJson(e.target.value)}
                    placeholder='e.g., [{"Header 1": "Row 1 Cell 1", "Header 2": "Row 1 Cell 2"}]'
                    className="font-monospace fs-sm"
                    readOnly={isReadOnly}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button variant="outline-secondary" onClick={onHide}>Cancel</Button>
                {!isReadOnly && <Button variant="primary" onClick={handleSave}>Save Table Data</Button>}
            </Modal.Footer>
        </Modal>
    );
};

const DataPointRenderer: React.FC<DataPointRendererProps> = ({
                                                                 datapoint,
                                                                 onChange,
                                                                 onFocus,
                                                                 onAutofill,
                                                                 isAutofilling = false,
                                                                 isFocused = false,
                                                                 isReadOnly = false,
                                                             }) => {
    const [currentValue, setCurrentValue] = useState<string>('');
    const [showTableEditor, setShowTableEditor] = useState(false);
    const cardRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        setCurrentValue(String(datapoint.dataResponse ?? ''));
    }, [datapoint.dataResponse]);

    useEffect(() => {
        if (isFocused && cardRef.current) {
            cardRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }, [isFocused]);

    const handleValueChange = (newValue: any, field: keyof EsrsDatapoint | string = 'dataResponse') => {
        const finalValue = typeof newValue === 'boolean' ? newValue.toString() : newValue;
        setCurrentValue(finalValue);
        if (onChange) {
            onChange(datapoint.id, field, finalValue);
        }
    };

    const handleCardClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (e.target instanceof Element && (e.target.closest('button, input, textarea, select'))) {
            return;
        }
        if (onFocus) {
            onFocus(datapoint.id);
        }
    };

    const {
        id, dataPointName, dataType: rawDataType, dataUnit, sourceId,
        conditionalDp, paragraph, relatedAr, dataResponseSource
    } = datapoint;

    const dpAny = datapoint as any;
    const options = dpAny.options as string[] | undefined;
    const isRequired = dpAny.isRequired as boolean | undefined;
    const helperText = dpAny.helperText as string | undefined;

    const dataType = useMemo(() => normalizeDataType(rawDataType), [rawDataType]);
    const isMDRSource = useMemo(() => sourceId?.toUpperCase().startsWith('MDR') ?? false, [sourceId]);
    const effectiveConditionalDp = useMemo(() => (conditionalDp === true) || isMDRSource, [conditionalDp, isMDRSource]);

    const isCurrentValueEffectivelyNaN = isEffectivelyNaNValue(currentValue);
    const typeIcon = useMemo(() => getDataTypeIcon(dataType, dataUnit), [dataType, dataUnit]);
    const canAutofill = useMemo(() => !['boolean', 'alternative', 'table'].includes(dataType), [dataType]);

    const AiSourceBadge = () => {
        if (!dataResponseSource || dataResponseSource === 'MANUAL' || !currentValue) return null;
        if (dataResponseSource === 'USER_DOC') {
            return (
                <OverlayTrigger placement="top" overlay={<Tooltip>Content generated by AI using your uploaded documents.</Tooltip>}>
                    <Badge pill bg="ai-doc" className="adv-ai-source-badge">
                        <FileTextIconLucide size={11} className="me-1" /> Doc AI
                    </Badge>
                </OverlayTrigger>
            );
        }
        if (dataResponseSource === 'AI_OPEN_DATA') {
            return (
                <OverlayTrigger placement="top" overlay={<Tooltip>Draft content generated by AI based on general knowledge.</Tooltip>}>
                    <Badge pill bg="ai-draft" className="adv-ai-source-badge">
                        <Sparkles size={11} className="me-1" /> AI Draft
                    </Badge>
                </OverlayTrigger>
            );
        }
        return null;
    };

    const AutofillButton = () => {
        if (!canAutofill || !onAutofill || isReadOnly) return null;
        return (
            <OverlayTrigger placement="top" overlay={<Tooltip>Autofill with AI</Tooltip>}>
                <Button variant="light" size="sm" onClick={(e) => { e.stopPropagation(); onAutofill(); }} disabled={isAutofilling} className="adv-ai-button p-1" aria-label="Autofill with AI">
                    {isAutofilling ? <Spinner as="span" animation="border" size="sm" /> : <Wand2 size={14} />}
                </Button>
            </OverlayTrigger>
        );
    };

    const EditButtonForTextarea = () => {
        if (isReadOnly) return null;
        return (
            <OverlayTrigger placement="top" overlay={<Tooltip>Edit Text</Tooltip>}>
                <Button variant="light" size="sm" onClick={(e) => { e.stopPropagation(); e.currentTarget.closest('.adv-textarea-wrapper')?.querySelector('textarea')?.focus(); }} className="adv-edit-button p-1" aria-label="Edit Text">
                    <Edit3 size={14} />
                </Button>
            </OverlayTrigger>
        );
    }

    const renderInputControls = () => {
        const commonProps: any = {
            value: currentValue,
            onChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => handleValueChange(e.target.value),
            placeholder: helperText || `Enter value`,
            className: `adv-form-control ${isCurrentValueEffectivelyNaN ? 'input-effectively-nan' : ''}`,
            readOnly: isReadOnly || isAutofilling,
            onClick: (e: React.MouseEvent) => e.stopPropagation(),
            onFocus: () => onFocus && onFocus(id),
        };

        switch (dataType) {
            case 'narrative': case 'semi-narrative': case 'text': case 'mdr-a': case 'mdr-t':
                return (
                    <div className="adv-textarea-wrapper position-relative">
                        <TextareaAutosize {...commonProps} minRows={2} maxRows={15} />
                        <div className="adv-textarea-actions">
                            <AiSourceBadge />
                            <AutofillButton />
                            <EditButtonForTextarea />
                        </div>
                    </div>
                );
            // ... other cases remain the same
            default:
                // The rest of the switch cases are unchanged. For brevity, they are omitted here,
                // but they should be included in the final code.
                return (
                    <InputGroup className="adv-input-group">
                        <InputGroup.Text><HelpCircle size={16} /></InputGroup.Text>
                        <Form.Control type="text" {...commonProps} placeholder={`${dataPointName} (Unknown Type: ${dataType})`} />
                        <AiSourceBadge />
                        <AutofillButton />
                    </InputGroup>
                );
        }
    };

    // ✅ NEW: Helper to determine the correct CSS class for focus state
    const getFocusClass = () => {
        if (!isFocused) return '';
        switch (dataResponseSource) {
            case 'USER_DOC':
                return 'adv-datapoint-focused-doc-ai';
            case 'AI_OPEN_DATA':
                return 'adv-datapoint-focused-ai-draft';
            default:
                return 'adv-datapoint-focused-manual';
        }
    };

    const getBorderClass = () => {
        if (!dataResponseSource || dataResponseSource === 'MANUAL' || !currentValue) return '';
        if (dataResponseSource === 'USER_DOC') return 'adv-datapoint-border-ai-doc';
        if (dataResponseSource === 'AI_OPEN_DATA') return 'adv-datapoint-border-ai-draft';
        return '';
    };

    return (
        <Card
            ref={cardRef}
            // ✅ UPDATED: Now uses getFocusClass() for dynamic focus styling
            className={`adv-datapoint-card ${getFocusClass()} ${effectiveConditionalDp ? 'adv-datapoint-conditional' : ''} ${isReadOnly ? 'adv-datapoint-readonly' : ''} ${getBorderClass()}`}
            onClick={handleCardClick}
            role={onFocus ? 'button' : undefined}
            tabIndex={onFocus && !isFocused ? 0 : undefined}
            onKeyDown={(e) => { if ((e.key === 'Enter' || e.key === ' ') && onFocus && !isFocused) handleCardClick(e as any);}}
        >
            <Card.Header className="adv-datapoint-header">
                <div className="adv-datapoint-title-wrapper">
                    {effectiveConditionalDp && (
                        <OverlayTrigger placement="top" overlay={<Tooltip>Conditional Datapoint</Tooltip>}>
                            <GitBranch size={18} className="adv-conditional-icon me-2 text-muted" />
                        </OverlayTrigger>
                    )}
                    <span className="adv-datapoint-name">{dataPointName}</span>
                    {isRequired && <span className="adv-required-indicator ms-1">*</span>}
                </div>
                <div className="adv-datapoint-badges">
                    {isMDRSource && <Badge pill bg="primary-soft" className="adv-data-badge me-1"><Tag size={11} className="me-1"/> MDR</Badge>}
                    <Badge pill bg="light" text="dark" className="adv-data-badge me-1">
                        {React.cloneElement(typeIcon as React.ReactElement, { size: 11, className: "me-1"})} {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
                    </Badge>
                    {dataUnit && !isEffectivelyNaNValue(dataUnit) && !['percent', 'boolean', 'table', 'alternative'].includes(dataType) && (
                        <Badge pill bg="secondary-soft" className="adv-data-badge">
                            <Hash size={11} className="me-1"/> {dataUnit}
                        </Badge>
                    )}
                </div>
            </Card.Header>

            <Card.Body className="adv-datapoint-body">
                {renderInputControls()}
                {helperText && <Form.Text muted id={`dp-helper-${id}`} className="adv-helper-text d-block mt-2 small fst-italic">{helperText}</Form.Text>}
            </Card.Body>

            {(paragraph || relatedAr) && (
                <Card.Footer className="adv-datapoint-footer">
                    <div className="adv-metadata-refs">
                        {paragraph && <small title={`Paragraph Reference: ${paragraph}`}><FileTextIconLucide size={12} className="me-1" />Ref: {paragraph.substring(0, 20)}{paragraph.length > 20 ? '...' : ''}</small>}
                        {relatedAr && <small title={`AR Reference: ${relatedAr}`} className="ms-2"><GitBranch size={12} className="me-1" />AR: {relatedAr.substring(0, 10)}{relatedAr.length > 10 ? '...' : ''}</small>}
                    </div>
                </Card.Footer>
            )}
        </Card>
    );
};

export default DataPointRenderer;