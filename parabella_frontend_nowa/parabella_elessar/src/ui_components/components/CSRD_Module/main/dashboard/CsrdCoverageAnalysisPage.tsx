// src/modules/csrd/components/dashboard/CsrdCoverageAnalysisPage.tsx
import React, { useState, useEffect, useMemo } from 'react';
import {
    Container,
    Row,
    Col,
    Card,
    Spinner,
    Alert,
    ProgressBar,
    Table,
    Badge,
    Accordion,
    Tooltip,
    OverlayTrigger,
    Form,
    InputGroup,
    Button,
} from 'react-bootstrap';
import {
    CheckCircle,
    XCircle,
    ClipboardList,
    Target,
    Activity,
    TrendingUp,
    Search,
    Info,
    FileText,
} from 'lucide-react';
import { useCsrdProjectContext } from '../../context/CsrdProjectProvider';


import '../css/CsrdCoverageAnalysisPage.css';
import {CoverageAnalysisResultDto, CoverageDatapointDto} from "../../api/csrdApiTypes.tsx";
import {fetchCoverageAnalysis} from "../../api/csrdApi.ts";

// Enhanced color palette for standards
const standardColors: Record<string, string> = {
    'ESRS E1': '#2E8B57', // SeaGreen
    'ESRS E2': '#20B2AA', // LightSeaGreen
    'ESRS E3': '#008B8B', // DarkCyan
    'ESRS E4': '#4682B4', // SteelBlue
    'ESRS E5': '#6A5ACD', // SlateBlue
    'ESRS S1': '#FF8C00', // DarkOrange
    'ESRS S2': '#FFA500', // Orange
    'ESRS S3': '#FF4500', // OrangeRed
    'ESRS S4': '#D2691E', // Chocolate
    'ESRS G1': '#B22222', // Firebrick
    'General': '#708090', // SlateGray
    'ESRS 2': '#495057' // A general one for "ESRS 2"
};

const getStandardColor = (standard: string | null): string => {
    if (!standard) return standardColors['General'];
    const matchedKey = Object.keys(standardColors).find(key => standard.includes(key.replace('ESRS ', '')));
    return matchedKey ? standardColors[matchedKey] : standardColors['General'];
};


const CsrdCoverageAnalysisPage: React.FC = () => {
    const { currentProject } = useCsrdProjectContext();
    const [analysisResult, setAnalysisResult] = useState<CoverageAnalysisResultDto | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [filterStatus, setFilterStatus] = useState<string>('all');
    const [filterStandard, setFilterStandard] = useState<string>('all');
    const [searchTerm, setSearchTerm] = useState<string>('');

    useEffect(() => {
        // Mock data for demonstration - REMOVE for production


        if (currentProject && currentProject.id) {
            setIsLoading(false); // Using mock


            // UNCOMMENT FOR PRODUCTION API CALL
            setIsLoading(true);
            setError(null);
            setAnalysisResult(null);
            fetchCoverageAnalysis(currentProject.id)
                .then(data => {
                    setAnalysisResult(data);
                })
                .catch(err => {
                    console.error("Error fetching coverage:", err);
                    setError(err.message || 'Failed to load coverage analysis.');
                })
                .finally(() => {
                    setIsLoading(false);
                });
        } else {
            setIsLoading(false); // Using mock
            setError("Please select a project to view coverage analysis."); // Set mock error
            setAnalysisResult(null);
        }
    }, [currentProject]);

    const filteredDatapoints = useMemo(() => {
        if (!analysisResult?.datapoints) return [];
        return analysisResult.datapoints.filter(dp =>
            (filterStatus === 'all' || (filterStatus === 'covered' && dp.covered) || (filterStatus === 'uncovered' && !dp.covered)) &&
            (filterStandard === 'all' || dp.esrsStandard === filterStandard) &&
            (searchTerm === '' || dp.dataPointName?.toLowerCase().includes(searchTerm.toLowerCase()) || dp.disclosureRequirement?.toLowerCase().includes(searchTerm.toLowerCase()) || dp.esrsStandard?.toLowerCase().includes(searchTerm.toLowerCase()))
        );
    }, [analysisResult?.datapoints, filterStatus, filterStandard, searchTerm]);

    const uniqueStandards = useMemo(() => {
        if (!analysisResult?.coverageByStandard) return [];
        return Object.keys(analysisResult.coverageByStandard).sort();
    }, [analysisResult?.coverageByStandard]);


    const renderProgressBar = (value: number, isOverall: boolean = false) => {
        let variant: string;
        if (isOverall) variant = 'info';
        else if (value > 70) variant = 'success';
        else if (value > 40) variant = 'warning';
        else variant = 'danger';

        return (
            <ProgressBar
                now={value}
                variant={variant}
                className="progress-bar-modern"
                label={isOverall ? `${value.toFixed(1)}%` : undefined}
            />
        );
    };

    const renderCoveredDocsTooltip = (datapoint: CoverageDatapointDto) => (
        <Tooltip id={`tooltip-docs-${datapoint.esrsDatapointId}`}>
            {datapoint.coveringDocumentNames?.length > 0 ? (
                <>
                    <strong>Covered by:</strong>
                    <ul className="list-unstyled mb-0 mt-1 small">
                        {datapoint.coveringDocumentNames.map((name, idx) => (
                            <li key={idx}><FileText size={12} className='me-1'/>{name}</li>
                        ))}
                    </ul>
                </>
            ) : "Details not available"}
        </Tooltip>
    );

    if (isLoading) {
        return (
            <div className="text-center py-5">
                <Spinner animation="border" role="status" variant="primary">
                    <span className="visually-hidden">Loading...</span>
                </Spinner>
                <p className="mt-2 text-muted">Loading Coverage Analysis...</p>
            </div>
        );
    }

    if (error && !currentProject) { // Only show if no project is selected
        return (
            <Card className="h-100">
                <Card.Body className="d-flex flex-column justify-content-center align-items-center text-center text-muted">
                    <Info size={40} className="mb-3 opacity-50"/>
                    <h5 className="mb-1">Coverage Analysis</h5>
                    <p>Please select a project to view the analysis.</p>
                </Card.Body>
            </Card>
        );
    }

    if (error) {
        return <Alert variant="danger">Error: {error}</Alert>;
    }

    if (!analysisResult) {
        return (
            <Card className="h-100">
                <Card.Body className="d-flex flex-column justify-content-center align-items-center text-center text-muted">
                    <Info size={40} className="mb-3 opacity-50"/>
                    <h5 className="mb-1">No Data Available</h5>
                    <p>Analysis data could not be loaded for this project.</p>
                </Card.Body>
            </Card>
        );
    }


    return (
        <div className="coverage-analysis-page">
            <Row className="mb-4 align-items-center">
                <Col>
                    <h2 className="mb-1">CSRD Coverage Analysis</h2>
                    <p className="text-muted mb-0">
                        Overview of required ESRS data points for project: <strong>{currentProject?.projectName}</strong>
                    </p>
                </Col>
            </Row>

            <Card className="shadow-sm mb-4">
                <Card.Body className="p-3">
                    <Row className="align-items-center">
                        <Col md={3} className="text-center text-md-start mb-2 mb-md-0">
                            <span className="text-muted small">Overall Coverage</span>
                            <h3 className="mb-0">{analysisResult.overallCoveragePercentage.toFixed(1)}%</h3>
                        </Col>
                        <Col md={9}>
                            {renderProgressBar(analysisResult.overallCoveragePercentage, true)}
                        </Col>
                    </Row>
                </Card.Body>
            </Card>

            <Row className="mb-4 g-3">
                <Col md={6} lg={4}>
                    <Card className="stat-card h-100">
                        <Card.Body>
                            <Target size={24} className="text-primary" />
                            <div className="stat-content">
                                <span className="stat-value">{analysisResult.totalEsrsDatapoints.toLocaleString()}</span>
                                <span className="stat-label">Total Required Datapoints</span>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} lg={4}>
                    <Card className="stat-card h-100">
                        <Card.Body>
                            <CheckCircle size={24} className="text-success"/>
                            <div className="stat-content">
                                <span className="stat-value">{analysisResult.totalCoveredDatapoints.toLocaleString()}</span>
                                <span className="stat-label">Covered Datapoints</span>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={12} lg={4}>
                    <Card className="stat-card h-100">
                        <Card.Body>
                            <XCircle size={24} className="text-danger" />
                            <div className="stat-content">
                                <span className="stat-value">{analysisResult.totalUncoveredDatapoints.toLocaleString()}</span>
                                <span className="stat-label">Uncovered Datapoints</span>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Accordion defaultActiveKey="0" className="mb-4 modern-accordion">
                <Accordion.Item eventKey="0">
                    <Accordion.Header>
                        <Activity size={18} className="me-2"/> Coverage Breakdown by ESRS Standard
                    </Accordion.Header>
                    <Accordion.Body>
                        <Row className="g-3">
                            {Object.entries(analysisResult.coverageByStandard)
                                .sort(([stdA], [stdB]) => stdA.localeCompare(stdB))
                                .map(([standard, stats]) => (
                                    <Col key={standard} sm={6} lg={4}>
                                        <Card className="h-100 category-card" style={{ borderLeftColor: getStandardColor(standard) }}>
                                            <Card.Body>
                                                <div className="d-flex justify-content-between align-items-center mb-2">
                                                    <Card.Title className="h6 mb-0" style={{ color: getStandardColor(standard) }}>{standard}</Card.Title>
                                                    <Badge pill bg="light" text="dark" className="fw-normal">{stats.totalDatapoints} pts</Badge>
                                                </div>
                                                <Card.Text className="small text-muted mb-2">
                                                    {stats.coveredDatapoints} covered / {stats.totalDatapoints - stats.coveredDatapoints} uncovered
                                                </Card.Text>
                                                {renderProgressBar(stats.coveragePercentage)}
                                            </Card.Body>
                                        </Card>
                                    </Col>
                                ))}
                        </Row>
                    </Accordion.Body>
                </Accordion.Item>
            </Accordion>

            <Card className="shadow-sm">
                <Card.Header className="d-flex justify-content-between align-items-center flex-wrap p-3">
                    <h5 className="mb-0 me-3"><ClipboardList size={20} className="me-2"/>Detailed Datapoint Status</h5>
                    <div className="d-flex flex-wrap gap-2 align-items-center mt-2 mt-md-0">
                        <InputGroup size="sm" className="filter-input-group">
                            <InputGroup.Text><Search size={14} /></InputGroup.Text>
                            <Form.Control type="search" placeholder="Search name, req, standard..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}/>
                        </InputGroup>
                        <Form.Select size="sm" value={filterStandard} onChange={(e) => setFilterStandard(e.target.value)} className="filter-select">
                            <option value="all">All Standards</option>
                            {uniqueStandards.map(std => <option key={std} value={std}>{std}</option>)}
                        </Form.Select>
                        <Form.Select size="sm" value={filterStatus} onChange={(e) => setFilterStatus(e.target.value as 'all' | 'covered' | 'uncovered')} className="filter-select">
                            <option value="all">All Statuses</option>
                            <option value="covered">Covered</option>
                            <option value="uncovered">Uncovered</option>
                        </Form.Select>
                    </div>
                </Card.Header>
                <Card.Body className="p-0">
                    <div className="table-responsive">
                        <Table hover className="mb-0 coverage-table align-middle">
                            <thead className="table-light">
                            <tr>
                                <th className='text-center ps-3'>Status</th>
                                <th>Standard</th>
                                <th>Disclosure Requirement</th>
                                <th>Data Point Name</th>
                                <th className="text-center pe-3">Coverage</th>
                            </tr>
                            </thead>
                            <tbody>
                            {filteredDatapoints.length > 0 ? (
                                filteredDatapoints.map((dp) => (
                                    <tr key={dp.esrsDatapointId}>
                                        <td className="text-center ps-3">
                                            <OverlayTrigger placement="top" overlay={<Tooltip>{dp.covered ? 'Covered' : 'Uncovered'}</Tooltip>}>
                                                <span>
                                                    {dp.covered
                                                        ? <CheckCircle size={18} className="text-success" />
                                                        : <XCircle size={18} className="text-danger" />
                                                    }
                                                </span>
                                            </OverlayTrigger>
                                        </td>
                                        <td>
                                            <Badge pill bg="" className="standard-badge" style={{ backgroundColor: `${getStandardColor(dp.esrsStandard)}20`, color: getStandardColor(dp.esrsStandard) }}>
                                                {dp.esrsStandard || 'N/A'}
                                            </Badge>
                                        </td>
                                        <td className="small text-muted">{dp.disclosureRequirement || '-'}</td>
                                        <td className="fw-medium">{dp.dataPointName || '-'}</td>
                                        <td className="text-center pe-3">
                                            {dp.covered && dp.coveringDocumentNames?.length > 0 ? (
                                                <OverlayTrigger placement="left" overlay={renderCoveredDocsTooltip(dp)}>
                                                    <Button variant="link" size="sm" className="p-0 text-decoration-none info-icon-button">
                                                        <Info size={16} />
                                                    </Button>
                                                </OverlayTrigger>
                                            ) : (<span className="text-muted">-</span>)}
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan={5} className="text-center text-muted py-4">
                                        No data points match the current filters.
                                    </td>
                                </tr>
                            )}
                            </tbody>
                        </Table>
                    </div>
                </Card.Body>
                {filteredDatapoints.length > 0 && (
                    <Card.Footer className="text-muted small text-end py-2 px-3">
                        Showing {filteredDatapoints.length} of {analysisResult.datapoints.length} data points
                    </Card.Footer>
                )}
            </Card>
        </div>
    );
};

export default CsrdCoverageAnalysisPage;