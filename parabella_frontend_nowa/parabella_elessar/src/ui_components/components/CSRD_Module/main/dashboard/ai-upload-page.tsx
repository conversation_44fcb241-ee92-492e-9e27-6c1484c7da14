// src/modules/csrd/components/dashboard/ai-upload-page.tsx
// THIS FILE REMAINS EXACTLY AS YOU PROVIDED IN THE PROMPT.
// It will be rendered by the 'data-sources' tab in the CsrdLayout.

import React, { useState, useCallback, useEffect } from 'react';
import {
    Container,
    Row,
    Col,
    Card,
    Table,
    Spinner,
    Alert,
    ProgressBar,
    Modal,
    Button,
    ListGroup,
    Tooltip,
    OverlayTrigger,
    Badge,
} from 'react-bootstrap';
import { useDropzone } from 'react-dropzone';
import {
    UploadCloud,
    File as FileIcon,
    CheckCircle,
    XCircle,
    Loader,
    Leaf,
    Users,
    Scale,
    Compass,
    AlertCircle,
    Info,
    BarChart3,
    Hash,
    Percent,
    Sigma,
    DollarSign,
    Eye,
    RefreshCw,
    Trash2,
    FileText,
    Database,
    ListChecks,
    HelpCircle,
    Calendar
} from 'lucide-react';

// --- Context and Auth Imports ---
import { useCsrdProjectContext } from "../../context/CsrdProjectProvider.tsx"; // Adjust path if needed
import { getCurrentUser } from '../../../../../services/authService.ts'; // Adjust path if needed

// Define a simple User type - *** REPLACE 'any' with your actual User interface ***
interface User {
    id: number | string; // Ensure ID type matches what backend expects (string in this case based on Python code)
    username: string;
    // add other relevant user fields
}
// -------------------------------

import '../css/AiUploadPage.css'; // Ensure this CSS file exists
import CsrdCoverageAnalysisPage from "./CsrdCoverageAnalysisPage.tsx";
import {API_PYTHON_AI_URL} from "../../../../../config/APIEndpoints.ts"; // Import the coverage page

// --- Interfaces ---
type CategoryType = 'General' | 'Environmental' | 'Social' | 'Governance';

// Interface for extracted data points (Matches backend structure in results)
interface ExtractedDataPoint {
    id: string;
    category: CategoryType;
    metric: string;
    value: string | number;
    unit?: string;
    sourcePage?: number;
    confidence?: number;
    notes?: string;
    disclosure_requirement?: string;
    source_id?: number | string;
    data_source_ids?: (number | string)[];
    source_chunk_index?: number;
}

interface UploadedFile {
    id: number;
    file: File;
    status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
    progress: number;
    error?: string;
    uploadDate: Date;
    extractedCategories?: CategoryType[];
    extractedData?: ExtractedDataPoint[];
    backendJobId?: string;
}

interface CategoryStyle {
    name: CategoryType;
    color: string;
    icon: React.ReactNode;
}

const esgCategoryStyles: Record<CategoryType, CategoryStyle> = {
    General:       { name: 'General',       color: '#6c757d', icon: <Compass size={18} /> },
    Environmental: { name: 'Environmental', color: '#198754', icon: <Leaf size={18} /> },
    Social:        { name: 'Social',        color: '#fd7e14', icon: <Users size={18} /> },
    Governance:    { name: 'Governance',    color: '#c32020', icon: <Scale size={18} /> },
};
//https://parabella-fastapi-service-1091242934000.europe-west10.run.app

const API_UPLOAD_URL = `${API_PYTHON_AI_URL}/api/v1/jobs/extract`;

interface BackendJobContext {
    job_id: string;
    job_type: string;
    params: Record<string, any>;
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
    current_step?: string | null;
    results?: ExtractedDataPoint[] | null;
    error_message?: string | null;
    intermediate_data?: Record<string, any>;
}


const  AiUploadPage: React.FC = () => {
    const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [selectedFileDataForModal, setSelectedFileDataForModal] = useState<UploadedFile | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [pdfUrl, setPdfUrl] = useState<string | null>(null);

    const { currentProject } = useCsrdProjectContext();
    const [currentUser, setCurrentUser] = useState<User | null>(null);

    useEffect(() => {
        const user = getCurrentUser();
        if (user) {
            setCurrentUser(user);
            if (currentProject) setError(null);
        } else {
            setError("User not logged in. Please log in to upload documents.");
        }
    }, []);

    useEffect(() => {
        if (!currentProject && currentUser) {
            setError("No project selected. Please select or create a project before uploading.");
        } else if (!currentUser) {
            setError("User not logged in. Please log in to upload documents.");
        } else if (currentProject && currentUser && error && (error.includes("project") || error.includes("logged in"))) {
            setError(null);
        }
    }, [currentProject, currentUser, error]);

    useEffect(() => {
        // console.log("--- UploadedFiles State Changed ---", uploadedFiles);
    }, [uploadedFiles]);

    useEffect(() => {
        if (showDetailsModal && selectedFileDataForModal?.file) {
            const objectUrl = URL.createObjectURL(selectedFileDataForModal.file);
            setPdfUrl(objectUrl);
            console.log("Created PDF Object URL:", objectUrl);

            return () => {
                URL.revokeObjectURL(objectUrl);
                setPdfUrl(null);
                // console.log("Revoked PDF Object URL:", objectUrl);
            };
        } else {
            if (pdfUrl) {
                URL.revokeObjectURL(pdfUrl);
                setPdfUrl(null);
            }
        }
    }, [showDetailsModal, selectedFileDataForModal]);

    const handleFileUpload = useCallback(async (
        fileToUpload: UploadedFile,
        projectId: number,
        userId: string,
        options?: { use_semantic_chunking?: boolean; }
    ) => {
        // console.log(`[handleFileUpload] Starting for FE ID: ${fileToUpload.id}, File: ${fileToUpload.file.name}, Project: ${projectId}, User: ${userId}`);
        setError(null);

        setUploadedFiles((prev) =>
            prev.map((f) =>
                f.id === fileToUpload.id ? { ...f, status: 'uploading', progress: 0, error: undefined, backendJobId: undefined } : f
            )
        );

        const formData = new FormData();
        formData.append('file', fileToUpload.file);
        formData.append('projectId', projectId.toString());
        formData.append('userId', userId);
        if (options?.use_semantic_chunking !== undefined) {
            formData.append('use_semantic_chunking', String(options.use_semantic_chunking));
        }

        try {
            setUploadedFiles((prev) =>
                prev.map((f) =>
                    f.id === fileToUpload.id ? { ...f, status: 'processing', progress: 50 } : f
                )
            );

            // console.log(`[handleFileUpload] FE ID: ${fileToUpload.id} - Making fetch call to ${API_UPLOAD_URL}...`);
            const response = await fetch(API_UPLOAD_URL, { method: 'POST', body: formData });
            // console.log(`[handleFileUpload] FE ID: ${fileToUpload.id} - Fetch response received. Status: ${response.status}`);

            let result: BackendJobContext;
            let errorToThrow: Error | null = null;

            try {
                result = await response.json();
            } catch (jsonError) {
                const textError = await response.text();
                errorToThrow = new Error(`Server returned non-JSON response (Status: ${response.status}). Response: ${textError.substring(0, 200)}...`);
                result = { status: 'FAILED', error_message: errorToThrow.message } as unknown as BackendJobContext;
            }

            if (!response.ok) {
                if (!errorToThrow) {
                    const errorMsgFromServer = result?.error_message || (result as any)?.detail || `HTTP error! Status: ${response.status}`;
                    errorToThrow = new Error(errorMsgFromServer);
                }
                throw errorToThrow;
            }

            // console.log(`[handleFileUpload] FE ID: ${fileToUpload.id} - Processing result with backend status: ${result.status}`);

            if (result.status === 'COMPLETED') {
                const resultsData = Array.isArray(result.results) ? result.results : [];
                const categories = [...new Set(resultsData.map(dp => dp.category))].filter(Boolean).sort() as CategoryType[];
                setUploadedFiles((prev) =>
                    prev.map((f) =>
                        f.id === fileToUpload.id ? {
                            ...f,
                            status: 'completed' as const,
                            progress: 100,
                            error: undefined,
                            extractedCategories: categories,
                            extractedData: resultsData,
                            backendJobId: result.job_id,
                        } : f
                    )
                );
            } else if (result.status === 'FAILED') {
                throw new Error(result.error_message || 'Processing failed on the server.');
            } else {
                throw new Error(`Job ended with unexpected status: ${result.status}`);
            }

        } catch (error: any) {
            console.error(`[handleFileUpload] FE ID: ${fileToUpload.id} - CATCH block error:`, error);
            setUploadedFiles((prev) =>
                prev.map((f) =>
                    f.id === fileToUpload.id
                        ? { ...f, status: 'error', progress: 0, error: error.message || 'Upload failed.', extractedCategories: [], extractedData: [] }
                        : f
                )
            );
            setError(`Upload failed for ${fileToUpload.file.name}: ${error.message}`);
        }
    }, []);

    const onDrop = useCallback((acceptedFiles: File[]) => {
        setError(null);

        if (!currentProject || !currentProject.id) {
            setError("No project selected. Please select or create a project before uploading.");
            return;
        }
        if (!currentUser || !currentUser.id) {
            setError("User information not found. Please ensure you are logged in.");
            return;
        }
        const projectId = currentProject.id;
        const userId = String(currentUser.id);

        const newFiles: UploadedFile[] = acceptedFiles.map((file, index) => ({
            id: Date.now() + index,
            file,
            status: 'pending',
            progress: 0,
            uploadDate: new Date(),
        }));
        setUploadedFiles((prev) => [...newFiles, ...prev].sort((a, b) => b.uploadDate.getTime() - a.uploadDate.getTime()));

        newFiles.forEach((nf) => {
            handleFileUpload(nf, projectId, userId, { use_semantic_chunking: true });
        });
    }, [handleFileUpload, currentProject, currentUser]);

    const isDropzoneDisabled = !currentProject || !currentUser;
    const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({
        onDrop,
        accept: { 'application/pdf': ['.pdf'] },
        disabled: isDropzoneDisabled
    });

    const getFileExtension = (file: File): string => {
        const parts = file.name.split('.');
        return parts.length > 1 ? parts[parts.length - 1].toUpperCase() : 'N/A';
    };

    const handleRetry = (fileId: number) => {
        const fileToRetry = uploadedFiles.find((f) => f.id === fileId);
        setError(null);

        if (!currentProject || !currentProject.id) {
            setError("Cannot retry: No project selected.");
            return;
        }
        if (!currentUser || !currentUser.id) {
            setError("Cannot retry: User information not found.");
            return;
        }
        const projectId = currentProject.id;
        const userId = String(currentUser.id);

        if (fileToRetry) {
            handleFileUpload(fileToRetry, projectId, userId, { use_semantic_chunking: true });
        }
    };

    const handleDelete = (fileId: number) => {
        setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
    };

    const handleShowDetails = (fileId: number) => {
        const fileToShow = uploadedFiles.find((f) => f.id === fileId);
        if (fileToShow) {
            setSelectedFileDataForModal(fileToShow);
            setShowDetailsModal(true);
        }
    };

    const handleCloseDetails = () => {
        setShowDetailsModal(false);
        setSelectedFileDataForModal(null);
    };

    const getStatusIcon = (status: UploadedFile['status'], errorMsg?: string): React.ReactNode => {
        const iconProps = { size: 18 };
        let tooltipText = status.charAt(0).toUpperCase() + status.slice(1);
        let iconElement: React.ReactNode;
        const uniqueId = `status-${status}-${Math.random().toString(36).substring(7)}`;

        switch (status) {
            case 'uploading': iconElement = <Spinner animation="border" size="sm" variant="info" />; tooltipText = "Uploading..."; break;
            case 'processing': iconElement = <Loader {...iconProps} className="text-warning spinning" />; tooltipText = "Processing..."; break;
            case 'completed': iconElement = <CheckCircle {...iconProps} className="text-success" />; tooltipText = "Completed"; break;
            case 'error': iconElement = <XCircle {...iconProps} className="text-danger" />; tooltipText = `Error: ${errorMsg || 'Unknown error'}`; break;
            default: iconElement = <FileIcon {...iconProps} className="text-muted" />; tooltipText = "Pending"; break;
        }
        return (<OverlayTrigger placement="top" overlay={<Tooltip id={`tooltip-${uniqueId}`}>{tooltipText}</Tooltip>}><span className="d-inline-block">{iconElement}</span></OverlayTrigger>);
    };

    const getCategoryBadge = (category: CategoryType): React.ReactNode => {
        const style = esgCategoryStyles[category] || esgCategoryStyles.General;
        const uniqueId = `cat-${category}-${Math.random().toString(36).substring(7)}`;
        return (
            <span key={category} className="esg-badge" style={{ backgroundColor: `${style.color}20`, color: style.color }}>
                 {React.cloneElement(style.icon as React.ReactElement, { size: 12 })}
                <span className="ms-1">{style.name}</span>
             </span>
        );
    };

    const groupDataByCategory = (data: ExtractedDataPoint[] | undefined): Record<CategoryType, ExtractedDataPoint[]> => {
        const grouped: Record<CategoryType, ExtractedDataPoint[]> = { Environmental: [], Social: [], Governance: [], General: [] };
        if (!data) return grouped;
        const validCategories = Object.keys(grouped) as CategoryType[];
        data.forEach((item) => {
            const categoryKey = item.category && validCategories.includes(item.category) ? item.category : 'General';
            grouped[categoryKey].push(item);
        });
        return grouped;
    };

    const renderDataPointIcon = (unit?: string, metric?: string): React.ReactNode => {
        const iconProps = { size: 16, className: "text-muted me-2 flex-shrink-0" };
        if (unit) {
            const lowerUnit = unit.toLowerCase();
            if (lowerUnit.includes('%')) return <Percent {...iconProps} className="text-primary me-2 flex-shrink-0" />;
            if (lowerUnit.includes('co2') || lowerUnit.includes('ghg') || lowerUnit.includes('kg') || lowerUnit.includes('tonne')) return <Sigma {...iconProps} className="text-success me-2 flex-shrink-0" />;
            if (lowerUnit.includes('eur') || lowerUnit.includes('usd') || lowerUnit.includes('gbp') || lowerUnit.includes('$') || metric?.toLowerCase().includes('financial')) return <DollarSign {...iconProps} className="text-warning me-2 flex-shrink-0" />;
            if (lowerUnit.includes('mwh') || lowerUnit.includes('kwh') || lowerUnit.includes('gj') || lowerUnit.includes('energy')) return <Sigma {...iconProps} className="text-info me-2 flex-shrink-0" />;
            if (lowerUnit.includes('m³') || lowerUnit.includes('liter') || lowerUnit.includes('volume') || lowerUnit.includes('water')) return <Hash {...iconProps} className="text-info me-2 flex-shrink-0" />;
            if (lowerUnit.includes('hour') || lowerUnit.includes('day') || lowerUnit.includes('year')) return <Calendar {...iconProps} className="text-secondary me-2 flex-shrink-0" />;
        }
        if (metric) {
            const lowerMetric = metric.toLowerCase();
            if (lowerMetric.includes('number of') || lowerMetric.includes('count')) return <Hash {...iconProps} className="text-secondary me-2 flex-shrink-0" />;
            if (lowerMetric.includes('ratio') || lowerMetric.includes('rate') || lowerMetric.includes('percentage')) return <Percent {...iconProps} className="text-primary me-2 flex-shrink-0" />;
            if (lowerMetric.includes('emissions') || lowerMetric.includes('scope 1') || lowerMetric.includes('scope 2') || lowerMetric.includes('scope 3')) return <Sigma {...iconProps} className="text-success me-2 flex-shrink-0" />;
            if (lowerMetric.includes('financial') || lowerMetric.includes('revenue') || lowerMetric.includes('cost')) return <DollarSign {...iconProps} className="text-warning me-2 flex-shrink-0" />;
        }
        return <BarChart3 {...iconProps} />;
    };

    return (
        <>
            <Container fluid className="py-4 px-lg-5 ai-upload-page">
                <Row>
                    <Col lg={5} className="ai-upload-main-content mb-4 mb-lg-0">
                        <Row className="mb-4">
                            <Col>
                                <h2>AI Document Analysis</h2>
                                <p className="text-muted">Upload your sustainability reports (PDF). Our AI will analyze them for CSRD-relevant data points.</p>
                                {currentProject && (
                                    <p >Current Project: <strong>{currentProject.projectName}</strong></p>
                                )}
                                {!currentProject && currentUser && (
                                    <Alert variant="warning" className="small"><strong>Please select a project to enable uploads.</strong></Alert>
                                )}
                                {!currentUser && (
                                    <Alert variant="danger" className="small"><strong>Please log in to enable uploads.</strong></Alert>
                                )}
                            </Col>
                        </Row>

                        {error && (!error.includes("project") && !error.includes("logged in")) && (
                            <Row className="mb-3">
                                <Col xs={12}>
                                    <Alert variant="danger" onClose={() => setError(null)} dismissible>
                                        {error}
                                    </Alert>
                                </Col>
                            </Row>
                        )}
                        <Row>
                            <Col xs={12} className="mb-4">
                                <div
                                    {...getRootProps()}
                                    className={`dropzone-area modern-dropzone text-center p-4 p-lg-5 ${isDragActive ? 'active' : ''} ${isDragAccept ? 'accept' : ''} ${isDragReject ? 'reject' : ''} ${isDropzoneDisabled ? 'disabled-dropzone' : ''}`}
                                >
                                    <input {...getInputProps()} />
                                    <div className="dropzone-content">
                                        <div className={`dropzone-icon-bg d-inline-flex align-items-center justify-content-center rounded-circle mb-3 ${isDragActive ? 'pulsing' : ''}`}>
                                            <UploadCloud size={32} strokeWidth={1.5} />
                                        </div>
                                        {isDropzoneDisabled ? (
                                            <>
                                                <h5 className="mt-2 mb-1 text-muted">Upload Disabled</h5>
                                                <p className="text-muted mb-0">{!currentUser ? "Please log in." : "Please select a project."}</p>
                                            </>
                                        ) : isDragActive ? (
                                            isDragAccept ? <h5 className="mt-2 mb-1">Drop files here</h5> : <h5 className="mt-2 mb-1 text-danger">Invalid file type</h5>
                                        ) : (
                                            <>
                                                <h5 className="mt-2 mb-1">Drag & drop PDF files</h5>
                                                <p className="text-muted mb-0">or click to browse</p>
                                            </>
                                        )}
                                        <p className="small text-muted mt-2 mb-0">(Supports PDF only)</p>
                                    </div>
                                    {isDragReject && !isDropzoneDisabled && <Alert variant="danger" className="mt-3 small py-1 px-2 mb-0">Only PDF files accepted.</Alert>}
                                </div>
                            </Col>
                        </Row>
                        <Row>
                            <Col xs={12}>
                                <h4 className="mb-3 mt-2">Upload History</h4>
                                {uploadedFiles.length === 0 ? (
                                    <Card className="shadow-sm">
                                        <Card.Body className="text-center text-muted py-5">
                                            <Info size={40} className="mb-3 opacity-50" />
                                            <p>No files uploaded for this session yet.</p>
                                            <p className='small'>{isDropzoneDisabled ? 'Select a project and log in to start uploading.' : 'Use the area above to upload documents.'}</p>
                                        </Card.Body>
                                    </Card>
                                ) : (
                                    <div className="uploads-list-container">
                                        {uploadedFiles.map((f) => (
                                            <Card key={f.id} className="mb-3 upload-list-item">
                                                <Card.Body>
                                                    <div className="d-flex align-items-center flex-wrap">
                                                        <div className="upload-item-status me-3">{getStatusIcon(f.status, f.error)}</div>
                                                        <div className="upload-item-icon me-3">
                                                            <FileIcon size={32} className="text-danger" />
                                                            <span className='file-ext-badge'>{getFileExtension(f.file)}</span>
                                                        </div>
                                                        <div className="upload-item-details flex-grow-1">
                                                            <div className="file-name text-truncate" title={f.file.name}>{f.file.name}</div>
                                                            <div className="file-meta">
                                                                <span>{(f.file.size / (1024 * 1024)).toFixed(2)} MB</span>
                                                                <span className="mx-2">|</span>
                                                                <span>{f.uploadDate.toLocaleString()}</span>
                                                            </div>
                                                            {['uploading', 'processing'].includes(f.status) && <ProgressBar now={f.status === 'uploading' ? 25 : 75} variant={f.status === 'processing' ? 'warning' : 'info'} animated className="mt-2" />}
                                                            {f.status === 'error' && <div className="text-danger small mt-1 text-truncate" title={f.error}><AlertCircle size={14} className="me-1"/> {f.error}</div>}
                                                        </div>
                                                        <div className="upload-item-tags mx-3 my-2 my-md-0">
                                                            {f.status === 'completed' && f.extractedCategories && f.extractedCategories.length > 0
                                                                ? <div className="d-flex flex-wrap gap-2">{f.extractedCategories.map(cat => getCategoryBadge(cat))}</div>
                                                                : f.status === 'completed' ? <span className="text-muted small fst-italic">No categories</span>
                                                                    : !['error', 'pending'].includes(f.status) && <Spinner animation="border" size="sm" variant="secondary" />
                                                            }
                                                        </div>
                                                        <div className="upload-item-actions d-flex align-items-center">
                                                            <OverlayTrigger placement="top" overlay={<Tooltip>View Details & PDF</Tooltip>}>
                                                                <Button variant="link" className="p-0 me-2 text-primary action-icon-btn" onClick={() => handleShowDetails(f.id)} >
                                                                    <Eye size={18} />
                                                                </Button>
                                                            </OverlayTrigger>
                                                            {f.status === 'error' && (
                                                                <OverlayTrigger placement="top" overlay={<Tooltip>Retry Upload</Tooltip>}>
                                                                    <Button variant="link" className="p-0 me-2 text-warning action-icon-btn" onClick={() => handleRetry(f.id)} disabled={isDropzoneDisabled} >
                                                                        <RefreshCw size={18} />
                                                                    </Button>
                                                                </OverlayTrigger>
                                                            )}
                                                            <OverlayTrigger placement="top" overlay={<Tooltip>Delete Upload</Tooltip>}>
                                                                <Button variant="link" className="p-0 text-danger action-icon-btn" onClick={() => handleDelete(f.id)}>
                                                                    <Trash2 size={18} />
                                                                </Button>
                                                            </OverlayTrigger>
                                                        </div>
                                                    </div>
                                                </Card.Body>
                                            </Card>
                                        ))}
                                    </div>
                                )}
                            </Col>
                        </Row>
                    </Col>
                    <Col lg={7} className="csrd-coverage-sidebar">
                        <CsrdCoverageAnalysisPage />
                    </Col>
                </Row>
            </Container>

            <Modal show={showDetailsModal} onHide={handleCloseDetails} size="xl" dialogClassName="wide-modal" centered scrollable>
                <Modal.Header closeButton>
                    <Modal.Title className="d-flex align-items-center fs-5">
                        <FileIcon size={20} className="me-2 text-primary" />
                        Document View: <span className="fw-normal ms-1 text-muted">{selectedFileDataForModal?.file.name}</span>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body className="p-0 bg-light">
                    <Row className="g-0">
                        <Col md={6} className="pdf-pane border-end">
                            {pdfUrl ? (
                                <iframe src={pdfUrl} style={{ width: '100%', height: '100%', border: 'none' }} title="PDF Preview" />
                            ) : (
                                <div className="d-flex align-items-center justify-content-center h-100 text-muted">
                                    {selectedFileDataForModal?.file ? (
                                        <>
                                            <Spinner animation="border" variant="secondary" size="sm" className="me-2"/>
                                            <span>Loading PDF preview...</span>
                                        </>
                                    ) : (
                                        <span>No PDF file selected or available.</span>
                                    )}
                                </div>
                            )}
                        </Col>
                        <Col md={6} className="data-pane">
                            <div className="p-4">
                                {selectedFileDataForModal?.status === 'completed' && selectedFileDataForModal?.extractedData && selectedFileDataForModal.extractedData.length > 0 ? (
                                    <>
                                        {Object.entries(groupDataByCategory(selectedFileDataForModal.extractedData))
                                            .filter(([, dataPoints]) => dataPoints.length > 0)
                                            .sort(([catA], [catB]) => {
                                                const order: CategoryType[] = ['General', 'Environmental', 'Social', 'Governance'];
                                                return order.indexOf(catA as CategoryType) - order.indexOf(catB as CategoryType);
                                            })
                                            .map(([category, dataPoints]) => {
                                                const style = esgCategoryStyles[category as CategoryType] || esgCategoryStyles.General;
                                                return (
                                                    <Card key={category} className="mb-4 shadow-sm border-start border-5 modern-card" style={{ borderColor: `${style.color} !important` }}>
                                                        <Card.Header className="d-flex align-items-center justify-content-between py-2 px-3">
                                                            <div className="d-flex align-items-center">
                                                                <span className="me-2" style={{ color: style.color }}>{React.cloneElement(style.icon as React.ReactElement, { size: 20 })}</span>
                                                                <h6 className="mb-0 fw-semibold">{style.name} Data Points</h6>
                                                            </div>
                                                            <Badge pill bg="secondary">{dataPoints.length}</Badge>
                                                        </Card.Header>
                                                        <ListGroup variant="flush" className="data-point-list">
                                                            {dataPoints.map((dp, idx) => (
                                                                <ListGroup.Item key={dp.id || idx} className="py-3 px-3">
                                                                    <Row className="align-items-start gy-2">
                                                                        <Col xs={12} lg={6}>
                                                                            <div className="d-flex align-items-start mb-1">
                                                                                {renderDataPointIcon(dp.unit, dp.metric)}
                                                                                <span className="fw-medium">{dp.metric || 'N/A'}</span>
                                                                            </div>
                                                                            <div className="ps-4 ms-2">
                                                                                <strong className="data-value fs-5 me-1">{dp.value}</strong>
                                                                                {dp.unit && <small className="text-muted">{dp.unit}</small>}
                                                                            </div>
                                                                        </Col>
                                                                        <Col xs={12} lg={2}>
                                                                            <div className="d-flex align-items-center text-muted mb-1 small"><FileText size={14} className="me-2 flex-shrink-0"/> Disclosure</div>
                                                                            <OverlayTrigger placement="top" overlay={<Tooltip>{dp.disclosure_requirement || 'Not specified'}</Tooltip>}>
                                                                                <span className="d-block ps-4 ms-1 text-dark text-truncate">{dp.disclosure_requirement || '-'}</span>
                                                                            </OverlayTrigger>
                                                                        </Col>
                                                                        <Col xs={12} lg={2}>
                                                                            <div className="d-flex align-items-center text-muted mb-1 small"><Database size={14} className="me-2 flex-shrink-0"/> Source ID</div>
                                                                            <span className="d-block ps-4 ms-1">
                                                                                <Badge bg="light" text="dark" className="font-monospace fw-normal">{dp.source_id ? `${dp.source_id}` : '-'}</Badge>
                                                                            </span>
                                                                        </Col>
                                                                        <Col xs={12} lg={2}>
                                                                            <div className="d-flex align-items-center text-muted mb-1 small"><HelpCircle size={14} className="me-2 flex-shrink-0"/> Context</div>
                                                                            <div className="ps-4 ms-1 d-flex flex-wrap gap-1 align-items-center">
                                                                                {dp.confidence != null && <OverlayTrigger placement="top" overlay={<Tooltip>Extraction Confidence</Tooltip>}><Badge pill bg={dp.confidence > 0.7 ? "success-subtle" : "warning-subtle"} text={dp.confidence > 0.7 ? "success-emphasis" : "warning-emphasis"}>{(dp.confidence * 100).toFixed(0)}%</Badge></OverlayTrigger>}
                                                                                {dp.sourcePage != null && <OverlayTrigger placement="top" overlay={<Tooltip>Source Page</Tooltip>}><Badge pill bg="secondary-subtle" text="secondary-emphasis">P. {dp.sourcePage}</Badge></OverlayTrigger>}
                                                                                {dp.notes && <OverlayTrigger placement="top" overlay={<Tooltip>Notes: {dp.notes}</Tooltip>}><Info size={14} className="text-muted cursor-help ms-1" style={{cursor: 'help'}}/></OverlayTrigger>}
                                                                            </div>
                                                                        </Col>
                                                                    </Row>
                                                                </ListGroup.Item>
                                                            ))}
                                                        </ListGroup>
                                                    </Card>
                                                );
                                            })}
                                    </>
                                ) : (
                                    <Alert variant="info" className="text-center d-flex align-items-center justify-content-center py-4 mt-3">
                                        <Info size={24} className="me-3" />
                                        <div>
                                            {selectedFileDataForModal?.status === 'completed'
                                                ? 'No specific data points were extracted from this document.'
                                                : selectedFileDataForModal?.status === 'error'
                                                    ? `Processing failed: ${selectedFileDataForModal.error || 'Unknown error'}`
                                                    : 'Data details are available after successful processing.'
                                            }
                                        </div>
                                    </Alert>
                                    )}
                                    </div>
                                    </Col>
                                    </Row>
                                    </Modal.Body>
                                    <Modal.Footer className="bg-light border-top-0">
                                    <Button variant="outline-secondary" onClick={handleCloseDetails}>Close</Button>
                        </Modal.Footer>
            </Modal>
        </>
);
};

export default AiUploadPage;