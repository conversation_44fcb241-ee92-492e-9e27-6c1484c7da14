// src/modules/csrd/types/csrdTypes.ts (or types/common.ts)

// --- Re-defined with camelCase ---
export interface CompanyInfo {
    // id?: number; // Optional: if needed from response
    companyName: string; // Changed from company_name
    revenue: string;
    industry: string;
    size: string;
    numberOfEmployees: string; // Changed from number_of_employees
}

export interface CsrdProject {
    id: number;
    userId: number;
    projectName: string; // Already camelCase
    projectDescription: string; // Already camelCase
    projectType: 'COMPANY' | 'COMPANY_GROUP'; // Match backend Enum/String format
    createdAt: string; // ISO Date string
    updatedAt: string; // ISO Date string
    companyInfo?: CompanyInfo | null; // Use updated CompanyInfo type, allow null
}
// --- End Re-defined types ---


// Define the state managed by the CsrdProjectProvider
export interface CsrdProjectContextState {
    currentProject: CsrdProject | null;
    // companyInfo is now part of currentProject, maybe remove separate state?
    // For clarity, let's keep it separate but ensure it's loaded correctly.
    companyInfo: CompanyInfo | null;
    userCsrdProjects: CsrdProject[];
    isLoading: boolean;
    error: string | null; // Store error messages
}

// Define the value provided by the context, including actions
export interface CsrdProjectContextValue extends CsrdProjectContextState {
    selectCsrdProject: (project: CsrdProject | null) => Promise<void>; // Make async for potential data fetching
    loadUserCsrdProjects: (userId: number) => Promise<void>;
    // Update payload type to match backend DTO structure (use CsrdProjectDTO fields)
    createCsrdProject: (projectData: {
        userId: number;
        projectName: string;
        projectDescription: string;
        projectType: 'company' | 'companyGroup'; // Frontend uses lowercase for input convenience
    }) => Promise<CsrdProject | null>;
    // saveCompanyInfo now needs the projectId
    saveCompanyInfo: (projectId: number, info: CompanyInfo) => Promise<void>;
    setCurrentProjectDirectly: (project: CsrdProject) => void;
    clearError: () => void; // Helper to clear errors
}



export interface EsrsDatapoint {
    id: number;
    sourceId?: string | null;
    dataPointName: string;
    disclosureRequirement?: string | null;
    dataType?: string | null;
    dataUnit?: string | null;
    dataResponse?: string | null;
    // --- NEW FIELD ---
    dataResponseSource?: 'USER_DOC' | 'AI_OPEN_DATA' | 'MANUAL' | null;
    // ... any other existing fields
    conditionalDp?: boolean;
    paragraph?: string;
    relatedAr?: string;
    options?: string[];
    isRequired?: boolean;
    helperText?: string;
}


export interface CsrdSubtopic {
    id: number;
    csrd_subtopic_id: string;
    csrd_subtopic_label: string;
    datapoints: EsrsDatapoint[];
    autoFillData?: Record<string, any>; // For client-side AI data from old mechanism
}

export interface CsrdTopic {
    id: number;
    topicCode: string;    // Changed from 'code'
    topicLabel: string;   // Changed from 'name'
    subtopics: CsrdSubtopic[];
}


export interface CsrdTopic {
    id: number;
    code: string;
    name: string;
    subtopics: CsrdSubtopic[];
}

// For handling input changes if you implement editing
export interface DataPointChangeEvent {
    datapointId: number;
    subtopicId: number;
    topicId: number;
    newValue: string;
}

// Enum matching backend JobStatus (ensure it's consistent)
export enum JobStatusApi {
    PENDING = "PENDING",
    RUNNING = "RUNNING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    UNKNOWN = "UNKNOWN", // If backend might return this for jobs not in scheduler
}

// Type for an item in the reports list on ReportsPage (simplified for now)
export interface ReportListItem { // Renamed from ReportListItemDTO for clarity on frontend
    job_id: string;
    display_name: string; // A simple name for display
    status: JobStatusApi;
    generated_at: string; // ISO Date string
    // Minimal other fields for the list view initially
}

// Types for the /reports/{job_id}/details endpoint response
// These should EXACTLY match your Pydantic models from the backend.
export interface DisclosureChunkDetail { // Was DisclosureChunkDetailDTO
    disclosure_code: string;
    disclosure_name?: string | null;
    generated_text: string;
    created_at: string; // ISO date string
}

export interface FindingDetail { // Was FindingDetailDTO
    disclosure_code?: string | null;
    description: string;
    severity?: 'Low' | 'Medium' | 'High' | string | null;
    anomaly_type?: string | null;
    text_quote?: string | null;
    concern_type?: string | null;
    type?: string | null; // For repetitions
    score?: number | null;
    sentence1_location?: string | null;
    sentence1_text?: string | null;
    sentence2_location?: string | null;
    sentence2_text?: string | null;
    error?: string | null;
    raw_llm_output?: string | null;
}

export interface ReportValidationFindings { // Was ReportValidationFindingsDTO
    repetitions: FindingDetail[];
    anomalies: FindingDetail[];
    coverage: FindingDetail[];
}

export interface ReportDetails { // This is ReportDetailsForFrontend, was ReportDetailsDTO
    job_id: string;
    report_file_path?: string | null;
    disclosure_chunks: DisclosureChunkDetail[];
    validation_findings: ReportValidationFindings;
    generation_status: JobStatusApi; // This comes from original_job_context.status
    error_message?: string | null;
}


export * from '../../api/csrdApiTypes.tsx'; // Adjust path as needed