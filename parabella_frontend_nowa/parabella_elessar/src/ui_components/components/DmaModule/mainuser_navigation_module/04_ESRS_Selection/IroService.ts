// src/context_module/IroService.ts
import api from "../../../../../services/api.ts";
import { IroEvaluation } from "../../utilities/types.ts";

export interface IroDto {
    id?: number;
    name: string;
    esrsTopicId?: number;
    stakeholderId?: number;
    companyId?: number;
    iroType?: string;
    subSubTopic?: string;
}

const IroService = {
    async createIro(dto: IroDto): Promise<IroDto> {
        const { data } = await api.post<IroDto>("/iros", dto);
        return data;
    },

    async updateIro(id: number, dto: IroDto): Promise<IroDto> {
        const { data } = await api.put<IroDto>(`/iros/${id}`, dto);
        return data;
    },

    async deleteIro(id: number): Promise<void> {
        await api.delete(`/iros/${id}`);
    },

    async getIroById(id: number): Promise<IroDto> {
        const { data } = await api.get<IroDto>(`/iros/${id}`);
        return data;
    },

    async getAllIros(): Promise<IroDto[]> {
        const { data } = await api.get<IroDto[]>("/iros");
        return data;
    },

    async getIrosByCompanyId(companyId: number): Promise<IroDto[]> {
        const { data } = await api.get<IroDto[]>(`/iros/company/${companyId}`);
        return data;
    },

    // IroEvaluation-based calls:
    async getEvaluationsByCompanyId(companyId: number): Promise<IroEvaluation[]> {
        const { data } = await api.get<IroEvaluation[]>(`/iroEval/company/${companyId}`);
        return data;
    },

    async getEvaluationById(id: number): Promise<IroEvaluation> {
        const { data } = await api.get<IroEvaluation>(`/iroEval/${id}`);
        return data;
    },

    /**
     * Create OR update an IroEvaluation
     */
    async saveEvaluation(evaluation: IroEvaluation, stakeholderName: string): Promise<IroEvaluation> {
        const url = `/iroEval?stakeholderName=${encodeURIComponent(stakeholderName)}`;
        console.log(evaluation)
        const { data } = await api.post<IroEvaluation>(url, evaluation);
        console.log(data)
        return data;
    },

    async deleteEvaluation(id: number): Promise<void> {
        await api.delete(`/iroEval/${id}`);
    },
};

export default IroService;
