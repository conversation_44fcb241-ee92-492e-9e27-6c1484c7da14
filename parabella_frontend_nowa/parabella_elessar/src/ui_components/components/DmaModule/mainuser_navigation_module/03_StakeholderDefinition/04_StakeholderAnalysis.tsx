import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, Row} from 'react-bootstrap';
import {useCompanyContext} from '../../context_module/CompanyContext';
import {useProjectContext} from '../../context_module/ProjectContext';
import {useNavigate} from 'react-router-dom';
import {Stakeholder} from '../../utilities/types';
import ProgressUtils from '../../utilities/ProgressUtils';
// --- MODIFICATION: Import function to get assignable users ---
import { getCurrentUser, User, getAssignableUsers } from '../../../../../services/authService';
import StakeholderEditModal from "./StakeholderEditModal";
import {
    FaBuilding,
    FaEnvelope,
    FaNetworkWired,
    FaPencilAlt,
    FaTasks,
    FaTrash,
    FaUserShield,
    FaUserPlus
} from 'react-icons/fa';
import '../css/StakeholderAnalysis.css';

// --- MODIFICATION: The Stakeholder type should be updated to include the optional user link ---
// In types.ts
// export interface Stakeholder {
//     ...
//     user?: User; // Represents the linked system user
//     userId?: number; // The ID of the linked system user
// }


const StakeholderAnalysis: React.FC = () => {
    const {companies, setCompanies} = useCompanyContext();
    const {currentProject} = useProjectContext();
    const navigate = useNavigate();

    const [stakeholders, setStakeholders] = useState<Stakeholder[]>([]);
    const [showModal, setShowModal] = useState<boolean>(false);
    const [editingStakeholder, setEditingStakeholder] = useState<Stakeholder | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    // --- NEW: State for assignable users from your system ---
    const [assignableUsers, setAssignableUsers] = useState<User[]>([]);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        loadSavedData();
        // --- NEW: Fetch users who can be assigned ---
        fetchAssignableUsers();
    }, [currentProject]); // Depend on currentProject

    const fetchAssignableUsers = async () => {
        try {
            // This function needs to be implemented in your authService to call a new backend endpoint
            const users = await getAssignableUsers();
            setAssignableUsers(users);
        } catch (err) {
            console.error('Error fetching assignable users:', err);
            // Optionally set an error state
        }
    };

    const loadSavedData = async () => {
        if (!currentProject) return;
        try {
            if (currentProject.projectType === 'company' && currentProject.companyId) {
                const company = await ProgressUtils.fetchCompany(currentProject.companyId);
                if (company) {
                    setCompanies([company]);
                    const fetchedStakeholders = await ProgressUtils.fetchStakeholdersByCompanyId(company.id!);
                    setStakeholders(fetchedStakeholders);
                }
            } else if (currentProject.projectType === 'companyGroup' && currentProject.companyGroupId) {
                const companiesInGroup = await ProgressUtils.fetchCompaniesByGroup(currentProject.companyGroupId);
                setCompanies(companiesInGroup);
                const allStakeholders = await Promise.all(
                    companiesInGroup.map(c => ProgressUtils.fetchStakeholdersByCompanyId(c.id!))
                );
                setStakeholders(allStakeholders.flat());
            }
        } catch (err) {
            console.error('Error loading saved data:', err);
            setError('Failed to load project data. Please refresh the page.');
        }
    };

    // --- MODIFICATION: Handle a list of responsible persons ---
    const responsiblePersons = stakeholders.filter((s) => s.is_responsible);
    const otherStakeholders = stakeholders.filter((s) => !s.is_responsible);

    const getCompanyNameById = (companyId: number | null) => {
        const company = companies.find((c) => c.id === companyId);
        return company ? company.companyName : 'N/A';
    };

    const getValueChainObjectNames = (ids: number[]) => {
        const allVcos = companies.flatMap((c) => c.valueChainObjects || []);
        return ids
            .map((id) => allVcos.find((vco) => vco.id === id)?.name)
            .filter(Boolean)
            .join(', ') || 'N/A';
    };

    const handleAddStakeholder = (isResponsible: boolean) => {
        setEditingStakeholder({ is_responsible: isResponsible } as Stakeholder); // Pre-set if they are responsible
        setShowModal(true);
    };

    const handleEditStakeholder = (stakeholder: Stakeholder) => {
        setEditingStakeholder(stakeholder);
        setShowModal(true);
    };

    const handleDeleteStakeholder = async (stakeholderId: number) => {
        try {
            await ProgressUtils.deleteStakeholder(stakeholderId);
            setStakeholders((prev) => prev.filter((s) => s.id !== stakeholderId));
        } catch (err) {
            console.error('Error deleting stakeholder:', err);
            setError('Failed to delete stakeholder.');
        }
    };

    const handleSaveStakeholder = async (stakeholderData: any) => {
        setLoading(true);
        try {
            // The modal will now also pass back 'userId' if a system user was selected
            if (stakeholderData.userId) {
                const selectedUser = assignableUsers.find(u => u.id === stakeholderData.userId);
                if(selectedUser) {
                    stakeholderData.name = selectedUser.name;
                    stakeholderData.email = selectedUser.email;
                }
            }

            // --- FIX: Add the current project ID to the data being saved ---
            const dataToSave = {
                ...stakeholderData,
                projectId: currentProject.id, // Add the project ID here
            };

            if (editingStakeholder && editingStakeholder.id) {
                // Update existing stakeholder, passing the complete data object
                const updatedStakeholder = await ProgressUtils.updateStakeholder(
                    editingStakeholder.id,
                    dataToSave // Use the object that includes the projectId
                );
                setStakeholders(stakeholders.map(s => s.id === updatedStakeholder.id ? updatedStakeholder : s));
            } else {
                // Create new stakeholder, passing the complete data object
                const newStakeholder = await ProgressUtils.createStakeholder(
                    dataToSave // Use the object that includes the projectId
                );
                setStakeholders(prev => [...prev, newStakeholder]);
            }
            setShowModal(false);
            setEditingStakeholder(null);
        } catch (err) {
            console.error('Error saving stakeholder:', err);
            setError('Failed to save stakeholder. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleNext = () => {
        if (responsiblePersons.length === 0) {
            setErrorMessage('Please define at least one Responsible Person before proceeding.');
            return;
        }
        navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=KPISelection`);
    };

    const handleBack = () => {
        navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=ValueChainMapping`);
    };

    const renderStakeholderCard = (stakeholder: Stakeholder) => (
        <Card className="my-modern-card p-3 mb-3" key={stakeholder.id}>
            <Card.Body className="d-flex flex-column">
                <div className="d-flex align-items-center mb-3">
                    <div className="icon-circle me-3">
                        {/* If stakeholder is linked to a user, show a different icon */}
                        {stakeholder.user || stakeholder.userId ? <FaUserShield size={20} /> : <FaUserPlus size={20} />}
                    </div>
                    <h5 className="mb-0">{stakeholder.name}</h5>
                </div>
                <div className="text-muted mb-2"><FaEnvelope className="me-2 text-muted"/><strong>Email:</strong> {stakeholder.email || 'N/A'}</div>
                <div className="text-muted mb-2"><FaBuilding className="me-2 text-muted"/><strong>Organization:</strong> {getCompanyNameById(stakeholder.companyId)}</div>
                <div className="text-muted mb-2"><FaNetworkWired className="me-2 text-muted"/><strong>Role:</strong> {stakeholder.stakeholderType || 'N/A'}</div>
                <div className="text-muted mb-4"><FaTasks className="me-2 text-muted"/><strong>Value Chain Objects:</strong> {getValueChainObjectNames(stakeholder.valueChainObjects || [])}</div>
                <div className="mt-auto d-flex justify-content-end gap-2">
                    <Button className="edit-stake" onClick={() => handleEditStakeholder(stakeholder)}><FaPencilAlt /></Button>
                    <Button variant="danger" className="edit-stake" onClick={() => handleDeleteStakeholder(stakeholder.id!)}><FaTrash /></Button>
                </div>
            </Card.Body>
        </Card>
    );

    return (
        <Container className="stakeholder-analysis-container py-3">
            <h2 className="mb-3">Stakeholder Definition</h2>
            <p className="mb-4">
                Define the responsible persons and key stakeholders for this double materiality assessment. You can add internal users (e.g., Admins) or invite external stakeholders.
            </p>

            {errorMessage && <Alert variant="danger" onClose={() => setErrorMessage(null)} dismissible>{errorMessage}</Alert>}
            {error && <Alert variant="danger" onClose={() => setError(null)} dismissible>{error}</Alert>}

            <Row className="gx-5">
                {/* --- MODIFICATION: Responsible Persons Column --- */}
                <Col xs={12} md={6} className="mb-5">
                    <div className="d-flex justify-content-between align-items-center mb-3">
                        <h2 className="section-title mb-0">Responsible Persons</h2>
                        <Button variant="primary" onClick={() => handleAddStakeholder(true)}>
                            <FaUserPlus className="me-2" /> Add Responsible
                        </Button>
                    </div>
                    {responsiblePersons.length > 0 ? (
                        responsiblePersons.map(renderStakeholderCard)
                    ) : (
                        <Card className="my-modern-card p-3 text-center">
                            <Card.Body>
                                <h5 className="mb-2">No Responsible Person Defined</h5>
                                <p className="text-muted">Click 'Add Responsible' to assign a user from your organization or invite an external collaborator.</p>
                            </Card.Body>
                        </Card>
                    )}
                </Col>

                {/* --- MODIFICATION: Other Stakeholders Column --- */}
                <Col xs={12} md={6} className="mb-5">
                    <div className="d-flex justify-content-between align-items-center mb-3">
                        <h2 className="section-title mb-0">Other Stakeholders</h2>
                        <Button variant="outline-secondary" onClick={() => handleAddStakeholder(false)}>
                            <FaUserPlus className="me-2" /> Add Stakeholder
                        </Button>
                    </div>
                    {otherStakeholders.length > 0 ? (
                        otherStakeholders.map(renderStakeholderCard)
                    ) : (
                        <Card className="my-modern-card p-3 text-center">
                            <Card.Body>
                                <h5 className="mb-2">No Other Stakeholders</h5>
                                <p className="text-muted">Click 'Add Stakeholder' to include other internal or external parties in the assessment.</p>
                            </Card.Body>
                        </Card>
                    )}
                </Col>
            </Row>

            <div className="d-flex justify-content-between mt-5 mb-5">
                <Button variant="outline-secondary" onClick={handleBack}><i className="bi bi-arrow-left"></i> Back</Button>
                <Button variant="primary" onClick={handleNext} disabled={responsiblePersons.length === 0}>
                    Next <i className="bi bi-arrow-right"></i>
                </Button>
            </div>

            {/* --- MODIFICATION: Pass new props to the modal --- */}
            {showModal && (
                <StakeholderEditModal
                    show={showModal}
                    onHide={() => { setShowModal(false); setEditingStakeholder(null); }}
                    onSave={handleSaveStakeholder}
                    loading={loading}
                    stakeholder={editingStakeholder}
                    companies={companies}
                    // NEW: Pass the list of assignable users to the modal
                    assignableUsers={assignableUsers}
                    existingStakeholders={stakeholders}
                />
            )}
        </Container>
    );
};

export default StakeholderAnalysis;