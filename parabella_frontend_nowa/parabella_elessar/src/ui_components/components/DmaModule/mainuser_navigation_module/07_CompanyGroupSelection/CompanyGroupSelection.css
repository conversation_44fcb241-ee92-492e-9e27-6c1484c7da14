.left-tabs .nav-link {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--secondary-rgb);
}

.right-tabs .nav-link {
    color: #000;
    font-size: 0.9rem;
}

.toggle-switch1 {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 32px;
}

.toggle-switch1 input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #dc3545;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 25px;
    width: 25px;
    left: 4px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch1 input:checked + .toggle-slider {
    background-color: #002D57;
}

.toggle-switch1 input:checked + .toggle-slider:before {
    transform: translateX(26px);
}
