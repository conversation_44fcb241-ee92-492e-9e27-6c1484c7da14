import React, { useEffect, useRef, useState } from 'react';
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Col,
    Container,
    Form,
    Nav,
    Navbar,
    Row,
    Tab,
    Table,
} from 'react-bootstrap';
import { useCompanyContext } from '../../context_module/CompanyContext';
import { useProjectContext } from '../../context_module/ProjectContext';
import ProgressUtils from '../../utilities/ProgressUtils';
import {
    CompanyGroupEsrsSelectionDTO,
    EsrsTopic,
    EsrsTopicSelection,
} from '../../utilities/types';
import { CheckSquare, Square } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { BsBuilding, BsChatText, BsFileEarmarkText } from 'react-icons/bs';
import api from '../../../../../services/api.ts';
import "./CompanyGroupSelection.css";


interface CompanyGroupSelectionProps {
    activeKey: string; // This prop will be passed from Mithril.tsx
}

const CompanyGroupSelection: React.FC<CompanyGroupSelectionProps> =({ activeKey }) => {
    const { companies } = useCompanyContext();
    const { currentProject } = useProjectContext();
    const [topics, setTopics] = useState<EsrsTopic[]>([]);

    // Instead of storing IroEvaluation, we store the EsrsTopicSelection objects per company
    // topicSelectionsByCompany[companyId][topicId] => EsrsTopicSelection
    const [topicSelectionsByCompany, setTopicSelectionsByCompany] = useState<{
        [companyId: number]: { [topicId: number]: EsrsTopicSelection };
    }>({});

    const [groupSelections, setGroupSelections] = useState<{
        [topicId: number]: { selected: boolean; reasonIrrelevance: string };
    }>({});

    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState<string>('Environmental');
    const navigate = useNavigate();

    // Debounce timer reference for autosave
    const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // If multiple companies exist, you can use the first, or handle otherwise:
    const selectedCompanyId = companies.length > 0 ? companies[0].id! : undefined;

    const isComponentActive = activeKey === 'CompanyGroupSelection';

    useEffect(() => {
        if (companies && companies.length > 0) {
            fetchData();
        } else {
            setIsLoading(false);
        }
    }, [companies]);

    useEffect(() => {
        if (isComponentActive) {
            fetchData();
        }
    }, [ isComponentActive]);


    const fetchData = async () => {
        setIsLoading(true);
        try {
            // 1) Fetch all ESRS topics
            const allTopics = await ProgressUtils.fetchAllEsrsTopics();
            setTopics(allTopics);

            // 2) For each company, fetch EsrsTopicSelections
            const allCompaniesSelections = await Promise.all(
                companies.map((company) =>
                    ProgressUtils.fetchEsrsTopicSelectionsByCompanyId(company.id!)
                )
            );

            // Build a map: topicSelectionsByCompany[companyId][topicId] = EsrsTopicSelection
            const mapByCompany: {
                [companyId: number]: { [topicId: number]: EsrsTopicSelection };
            } = {};

            companies.forEach((company, index) => {
                const selections = allCompaniesSelections[index] || [];
                const topicMap: { [topicId: number]: EsrsTopicSelection } = {};
                selections.forEach((sel) => {
                    topicMap[sel.esrsTopicId] = sel;
                });
                mapByCompany[company.id!] = topicMap;
            });
            setTopicSelectionsByCompany(mapByCompany);

            // 3) Fetch group-level selections
            const groupCompanyId = currentProject?.companyGroupId;
            if (!groupCompanyId) {
                setError('Group company ID not found.');
                return;
            }
            const groupSelectionsResponse =
                await ProgressUtils.fetchGroupSelectionsByCompanyGroupId(groupCompanyId);
            const groupSelectionsData = groupSelectionsResponse.reduce((acc, selection) => {
                acc[selection.esrsTopicId] = {
                    selected: selection.isRelevantCompanyGroup,
                    reasonIrrelevance: selection.reasonIrrelevance || '',
                };
                return acc;
            }, {} as { [topicId: number]: { selected: boolean; reasonIrrelevance: string } });

            // Fill in missing topics with default values
            allTopics.forEach((topic) => {
                if (!groupSelectionsData[topic.id!]) {
                    groupSelectionsData[topic.id!] = {
                        selected: false,
                        reasonIrrelevance: '',
                    };
                }
            });

            setGroupSelections(groupSelectionsData);
        } catch (err) {
            console.error('Error fetching data:', err);
            setError('Failed to fetch data. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleGroupSelectionChange = (topicId: number) => {
        setGroupSelections((prev) => ({
            ...prev,
            [topicId]: {
                ...prev[topicId],
                selected: !prev[topicId].selected,
            },
        }));
    };

    const handleReasonChange = (topicId: number, value: string) => {
        setGroupSelections((prev) => ({
            ...prev,
            [topicId]: {
                ...prev[topicId],
                reasonIrrelevance: value,
            },
        }));
    };

    const handleInputChange = (
        topicId: number,
        field: 'reasonIrrelevance',
        value: string
    ) => {
        setGroupSelections((prev) => ({
            ...prev,
            [topicId]: {
                ...prev[topicId],
                [field]: value,
            },
        }));
    };

    // Debounced autosave on groupSelections change
    useEffect(() => {
        if (saveTimeoutRef.current) {
            clearTimeout(saveTimeoutRef.current);
        }
        if (Object.keys(groupSelections).length > 0) {
            saveTimeoutRef.current = setTimeout(() => {
                saveGroupSelections();
            }, 500);
        }
        return () => {
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [groupSelections]);

    const saveGroupSelections = async () => {
        try {
            const groupCompanyId = currentProject?.companyGroupId;
            if (!groupCompanyId) {
                setError('Group company ID not found.');
                return;
            }
            const savePromises = Object.entries(groupSelections).map(
                async ([topicIdStr, selection]) => {
                    const topicId = parseInt(topicIdStr);
                    const selectionDTO: CompanyGroupEsrsSelectionDTO = {
                        companyGroupId: groupCompanyId,
                        esrsTopicId: topicId,
                        isRelevantCompanyGroup: selection.selected,
                        reasonIrrelevance: selection.reasonIrrelevance || '',
                    };
                    await ProgressUtils.createOrUpdateCompanyGroupEsrsSelection(selectionDTO);
                }
            );
            await Promise.all(savePromises);
            console.log('Group selections saved successfully (autosave).');
        } catch (error) {
            console.error('Error saving group selections:', error);
            setError('Failed to save group selections. Please try again.');
        }
    };

    const handleNavigate = async (path: string) => {
        // Autosave is in place, just navigate
        navigate(path);
    };

    const prepareDataPoints = () => {
        const dataPointsMap: { [area: string]: { [topicName: string]: EsrsTopic[] } } =
            {};

        topics.forEach((topic) => {
            if (!dataPointsMap[topic.area]) {
                dataPointsMap[topic.area] = {};
            }
            const topicName = topic.topic || 'General';
            if (!dataPointsMap[topic.area][topicName]) {
                dataPointsMap[topic.area][topicName] = [];
            }
            dataPointsMap[topic.area][topicName].push(topic);
        });

        return dataPointsMap;
    };

    const dataPointsByArea = prepareDataPoints();

    // Generate reason for irrelevance via AI
    const generateReasonForIrrelevance = async (
        topicId: number,
        mainTopic: string,
        subtopicObj: EsrsTopic
    ) => {
        if (!selectedCompanyId) {
            console.warn('No selected company available for generating reason.');
            return;
        }

        const groupCompanyName =
            companies.find((c) => c.id === selectedCompanyId)?.companyName || 'The Company';
        const companyIndustry =
            companies.find((c) => c.id === selectedCompanyId)?.industry || 'Industry';

        const requestBody = {
            companyName: groupCompanyName,
            esrsCriteria: mainTopic,
            esrsKpi: subtopicObj.subtopic,
            companyIndustry: companyIndustry,
        };

        try {
            const response = await api.post('/chat/generate-irrelevance-reason', requestBody);
            const reason = response.data.reply;
            handleInputChange(topicId, 'reasonIrrelevance', reason);
        } catch (err) {
            console.error('Error generating reason:', err);
        }
    };

    if (isLoading) {
        return (
            <Container className="text-center mt-5">
                <div className="spinner-border text-primary" role="status"></div>
            </Container>
        );
    }

    if (error) {
        return (
            <Container className="mt-5">
                <Alert variant="danger">{error}</Alert>
            </Container>
        );
    }

    if (!companies || companies.length === 0) {
        return (
            <Container className="mt-5">
                <Alert variant="info">No companies available. Please add companies to proceed.</Alert>
            </Container>
        );
    }

    return (
        <Container fluid className="py-4">
            <Row className="mb-4">
                <Col>
                    <h2>Company Group Selection</h2>
                    <p>
                        Select which topics should be reported at the group level. A checkmark indicates
                        that the topic is relevant for each company. Changes are saved automatically.
                    </p>
                </Col>
            </Row>

            {/* Navbar similar to TopicSelectionAnalysis */}
            <Navbar expand="lg" className="mb-3 align-items-center">
                <Navbar.Toggle aria-controls="navbar-nav" />
                <Navbar.Collapse id="navbar-nav" className="align-items-center">
                    <Nav
                        variant="tabs"
                        activeKey={activeTab}
                        onSelect={(k) => k && setActiveTab(k)}
                        className="left-tabs align-items-center tab-style-2 nav-justified d-sm-flex d-block"
                    >
                        <Nav.Item>
                            <Nav.Link eventKey="Environmental">Environmental</Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="Social">Social</Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="Governance">Governance</Nav.Link>
                        </Nav.Item>
                    </Nav>
                </Navbar.Collapse>
            </Navbar>

            <Tab.Container activeKey={activeTab}>
                <Tab.Content>
                    {['Environmental', 'Social', 'Governance'].map((area) => (
                        <Tab.Pane key={area} eventKey={area}>
                            <Accordion className="accordion-primary" defaultActiveKey="0">
                                {(Object.entries(dataPointsByArea[area] || {}) || []).map(
                                    ([topicName, topics], idx) => (
                                        <Accordion.Item eventKey={idx.toString()} key={topicName}>
                                            <Accordion.Header>
                                                <strong>{topicName}</strong>
                                            </Accordion.Header>
                                            <Accordion.Body>
                                                <div className="table-responsive">
                                                    <Table striped bordered hover>
                                                        <thead className="bg-light">
                                                        <tr>
                                                            <th style={{ width: '20%' }}>
                                                                <BsFileEarmarkText /> ESRS Topic
                                                            </th>
                                                            {companies.map((company) => (
                                                                <th key={company.id} style={{ width: '10%' }}>
                                                                    <BsBuilding /> {company.companyName}
                                                                </th>
                                                            ))}
                                                            <th style={{ width: '40%' }}>
                                                                <BsChatText /> Reason for Irrelevance
                                                            </th>
                                                            <th style={{ width: '10%' }} className="font-weight-bold">
                                                                Group Selection
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        {topics.map((topic) => {
                                                            const topicId = topic.id!;
                                                            const groupSel = groupSelections[topicId] || {
                                                                selected: false,
                                                                reasonIrrelevance: '',
                                                            };

                                                            return (
                                                                <tr key={topicId}>
                                                                    {/* ESRS Topic Column */}
                                                                    <td style={{ width: '20%' }}>
                                                                        {topic.esrsCode} - {topic.subtopic}{' '}
                                                                        {topic.subSubTopic && `- ${topic.subSubTopic}`}
                                                                    </td>

                                                                    {/* Company Relevance Columns */}
                                                                    {companies.map((company) => {
                                                                        const selectionsMap = topicSelectionsByCompany[company.id!] || {};
                                                                        const detail = selectionsMap[topicId];
                                                                        // Instead of "detail.resultMaterialityAssessment === 'Relevant'",
                                                                        // you can use "detail.finalRelevance === 'Relevant'" or "detail.relevant"
                                                                        const isRelevant =
                                                                            detail && (detail.finalRelevance === 'Relevant' || detail.relevant);

                                                                        return (
                                                                            <td
                                                                                key={company.id}
                                                                                style={{ width: '10%' }}
                                                                                className="text-center"
                                                                            >
                                                                                {isRelevant ? (
                                                                                    <CheckSquare className="text-success" size={24} />
                                                                                ) : (
                                                                                    <Square className="text-muted" size={24} />
                                                                                )}
                                                                            </td>
                                                                        );
                                                                    })}

                                                                    {/* Reason for Irrelevance Column */}
                                                                    <td style={{ width: '40%', position: 'relative' }}>
                                                                        {!groupSel.selected && (
                                                                            <>
                                                                                <Form.Control
                                                                                    as="textarea"
                                                                                    rows={4}
                                                                                    placeholder="Reason for irrelevance"
                                                                                    value={groupSel.reasonIrrelevance}
                                                                                    onChange={(e) => handleReasonChange(topicId, e.target.value)}
                                                                                    style={{
                                                                                        resize: 'vertical',
                                                                                        paddingRight: '40px',
                                                                                    }}
                                                                                />
                                                                                <Button
                                                                                    variant="primaryy"
                                                                                    size="sm"
                                                                                    onClick={() =>
                                                                                        generateReasonForIrrelevance(topicId, topicName, topic)
                                                                                    }
                                                                                    className="d-flex align-items-center justify-content-center"
                                                                                    style={{
                                                                                        position: 'absolute',
                                                                                        top: '12px',
                                                                                        right: '12px',
                                                                                        width: '32px',
                                                                                        height: '32px',
                                                                                        fontSize: '1.25rem',
                                                                                        padding: '0',
                                                                                    }}
                                                                                >
                                                                                    <i className="bi bi-stars"></i>
                                                                                </Button>
                                                                            </>
                                                                        )}
                                                                    </td>

                                                                    {/* Group Selection Column with custom toggle */}
                                                                    <td style={{ width: '10%' }} className="text-center align-middle">
                                                                        <label className="toggle-switch1">
                                                                            <input
                                                                                type="checkbox"
                                                                                checked={groupSel.selected}
                                                                                onChange={() => handleGroupSelectionChange(topicId)}
                                                                            />
                                                                            <span className="toggle-slider"></span>
                                                                        </label>
                                                                    </td>
                                                                </tr>
                                                            );
                                                        })}
                                                        </tbody>
                                                    </Table>
                                                </div>
                                            </Accordion.Body>
                                        </Accordion.Item>
                                    )
                                )}
                            </Accordion>
                        </Tab.Pane>
                    ))}
                </Tab.Content>
            </Tab.Container>

            <Row className="mt-4">
                <Col className="d-flex justify-content-between">
                    <Button
                        variant="light"
                        onClick={() =>
                            handleNavigate(
                                `${import.meta.env.BASE_URL}mithril/mithril?tab=AnalysisResult`
                            )
                        }
                    >
                        <i className="bi bi-arrow-left"></i> Back
                    </Button>
                    {/* No explicit save button since autosaving is enabled */}
                    <Button
                        variant="primary"
                        onClick={() =>
                            handleNavigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=CompanyGroupOverview`)
                        }
                    >
                        Next <i className="bi bi-arrow-right"></i>
                    </Button>
                </Col>
            </Row>
        </Container>
    );
};

export default CompanyGroupSelection;
