// CompanyGroupOverview.tsx

import React, {useEffect, useState} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Nav,
    Navbar,
    <PERSON>,
    Spinner,
    Tab
} from 'react-bootstrap';
import {EsrsTopic} from '../../utilities/types';
import ProgressUtils from '../../utilities/ProgressUtils';
import {useProjectContext} from '../../context_module/ProjectContext';
import {useNavigate} from 'react-router-dom';
import './CompanyGroupOverview.css';

interface CompanyGroupOverviewProps {
    activeKey: string; // This prop will be passed from Mithril.tsx
}

const CompanyGroupOverview: React.FC<CompanyGroupOverviewProps> = ({ activeKey }) =>{
    const {currentProject} = useProjectContext();
    const [topicsByArea, setTopicsByArea] = useState<{ [area: string]: EsrsTopic[] }>({});
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState<string>('Environmental');

    const isComponentActive = activeKey === 'CompanyGroupOverview';

    useEffect(() => {
        fetchGroupRelevantTopics();
    }, []);

    useEffect(() => {
        if (isComponentActive) {
            fetchGroupRelevantTopics()
        }
    }, [ isComponentActive]);


    const fetchGroupRelevantTopics = async () => {
        try {
            const groupCompanyId = currentProject?.companyGroupId;
            if (!groupCompanyId) {
                console.error('Group company ID not found.');
                return;
            }

            const groupSelections = await ProgressUtils.fetchGroupSelectionsByCompanyGroupId(groupCompanyId);

            // Filter selections to those marked as relevant
            const relevantSelections = groupSelections.filter(selection => selection.isRelevantCompanyGroup);

            // Get the ESRS topic IDs
            const topicIds = relevantSelections.map(selection => selection.esrsTopicId);

            // Fetch all ESRS topics
            const allTopics = await ProgressUtils.fetchAllEsrsTopics();

            // Filter topics to only include those that are relevant at the group level
            const relevantTopics = allTopics.filter(topic => topicIds.includes(topic.id!));

            // Organize topics by area
            const groupedByArea: { [area: string]: EsrsTopic[] } = {};
            relevantTopics.forEach(topic => {
                if (!groupedByArea[topic.area]) {
                    groupedByArea[topic.area] = [];
                }
                groupedByArea[topic.area].push(topic);
            });

            setTopicsByArea(groupedByArea);
        } catch (error) {
            console.error('Error fetching group relevant topics:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Function to handle Back button click
    const handleBack = () => {
        navigate(-1);
    };

    return (
        <Container fluid className="py-4">
            <Row className="mb-4">
                <Col>
                    <h2 >Company Group Overview</h2>
                    <p>
                        Below is an overview of all topics marked as relevant for the group. These topics need to be
                        reported at the group level.
                    </p>
                </Col>
            </Row>
            {/* Navbar Section */}
            <Navbar expand="lg" className="mb-3 align-items-center navbar-custom ">
                <Navbar.Toggle aria-controls="navbar-nav" />
                <Navbar.Collapse id="navbar-nav" className="align-items-center">
                    <Nav
                        variant="tabs"
                        activeKey={activeTab}
                        onSelect={(k) => k && setActiveTab(k)}
                        className="left-tabs align-items-center tab-style-2 nav-justified d-sm-flex d-block me-auto"
                    >
                        <Nav.Item>
                            <Nav.Link eventKey="Environmental" className="left-tabs2">
                                Environmental
                            </Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="Social">Social</Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="Governance">Governance</Nav.Link>
                        </Nav.Item>
                    </Nav>

                    <Nav className="ms-auto align-items-center  tab-style-2   ">
                        <Nav.Item className="me-2">
                            <Button variant="primary" disabled>
                                <i className="bi bi-download me-2"></i> Export
                            </Button>
                        </Nav.Item>
                        <Nav.Item className="me-2">
                            <Button variant="primary" disabled>
                                <i className="bi bi-share-fill me-2"></i> Share
                            </Button>
                        </Nav.Item>
                        <Nav.Item>
                            <Button variant="primary" disabled>
                                <i className="bi bi-save-fill me-2"></i> Save
                            </Button>
                        </Nav.Item>
                    </Nav>
                </Navbar.Collapse>
            </Navbar>



            {isLoading ? (
                <div className="text-center">
                    <Spinner animation="border" variant="primary" role="status" />
                </div>
            ) : (
                <Tab.Container activeKey={activeTab}>
                    <Tab.Content>
                        {['Environmental', 'Social', 'Governance'].map(area => (
                            <Tab.Pane key={area} eventKey={area}>
                                <Row className="mt-4">
                                    {topicsByArea[area] && topicsByArea[area].length > 0 ? (
                                        topicsByArea[area].map(topic => (
                                            <Col md={6} lg={4} key={topic.id} className="mb-4">
                                                <Card className="company-group-card">
                                                    <Card.Header>
                                                        <div className="d-flex align-items-center">
                                                            <div className="me-3">
                                                                {area === 'Environmental' && (
                                                                    <i className="bi bi-tree-fill" style={{fontSize: '1.5rem'}}></i>
                                                                )}
                                                                {area === 'Social' && (
                                                                    <i className="bi bi-people-fill" style={{fontSize: '1.5rem'}}></i>
                                                                )}
                                                                {area === 'Governance' && (
                                                                    <i className="bi bi-building" style={{fontSize: '1.5rem'}}></i>
                                                                )}
                                                            </div>
                                                            <div>
                                                                <Card.Title className="mb-0">{topic.esrsCode}</Card.Title>
                                                                <small>{topic.topic}</small>
                                                            </div>
                                                        </div>
                                                    </Card.Header>
                                                    <Card.Body>
                                                        <Card.Text>
                                                            Sub-Sub-Topic: {topic.subtopic}
                                                            {topic.subSubTopic && ` - ${topic.subSubTopic}`}
                                                        </Card.Text>
                                                    </Card.Body>
                                                </Card>
                                            </Col>
                                        ))
                                    ) : (
                                        <Col>
                                            <p>No relevant topics in this area.</p>
                                        </Col>
                                    )}
                                </Row>
                            </Tab.Pane>
                        ))}
                    </Tab.Content>
                </Tab.Container>
            )}

            {/* Back Button at the Bottom */}
            <Row className="mt-4">
                <Col xs={12}>
                    <Button variant="light" onClick={handleBack}>
                        <i className="bi bi-arrow-left me-2"></i> Back
                    </Button>
                </Col>
            </Row>
        </Container>
    );
};

export default CompanyGroupOverview;
