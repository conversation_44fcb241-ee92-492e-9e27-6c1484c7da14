import React, {Fragment, useEffect, useState} from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    Container,
    Dropdown,
    ListGroup,
    Modal,
    Nav,
    <PERSON>vbar,
    Spinner,
    Tab,
} from 'react-bootstrap';
import {Link, useLocation, useNavigate} from 'react-router-dom';
import {FaBell, FaCheckCircle, FaHistory, FaTimesCircle, FaTrash} from 'react-icons/fa';
import GeneralCompanyInformation from '../01_GeneralCompanyInformation/02_GeneralCompanyInformation';
import StakeholderAnalysis from '../03_StakeholderDefinition/04_StakeholderAnalysis';
import ValueChainMapping from '../02_ValueChainMapping/03_ValueChainMapping';
import TopicsAnalysisSelection from '../04_ESRS_Selection/05_TopicsAnalysisSelection';
import ImpactFinancialAnalysis from '../05_ImpactFinancialAnalysis/07_ImpactFinancialAnalysis';
import AnalysisResult from '../06_AnalysisResult/08_AnalysisResult';
import '../css/Mithril.css';
import {useProjectContext} from '../../context_module/ProjectContext';
import CompanyGroupSelection from '../07_CompanyGroupSelection/09_CompanyGroupSelection';
import {AnimatePresence, motion} from 'framer-motion';
import {
    getNotificationsForProject,
    markNotificationAsRead,
    NotificationDTO,
} from '../../utilities/notification/NotificationService';
import {Home} from 'lucide-react';
import {DashboardCustomize} from '@mui/icons-material';
import Chatbot from '../../../chat_bot/ChatBot.tsx';
import CompanyGroupOverview from '../08_CompanyGroupAnalysis/CompanyGroupOverview.tsx';
import {useCompanyContext} from "../../context_module/CompanyContext";

const Mithril: React.FC = () => {
    // State for chatbot
    const [chatbotOpen, setChatbotOpen] = useState(false);
    const openChatbot = () => setChatbotOpen(true);
    const closeChatbot = () => setChatbotOpen(false);

    // Active tab key
    const [activeKey, setActiveKey] = useState('Start');

    // Modal state
    const [showModal, setShowModal] = useState(false);
    const handleCloseModal = () => setShowModal(false);
    const handleShowModal = () => setShowModal(true);

    // Location & navigation
    const location = useLocation();
    const navigate = useNavigate();

    // Context
    const {companies, setCompanies} = useCompanyContext();
    const {currentProject} = useProjectContext();
    const { setSelectedCompanyId } = useCompanyContext();

    // Notifications
    const [notifications, setNotifications] = useState<NotificationDTO[]>([]);
    const [isNotificationsLoading, setIsNotificationsLoading] = useState<boolean>(false);
    const [notificationsError, setNotificationsError] = useState<string>('');
    const [showNotifications, setShowNotifications] = useState(false);

    // Steps configuration
    const baseSteps = [
        {eventKey: 'GeneralInformation', title: 'General Information'},
        {eventKey: 'ValueChainMapping', title: 'Value Chain Mapping'},
        {eventKey: 'StakeholderAnalysis', title: 'Stakeholder Definition'},
        {eventKey: 'KPISelection', title: 'ESRS Selection'},
        {eventKey: 'ImpactFinancialAnalysis', title: 'Impact&Financial Analysis'},
        {eventKey: 'AnalysisResult', title: 'Analysis Results'},
    ];

    const groupSteps = [
        {eventKey: 'CompanyGroupSelection', title: 'Company Group Selection'},
        {eventKey: 'CompanyGroupOverview', title: 'Company Group Overview'},
    ];

    const steps = currentProject?.projectType === 'companyGroup'
        ? [...baseSteps, ...groupSteps]
        : baseSteps;

    // Give each step a step number
    const numberedSteps = steps.map((step, index) => ({
        ...step,
        number: (index + 1).toString(),
    }));

    /**
     * Sync active tab with URL query param:
     *   1. If ?tab=xxx is present and valid, set activeKey to that.
     *   2. Otherwise, default to "GeneralInformation".
     */
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const tab = searchParams.get('tab');

        if (tab && steps.some((step) => step.eventKey === tab)) {
            setActiveKey(tab);
        } else {
            // Fallback if no valid ?tab= found:
            setActiveKey('GeneralInformation');
        }
    }, [location.search, steps]);

    // // Notification polling
    // useEffect(() => {
    //     fetchNotifications(); // Initial fetch
    //     const interval = setInterval(() => {
    //         fetchNotifications();
    //     }, 300000); // Poll every 5 minutes
    //     return () => clearInterval(interval);
    // }, [currentProject]);

    const fetchNotifications = async () => {
        if (!currentProject) return;
        setIsNotificationsLoading(true);
        setNotificationsError('');
        try {
            const fetchedNotifications = await getNotificationsForProject(currentProject.id);
            setNotifications(fetchedNotifications);
        } catch (error) {
            console.error('Error fetching notifications:', error);
            setNotificationsError('Failed to load notifications.');
        } finally {
            setIsNotificationsLoading(false);
        }
    };

    // Notification toggle
    const toggleNotifications = () => setShowNotifications(!showNotifications);
    const unreadCount = notifications.filter((notif) => !notif.read).length;

    const handleMarkAsRead = async (id: number) => {
        try {
            await markNotificationAsRead(id);
            setNotifications((prev) =>
                prev.map((notif) =>
                    notif.id === id ? {...notif, read: true} : notif
                )
            );
        } catch (error) {
            console.error('Error marking notification as read:', error);
            alert('Failed to mark notification as read. Please try again.');
        }
    };

    // Leave project
    const handleLeaveProject = () => {
        handleCloseModal();
        setSelectedCompanyId(null);
        setCompanies([]);
        navigate(`${import.meta.env.BASE_URL}mithril/CreateNewProjectDMA`);
    };

    // If user clicks Home, show modal and reset selectedCompanyId
    const handleHomeClick = (event: React.MouseEvent<HTMLElement>) => {
        event.preventDefault();
        setSelectedCompanyId(null);
        handleShowModal();
    };

    return (
        <Fragment>
            {/* TOP NAVBAR */}
            <Navbar bg="white" expand="lg" className="navbar-custom shadow-sm">
                <Container fluid>
                    <Navbar.Toggle aria-controls="navbar-top"/>
                    <Navbar.Collapse id="navbar-top">
                        <Nav className="me-auto">
                            <Nav.Link href="#" onClick={handleHomeClick}>
                                <Home className="me-1"/> Home
                            </Nav.Link>
                          {/*  <Nav.Link as={Link} to={`${import.meta.env.BASE_URL}mithril/DMAAdminDashboard`}>
                                <DashboardCustomize className="me-1"/> Stakeholder Dashboard
                            </Nav.Link>*/}
                            <Nav.Link
                                as={Link}
                                to={`${import.meta.env.BASE_URL}mithril/auditlog`}
                            >
                                <FaHistory className="me-1" /> Audit Log
                            </Nav.Link>
                        </Nav>
                        <Nav className="align-items-center">
                            {/* Notifications */}
                            <Dropdown show={showNotifications} onToggle={toggleNotifications} align="end">
                                <Dropdown.Toggle as="div" className="position-relative d-inline-block me-3">
                                    <motion.div
                                        whileHover={{scale: 1.1}}
                                        whileTap={{scale: 0.9}}
                                        className="cursor-pointer"
                                    >
                                        <FaBell size={20} className="text-muted"/>
                                        {unreadCount > 0 && (
                                            <Badge
                                                bg="danger"
                                                pill
                                                className="position-absolute top-0 start-100 translate-middle"
                                            >
                                                {unreadCount}
                                            </Badge>
                                        )}
                                    </motion.div>
                                </Dropdown.Toggle>

                                <Dropdown.Menu
                                    className="shadow-lg border-0 p-0"
                                    style={{width: '350px', maxHeight: '500px', overflowY: 'auto'}}
                                >
                                    <AnimatePresence>
                                        {showNotifications && (
                                            <motion.div
                                                initial={{opacity: 0, y: -10}}
                                                animate={{opacity: 1, y: 0}}
                                                exit={{opacity: 0, y: -10}}
                                                transition={{duration: 0.2}}
                                            >
                                                <Dropdown.Header
                                                    className="bg-light py-3 px-4 d-flex justify-content-between align-items-center">
                                                    <h6 className="mb-0">Notifications</h6>
                                                    <Button
                                                        variant="link"
                                                        size="sm"
                                                        className="text-decoration-none p-0"
                                                        onClick={() => {
                                                            const unreadIds = notifications
                                                                .filter((n) => !n.read)
                                                                .map((n) => n.id);
                                                            unreadIds.forEach((id) => handleMarkAsRead(id));
                                                        }}
                                                    >
                                                        Mark all as read
                                                    </Button>
                                                </Dropdown.Header>

                                                {isNotificationsLoading ? (
                                                    <div className="text-center my-4">
                                                        <Spinner animation="border" variant="primary"/>
                                                    </div>
                                                ) : notificationsError ? (
                                                    <p className="text-danger p-3 mb-0">{notificationsError}</p>
                                                ) : notifications.length === 0 ? (
                                                    <p className="text-muted p-3 mb-0">No new notifications.</p>
                                                ) : (
                                                    <ListGroup variant="flush">
                                                        {notifications.map((notif) => (
                                                            <ListGroup.Item
                                                                key={notif.id}
                                                                action
                                                                onClick={() => !notif.read && handleMarkAsRead(notif.id)}
                                                                className={`border-0 ${
                                                                    !notif.read ? 'bg-light' : ''
                                                                }`}
                                                            >
                                                                <div className="d-flex align-items-center">
                                                                    <div className="me-3">
                                                                        {!notif.read ? (
                                                                            <FaCheckCircle className="primary"/>
                                                                        ) : (
                                                                            <FaTimesCircle className="text-muted"/>
                                                                        )}
                                                                    </div>
                                                                    <div className="flex-grow-1">
                                                                        <p className={`mb-1 ${!notif.read ? 'fw-bold' : ''}`}>
                                                                            {notif.message}
                                                                        </p>
                                                                        <small className="text-muted">
                                                                            {/* timestamp if available */}
                                                                        </small>
                                                                    </div>
                                                                    {!notif.read && (
                                                                        <Button
                                                                            variant="link"
                                                                            className="p-0 text-danger"
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                // Implement delete functionality if required
                                                                            }}
                                                                        >
                                                                            <FaTrash/>
                                                                        </Button>
                                                                    )}
                                                                </div>
                                                            </ListGroup.Item>
                                                        ))}
                                                    </ListGroup>
                                                )}
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </Dropdown.Menu>
                            </Dropdown>

                            {/* Current Project Display */}
                            {currentProject && (
                                <Navbar.Text>
                                    Current Project: <span className="fw-bold">{currentProject.projectName}</span>
                                </Navbar.Text>
                            )}
                        </Nav>
                    </Navbar.Collapse>
                </Container>
            </Navbar>

            {/* MAIN CONTENT with TABS */}
            <div className="main-content">
                <Container fluid>
                    {/*
                      We no longer need onSelect={handleTabSelect}
                      because each Nav.Link uses React Router's Link
                      to update the URL, and the useEffect above
                      sets `activeKey` from the ?tab= param.
                    */}
                    <Tab.Container id="left-tabs-example" activeKey={activeKey}>
                        <Navbar bg="light" expand="lg" className="nav-tabs-custom justify-content-center">
                            <Container fluid>
                                <Navbar.Toggle aria-controls="navbar-tabs"/>
                                <Navbar.Collapse id="navbar-tabs">
                                    <Nav className="me-auto nav-tabs-custom" role="tablist">
                                        {numberedSteps.map((step) => (
                                            <Nav.Item key={step.eventKey}>
                                                <Nav.Link
                                                    // Use React Router's Link to update ?tab=xxx
                                                    as={Link}
                                                    to={`${import.meta.env.BASE_URL}mithril/mithril?tab=${step.eventKey}`}
                                                    eventKey={step.eventKey}
                                                >
                                                    <div
                                                        className={`nav-step-item ${
                                                            activeKey === step.eventKey ? 'active' : ''
                                                        }`}
                                                    >
                                                        <div className="step-content">
                                                            <div className="step-number">{step.number}</div>
                                                            <span className="step-title2">{step.title}</span>
                                                        </div>
                                                    </div>
                                                </Nav.Link>
                                            </Nav.Item>
                                        ))}
                                    </Nav>
                                </Navbar.Collapse>
                            </Container>
                        </Navbar>

                        <Card className="custom-card fade-in">
                            <Card.Body>
                                <Tab.Content>
                                    <Tab.Pane className="p-0 border-0" eventKey="GeneralInformation">
                                        <GeneralCompanyInformation/>
                                    </Tab.Pane>
                                    <Tab.Pane className="p-0 border-0" eventKey="ValueChainMapping">
                                        <ValueChainMapping/>
                                    </Tab.Pane>
                                    <Tab.Pane className="p-0 border-0" eventKey="StakeholderAnalysis">
                                        <StakeholderAnalysis/>
                                    </Tab.Pane>
                                    <Tab.Pane eventKey="KPISelection">
                                        <TopicsAnalysisSelection
                                            openChatbot={openChatbot}
                                            activeKey={activeKey}
                                        />
                                    </Tab.Pane>
                                    <Tab.Pane className="p-0 border-0" eventKey="ImpactFinancialAnalysis">
                                        <ImpactFinancialAnalysis
                                            activeKey={activeKey}
                                        />
                                    </Tab.Pane>
                                    <Tab.Pane className="p-0 border-0" eventKey="AnalysisResult">
                                        <AnalysisResult
                                            activeKey={activeKey}/>
                                    </Tab.Pane>

                                    {currentProject?.projectType === 'companyGroup' && (
                                        <Tab.Pane className="p-0 border-0" eventKey="CompanyGroupSelection">
                                            <CompanyGroupSelection
                                                activeKey={activeKey}
                                            />
                                        </Tab.Pane>
                                    )}
                                    {currentProject?.projectType === 'companyGroup' && (
                                        <Tab.Pane className="p-0 border-0" eventKey="CompanyGroupOverview">
                                            <CompanyGroupOverview
                                                activeKey={activeKey}
                                            />
                                        </Tab.Pane>
                                    )}
                                </Tab.Content>
                            </Card.Body>
                        </Card>
                    </Tab.Container>
                </Container>
            </div>

            {/* MODAL for leaving project */}
            <Modal show={showModal} onHide={handleCloseModal}>
                <Modal.Header closeButton>
                    <Modal.Title>Confirm Leave Project</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    Are you sure you want to leave the current project? Unsaved changes may be lost.
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="outline-secondary" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button variant="danger" onClick={handleLeaveProject}>
                        Leave Project
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* CHATBOT */}
            <Chatbot isOpen={chatbotOpen} onOpenChange={setChatbotOpen}/>
        </Fragment>
    );
};

export default Mithril;
