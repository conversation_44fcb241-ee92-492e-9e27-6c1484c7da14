import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Card, Col, Container, Form, Modal, Row, Spinner,} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import {imagesData} from '../../../templateLogic/commonimages.tsx';
import {Project, useProjectContext} from '../context_module/ProjectContext.tsx';
import './css/CreateNewProjectDMA.css';
import {API_ENDPOINTS} from '../../../../config/APIEndpoints.ts';
import api from '../../../../services/api.ts';
import {useAuth} from "../../../../services/AuthContext.tsx";

const CreateNewProjectDMA: React.FC = () => {
    // --- Auth & Permissions ---
    const {user, hasPermission} = useAuth();
    const canReadDma = hasPermission('dma.read');
    const canEditDma = hasPermission('dma.edit');
    const isActionsDisabled = true;

    // --- Component State ---
    const [projectName, setProjectName] = useState('');
    const [projectDescription, setProjectDescription] = useState('');
    const [projectType, setProjectType] = useState<'company' | 'companyGroup'>('company');
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [loading, setLoading] = useState(true);
    const [selectedProjectId, setSelectedProjectId] = useState<number | 'new' | null>(null);
    const [showWarningModal, setShowWarningModal] = useState(false);

    // --- Context & Navigation ---
    // MODIFICATION: Destructure setCurrentProject to reset the context.
    const {
        loadProjectData,
        userProjects,
        loadUserProjects,
        copyProject,
        deleteProject,
        setCurrentProject
    } = useProjectContext();
    const navigate = useNavigate();

    // --- Effects ---
    // MODIFICATION: This effect runs when the component mounts.
    // It resets the global current project and the local UI selection.
    useEffect(() => {
        // Reset the global current project in the context.
        setCurrentProject(null);
        // Also reset the local selection state to ensure the UI is clean.
        setSelectedProjectId(null);
    }, [setCurrentProject]); // Dependency on the stable setter function ensures this runs once on mount.

    // Effect to load projects based on the user from the context.
    useEffect(() => {
        if (user?.id && canReadDma) {
            setLoading(true);
            loadUserProjects(user.id)
                .finally(() => setLoading(false));
        } else {
            setLoading(false);
        }
    }, [user, canReadDma, loadUserProjects]);

    // --- Validation ---
    const validateFields = () => {
        const newErrors: { [key: string]: string } = {};
        if (!projectName.trim()) newErrors.projectName = 'Project name is required';
        if (!projectDescription.trim()) newErrors.projectDescription = 'Project description is required';
        if (!projectType) newErrors.projectType = 'Please select a project type';
        return newErrors;
    };

    // --- Handlers ---
    const handleCreateProject = async () => {
        if (!canEditDma || !user) {
            console.warn("Attempted to create project without permission or user session.");
            return;
        }

        try {
            setLoading(true);
            const projectData = {
                userId: user.id,
                projectName: projectName.trim(),
                projectDescription: projectDescription.trim(),
                projectType,
            };
            const response = await api.post<Project>(API_ENDPOINTS.PROJECTS.CREATE, projectData);
            const newProject = response.data;
            await loadUserProjects(user.id); // Refresh project list
            await loadProjectData(newProject.id); // Load the new project into context
            navigate(`${import.meta.env.BASE_URL}mithril/mithril`);
        } catch (error) {
            console.error('Error while creating project:', error);
            setErrors({apiError: 'Failed to create project. Please try again later.'});
        } finally {
            setLoading(false);
        }
    };

    const handleLoadProject = async (projectId: number) => {
        if (!canReadDma) {
            console.warn("Attempted to load a project without 'dma.read' permission.");
            return;
        }
        try {
            await loadProjectData(projectId); // Load the selected project into context
            navigate(`${import.meta.env.BASE_URL}mithril/mithril`);
        } catch (error) {
            console.error('Error loading project:', error);
        }
    };

    const handleCopyProject = async (projectId: number) => {
        if (!canEditDma || !user) {
            console.warn("Attempted to copy project without permission or user session.");
            return;
        }
        try {
            setLoading(true);
            await copyProject(projectId, user.id);
        } catch (error) {
            console.error('Error copying project:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteProject = async (projectId: number) => {
        if (!canEditDma || !user) {
            console.warn("Attempted to delete project without permission or user session.");
            return;
        }
        if (!window.confirm('Are you sure you want to delete this project?')) {
            return;
        }
        try {
            setLoading(true);
            await deleteProject(projectId, user.id);
            if (selectedProjectId === projectId) {
                setSelectedProjectId(null);
            }
        } catch (error) {
            console.error('Error deleting project:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSelectProject = (projectId: number | 'new') => {
        if (projectId === 'new' && !canEditDma) {
            return;
        }
        setSelectedProjectId(projectId);
    };

    const handleNext = async () => {
        if (!selectedProjectId) {
            setShowWarningModal(true);
            return;
        }

        if (selectedProjectId === 'new') {
            const validationErrors = validateFields();
            if (Object.keys(validationErrors).length > 0) {
                setErrors(validationErrors);
                return;
            }
            await handleCreateProject();
        } else {
            await handleLoadProject(selectedProjectId);
        }
    };

    if (!loading && !canReadDma) {
        return (
            <Container className="text-center mt-5">
                <h1>Access Denied</h1>
                <p>You do not have permission to view Double Materiality Analysis projects.</p>
            </Container>
        );
    }

    return (
        <Container className="project-container">
            <h1>Choose a project</h1>
            <p>
                {canEditDma
                    ? 'Start your Double Materiality analysis by creating a new project or loading an existing one.'
                    : 'Select a project to view its Double Materiality analysis.'}
            </p>

            <Row className="project-grid-wrapper">
                {canEditDma && (
                    <Col md={4} sm={6} xs={12} className="fade-in">
                        <Card
                            className={`project-tile ${selectedProjectId === 'new' ? 'selected' : ''}`}
                            onClick={() => handleSelectProject('new')}
                        >
                            <Card.Body>
                                <div className="position-relative">
                                    <div className="d-flex align-items-center">
                                        <div>
                                            <i className="bi bi-plus-circle" style={{fontSize: '3rem'}}></i>
                                        </div>
                                        <div className="h4 ms-3">Create New Project</div>
                                    </div>
                                    {selectedProjectId === 'new' && (
                                        <div className="mt-3">
                                            <Form>
                                                <Form.Group controlId="projectName">
                                                    <Form.Label>Project Name</Form.Label>
                                                    <Form.Control
                                                        type="text" value={projectName}
                                                        onChange={(e) => setProjectName(e.target.value)}
                                                        placeholder="Enter project name" isInvalid={!!errors.projectName}/>
                                                    <Form.Control.Feedback type="invalid">{errors.projectName}</Form.Control.Feedback>
                                                </Form.Group>
                                                <Form.Group controlId="projectType" className="mt-3">
                                                    <Form.Label>Project Type</Form.Label>
                                                    <div className="d-flex mt-2">
                                                        <Form.Check inline label="Company" type="radio" name="projectType" value="company" checked={projectType === 'company'} onChange={() => setProjectType('company')}/>
                                                        <Form.Check inline label="Company Group" type="radio" name="projectType" value="companyGroup" checked={projectType === 'companyGroup'} onChange={() => setProjectType('companyGroup')}/>
                                                    </div>
                                                    {errors.projectType && <div className="text-danger mt-1">{errors.projectType}</div>}
                                                </Form.Group>
                                                <Form.Group controlId="projectDescription" className="mt-3">
                                                    <Form.Label>Project Description</Form.Label>
                                                    <Form.Control as="textarea" rows={3} value={projectDescription} onChange={(e) => setProjectDescription(e.target.value)} placeholder="Enter project description" isInvalid={!!errors.projectDescription}/>
                                                    <Form.Control.Feedback type="invalid">{errors.projectDescription}</Form.Control.Feedback>
                                                </Form.Group>
                                            </Form>
                                        </div>
                                    )}
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                )}

                {loading ? (
                    <div className="text-center w-100">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </Spinner>
                    </div>
                ) : userProjects.length > 0 ? (
                    userProjects.map((project) => (
                        <Col md={4} sm={6} xs={12} key={project.id} className="fade-in">
                            <Card
                                className={`project-tile ${selectedProjectId === project.id ? 'selected' : ''}`}
                                onClick={() => handleSelectProject(project.id)}
                            >
                                <Card.Body>
                                    <div className="position-relative">
                                        <div className="d-flex align-items-center">
                                            <div>
                                                <img src={imagesData(project.projectType === 'company' ? 'company_small' : 'company_big')} alt={project.projectType === 'company' ? 'Company' : 'Company Group'} style={{width: '80px', height: '80px', objectFit: 'cover'}}/>
                                            </div>
                                            <div className="ms-3">
                                                <Card.Title>{project.projectName}</Card.Title>
                                            </div>
                                        </div>
                                        <div className="mt-3">
                                            <p><strong>Project Type:</strong> {project.projectType === 'company' ? 'Company' : 'Company Group'}</p>
                                            <p><strong>Description:</strong> {project.projectDescription || 'No description available.'}</p>
                                        </div>
                                        {canEditDma && (
                                            <div className="d-flex justify-content-end">
                                                <i
                                                    className={`bi bi-files action-icon me-2 ${isActionsDisabled ? 'disabled-icon' : ''}`}
                                                    title={isActionsDisabled ? "Action disabled" : "Copy Project"}
                                                    onClick={isActionsDisabled ? undefined : (e) => {
                                                        e.stopPropagation();
                                                        handleCopyProject(project.id);
                                                    }}
                                                ></i>
                                                <i
                                                    className={`bi bi-trash action-icon ${isActionsDisabled ? 'disabled-icon' : ''}`}
                                                    title={isActionsDisabled ? "Action disabled" : "Delete Project"}
                                                    onClick={isActionsDisabled ? undefined : (e) => {
                                                        e.stopPropagation();
                                                        handleDeleteProject(project.id);
                                                    }}
                                                ></i>
                                            </div>
                                        )}
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    ))
                ) : (
                    <Col>
                        <p className="text-center mt-4">
                            {canEditDma
                                ? 'No projects found. Create your first project!'
                                : 'No projects have been assigned to you for viewing.'}
                        </p>
                    </Col>
                )}
            </Row>

            <div className="d-flex justify-content-end mt-4">
                <Button variant="primary" onClick={handleNext} disabled={loading}>
                    Next
                </Button>
            </div>

            <Modal show={showWarningModal} onHide={() => setShowWarningModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>No Project Selected</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {canEditDma
                        ? 'Please create a new project or select an existing one to continue.'
                        : 'Please select a project to continue.'}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="primary" onClick={() => setShowWarningModal(false)}>
                        OK
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default CreateNewProjectDMA;