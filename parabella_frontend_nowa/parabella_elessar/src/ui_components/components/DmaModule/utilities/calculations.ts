interface ImpactInputs {
    scope: number;
    scale: number;
    irreversibility: number;
    probability: number;
    financialImpact: number;
    timeSpan: number;
}


export const calculateImpactMaterialityActual = (inputs: ImpactInputs, isOpportunity: boolean): number => {
    let result;
    if (isOpportunity) {
        result = (+inputs.scope + +inputs.scale) / 2;
        console.log(`calculateImpactMaterialityActual (Opportunity): scope=${inputs.scope}, scale=${inputs.scale}, result=${result}`);
    } else {
        result = (+inputs.scope + +inputs.scale + +inputs.irreversibility) / 3;
        //console.log(`calculateImpactMaterialityActual: scope=${inputs.scope}, scale=${inputs.scale}, irreversibility=${inputs.irreversibility}, result=${result}`);
    }
    return parseFloat(result.toFixed(2)); // Round to 2 decimal points
};

export const calculateImpactMaterialityPotential = (actualImpact: number, probability: number): number => {
    const result = actualImpact * probability;
    //console.log(`calculateImpactMaterialityPotential: actualImpact=${actualImpact}, probability=${probability}, result=${result}`);
    return parseFloat(result.toFixed(2)); // Round to 2 decimal points
};

export const calculateFinancialMaterialityPotential = (actualImpact: number, timeSpan: number): number => {
    const result = actualImpact * timeSpan;
    //console.log(`calculateFinancialMaterialityPotential: actualImpact=${actualImpact}, timeSpan=${timeSpan}, result=${result}`);
    return parseFloat(result.toFixed(2)); // Round to 2 decimal points
};

export const calculateNumericResultMaterialityAssessment = (impactPotential: number, financialPotential: number): number => {
    const result = impactPotential + financialPotential;
    //console.log(`calculateNumericResultMaterialityAssessment: impactPotential=${impactPotential}, financialPotential=${financialPotential}, result=${result}`);
    return parseFloat(result.toFixed(2)); // Round to 2 decimal points
};

export const calculateResultMaterialityAssessment = (impactPotential: number, financialPotential: number): string => {
    const result = (impactPotential >= 2.5 || financialPotential >= 2.5) ? "relevant" : "not relevant";
    //console.log(`calculateResultMaterialityAssessment: impactPotential=${impactPotential}, financialPotential=${financialPotential}, result=${result}`);
    return result;
};
