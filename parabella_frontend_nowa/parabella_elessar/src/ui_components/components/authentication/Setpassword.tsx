import { FC, Fragment, useState, FormEvent, useEffect } from 'react';
import { Link, useParams, useNavigate, useLocation } from 'react-router-dom';
import { imagesData } from '../../templateLogic/commonimages';
import axios from "axios";
import { API_BASE_URL } from "../../../config/APIEndpoints.ts";
import { useAuth } from "../../../services/AuthContext.tsx";
import { getCurrentUser } from "../../../services/authService.ts";

interface ComponentProps {}

const SetPassword: FC<ComponentProps> = () => {
    // Retrieve token from the URL path, same as Resetpassword
    const { token } = useParams<{ token: string }>();
    const navigate = useNavigate();
    const location = useLocation();
    const { logout, isAuthenticated } = useAuth();

    // Local state for the form
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [showSessionWarning, setShowSessionWarning] = useState(false);

    // Check if there's an active session when component mounts
    useEffect(() => {
        const currentUser = getCurrentUser();
        if (currentUser && isAuthenticated) {
            setShowSessionWarning(true);
        }
    }, [isAuthenticated]);

    const handleClearSession = async () => {
        await logout();
        setShowSessionWarning(false);
        setError('');
        setSuccessMessage('Session cleared. You can now set up the new user account.');
    };

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setError('');
        setSuccessMessage('');

        // Frontend validation
        if (!token) {
            setError('No invitation token found. The link may be invalid or expired.');
            return;
        }
        if (!newPassword || newPassword.length < 6) {
            setError('Password must be at least 6 characters long.');
            return;
        }
        if (newPassword !== confirmPassword) {
            setError('The new passwords do not match.');
            return;
        }

        setLoading(true);

        const payload = {
            token,
            password: newPassword,
        };

        try {
            // *** CHANGE: Call the '/auth/set-password' endpoint for invited users ***
            const response = await axios.post(`${API_BASE_URL}/auth/set-password`, payload);

            setSuccessMessage(response.data || 'Your password has been set successfully!');

            // Redirect to login after a delay
            setTimeout(() => {
                navigate(`${import.meta.env.BASE_URL}authentication/login`);
            }, 3000);

        } catch (err: any) {
            console.error('Error setting password:', err);
            if (err.response && err.response.data) {
                setError(err.response.data);
            } else {
                setError('An unknown error occurred. Please try again or contact support.');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <Fragment>
            <div className="cover-image forgot-page">
                <div className="container">
                    <div className="row justify-content-center align-items-center authentication authentication-basic h-100">
                        <div className="col-xl-5 col-lg-6 col-md-8 col-sm-8 col-xs-10 card-sigin-main py-4 justify-content-center mx-auto">
                            <div className="card-sigin">
                                <div className="main-card-signin d-md-flex">
                                    <div className="wd-100p">
                                        <div className="d-flex mb-3">
                                            <img
                                                src={imagesData('parabella_logo')}
                                                className="sign-favicon ht-30"
                                                alt="logo"
                                            />
                                        </div>
                                        <div className="mb-1">
                                            <div className="main-signin-header">
                                                <div>
                                                    {/* Changed Title for Invitation Flow */}
                                                    <h2>Welcome!</h2>
                                                    <h4 className="text-start">Create Your Account Password</h4>

                                                    {showSessionWarning && (
                                                        <div className="alert alert-warning">
                                                            <h6>⚠️ Active Session Detected</h6>
                                                            <p>You're currently logged in as another user. To set up this new user account, you should clear your current session first.</p>
                                                            <button 
                                                                type="button"
                                                                className="btn btn-warning btn-sm me-2"
                                                                onClick={handleClearSession}
                                                            >
                                                                Clear Session & Continue
                                                            </button>
                                                            <button 
                                                                type="button"
                                                                className="btn btn-outline-secondary btn-sm"
                                                                onClick={() => setShowSessionWarning(false)}
                                                            >
                                                                Continue Anyway
                                                            </button>
                                                            <br />
                                                            <small className="text-muted">
                                                                Or <a href={`${location.pathname}?logout=true`} className="text-decoration-none">
                                                                    click here to logout and refresh the page
                                                                </a>
                                                            </small>
                                                        </div>
                                                    )}

                                                    {error && <div className="alert alert-danger">{error}</div>}
                                                    {successMessage && <div className="alert alert-success">{successMessage}</div>}

                                                    {!successMessage && (
                                                        <form onSubmit={handleSubmit}>
                                                            <div className="form-group text-start">
                                                                <label>Password</label>
                                                                <input
                                                                    className="form-control"
                                                                    placeholder="Enter your password"
                                                                    type="password"
                                                                    value={newPassword}
                                                                    onChange={(e) => setNewPassword(e.target.value)}
                                                                    required
                                                                />
                                                            </div>

                                                            <div className="form-group text-start">
                                                                <label>Confirm Password</label>
                                                                <input
                                                                    className="form-control"
                                                                    placeholder="Confirm your password"
                                                                    type="password"
                                                                    value={confirmPassword}
                                                                    onChange={(e) => setConfirmPassword(e.target.value)}
                                                                    required
                                                                />
                                                            </div>

                                                            <button
                                                                type="submit"
                                                                className="btn ripple btn-primary btn-block"
                                                                disabled={loading}
                                                            >
                                                                {loading ? 'Setting Password...' : 'Set Password and Activate Account'}
                                                            </button>
                                                        </form>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="main-signup-footer mt-3 text-center">
                                                <p>
                                                    Already have an account?{' '}
                                                    <Link to={`${import.meta.env.BASE_URL}authentication/login`}>
                                                        Sign In
                                                    </Link>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Fragment>
    );
};

export default SetPassword;