import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button, ProgressBar } from 'react-bootstrap';
import { useAuth } from '../../../services/AuthContext';
import { refreshAccessToken } from '../../../services/authService';
import { tokenService } from '../../../services/tokenService';

/**
 * SessionTimeoutWarning Component
 * Displays a warning modal when the user's session is about to expire.
 * Allows the user to extend their session or logout gracefully.
 */
export const SessionTimeoutWarning: React.FC = () => {
    const { logout, isAuthenticated } = useAuth();
    const [showWarning, setShowWarning] = useState(false);
    const [remainingTime, setRemainingTime] = useState(0);
    const [isExtending, setIsExtending] = useState(false);
    
    // Warning threshold: 2 minutes before expiry
    const WARNING_THRESHOLD = 2 * 60 * 1000; // 2 minutes in ms
    const CHECK_INTERVAL = 30 * 1000; // Check every 30 seconds
    
    useEffect(() => {
        if (!isAuthenticated) return;
        
        const checkSessionExpiry = () => {
            const remaining = tokenService.getRemainingSessionTime();
            
            if (remaining > 0 && remaining <= WARNING_THRESHOLD) {
                setShowWarning(true);
                setRemainingTime(remaining);
            } else {
                setShowWarning(false);
            }
        };
        
        // Initial check
        checkSessionExpiry();
        
        // Set up interval
        const interval = setInterval(checkSessionExpiry, CHECK_INTERVAL);
        
        return () => clearInterval(interval);
    }, [isAuthenticated]);
    
    useEffect(() => {
        if (!showWarning) return;
        
        // Update remaining time every second when warning is shown
        const countdown = setInterval(() => {
            const remaining = tokenService.getRemainingSessionTime();
            setRemainingTime(remaining);
            
            if (remaining <= 0) {
                // Session expired, force logout
                handleLogout();
            }
        }, 1000);
        
        return () => clearInterval(countdown);
    }, [showWarning]);
    
    const handleExtendSession = async () => {
        setIsExtending(true);
        try {
            const newToken = await refreshAccessToken();
            if (newToken) {
                setShowWarning(false);
                // Show success notification (you might want to add a toast library)
                console.log('Session extended successfully');
            } else {
                // Refresh failed, logout
                handleLogout();
            }
        } catch (error) {
            console.error('Failed to extend session:', error);
            handleLogout();
        } finally {
            setIsExtending(false);
        }
    };
    
    const handleLogout = () => {
        setShowWarning(false);
        logout();
    };
    
    const formatTime = (ms: number): string => {
        const totalSeconds = Math.max(0, Math.floor(ms / 1000));
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };
    
    const progressPercentage = (remainingTime / WARNING_THRESHOLD) * 100;
    
    return (
        <Modal 
            show={showWarning} 
            onHide={() => {}} // Prevent closing by clicking outside
            backdrop="static"
            keyboard={false}
            centered
        >
            <Modal.Header>
                <Modal.Title>
                    <i className="bi bi-clock-history me-2"></i>
                    Session Expiring Soon
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p className="mb-3">
                    Your session will expire in <strong>{formatTime(remainingTime)}</strong>.
                </p>
                <p className="mb-3">
                    Would you like to extend your session?
                </p>
                <ProgressBar 
                    now={progressPercentage} 
                    variant={progressPercentage > 30 ? "warning" : "danger"}
                    animated
                    striped
                />
            </Modal.Body>
            <Modal.Footer>
                <Button 
                    variant="secondary" 
                    onClick={handleLogout}
                    disabled={isExtending}
                >
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                </Button>
                <Button 
                    variant="primary" 
                    onClick={handleExtendSession}
                    disabled={isExtending}
                >
                    {isExtending ? (
                        <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Extending...
                        </>
                    ) : (
                        <>
                            <i className="bi bi-arrow-clockwise me-2"></i>
                            Extend Session
                        </>
                    )}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};