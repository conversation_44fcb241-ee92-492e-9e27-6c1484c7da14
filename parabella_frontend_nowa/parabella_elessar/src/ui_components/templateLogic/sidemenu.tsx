const pagessvg = <svg xmlns="http://www.w3.org/2000/svg" className="side-menu__icon" width="24" height="24"
                      viewBox="0 0 24 24">
  <path
      d="M22 7.999a1 1 0 0 0-.516-.874l-9.022-5a1.003 1.003 0 0 0-.968 0l-8.978 4.96a1 1 0 0 0-.003 1.748l9.022 5.04a.995.995 0 0 0 .973.001l8.978-5A1 1 0 0 0 22 7.999zm-9.977 3.855L5.06 7.965l6.917-3.822 6.964 3.859-6.918 3.852z"/>
  <path d="M20.515 11.126 12 15.856l-8.515-4.73-.971 1.748 9 5a1 1 0 0 0 .971 0l9-5-.97-1.748z"/>
  <path d="M20.515 15.126 12 19.856l-8.515-4.73-.971 1.748 9 5a1 1 0 0 0 .971 0l9-5-.97-1.748z"/>
</svg>

const zahnrad = <svg version="1.1" xmlns="http://www.w3.org/2000/svg" className="side-menu__icon" fill="currentColor"
                     width="26" height="26" viewBox="0 0 18 18">
  <path fill="#444444"
        d="M15.2 6l-1.1-0.2c-0.1-0.2-0.1-0.4-0.2-0.6l0.6-0.9 0.5-0.7-2.6-2.6-0.7 0.5-0.9 0.6c-0.2-0.1-0.4-0.1-0.6-0.2l-0.2-1.1-0.2-0.8h-3.6l-0.2 0.8-0.2 1.1c-0.2 0.1-0.4 0.1-0.6 0.2l-0.9-0.6-0.7-0.4-2.5 2.5 0.5 0.7 0.6 0.9c-0.2 0.2-0.2 0.4-0.3 0.6l-1.1 0.2-0.8 0.2v3.6l0.8 0.2 1.1 0.2c0.1 0.2 0.1 0.4 0.2 0.6l-0.6 0.9-0.5 0.7 2.6 2.6 0.7-0.5 0.9-0.6c0.2 0.1 0.4 0.1 0.6 0.2l0.2 1.1 0.2 0.8h3.6l0.2-0.8 0.2-1.1c0.2-0.1 0.4-0.1 0.6-0.2l0.9 0.6 0.7 0.5 2.6-2.6-0.5-0.7-0.6-0.9c0.1-0.2 0.2-0.4 0.2-0.6l1.1-0.2 0.8-0.2v-3.6l-0.8-0.2zM15 9l-1.7 0.3c-0.1 0.5-0.3 1-0.6 1.5l0.9 1.4-1.4 1.4-1.4-0.9c-0.5 0.3-1 0.5-1.5 0.6l-0.3 1.7h-2l-0.3-1.7c-0.5-0.1-1-0.3-1.5-0.6l-1.4 0.9-1.4-1.4 0.9-1.4c-0.3-0.5-0.5-1-0.6-1.5l-1.7-0.3v-2l1.7-0.3c0.1-0.5 0.3-1 0.6-1.5l-1-1.4 1.4-1.4 1.4 0.9c0.5-0.3 1-0.5 1.5-0.6l0.4-1.7h2l0.3 1.7c0.5 0.1 1 0.3 1.5 0.6l1.4-0.9 1.4 1.4-0.9 1.4c0.3 0.5 0.5 1 0.6 1.5l1.7 0.3v2z"></path>
  <path
      d="M8 4.5c-1.9 0-3.5 1.6-3.5 3.5s1.6 3.5 3.5 3.5 3.5-1.6 3.5-3.5c0-1.9-1.6-3.5-3.5-3.5zM8 10.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5c0 1.4-1.1 2.5-2.5 2.5z"></path>
</svg>


const tablessvg = <svg xmlns="http://www.w3.org/2000/svg" className="side-menu__icon" width="24" height="24"
                       viewBox="0 0 24 24">
  <path
      d="M19 3H5c-1.103 0-2 .897-2 2v14c0 1.103.897 2 2 2h14c1.103 0 2-.897 2-2V5c0-1.103-.897-2-2-2zm0 2 .001 4H5V5h14zM5 11h8v8H5v-8zm10 8v-8h4.001l.001 8H15z"/>
</svg>


const booksvg = <svg xmlns="http://www.w3.org/2000/svg" className="side-menu__icon" width="20" height="20"
                     fill="currentColor"
                     viewBox="0 -2 20 20">
  <path
      d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"/>
</svg>

const shufflesvg = <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="currentColor"
                        className="side-menu__icon" viewBox="-1 0 18 18">
  <path fill-rule="evenodd"
        d="M0 3.5A.5.5 0 0 1 .5 3H1c2.202 0 3.827 1.24 4.874 2.418.49.552.865 1.102 1.126 1.532.26-.43.636-.98 1.126-1.532C9.173 4.24 10.798 3 13 3v1c-1.798 0-3.173 1.01-4.126 2.082A9.624 9.624 0 0 0 7.556 8a9.624 9.624 0 0 0 1.317 1.918C9.828 10.99 11.204 12 13 12v1c-2.202 0-3.827-1.24-4.874-2.418A10.595 10.595 0 0 1 7 9.05c-.26.43-.636.98-1.126 1.532C4.827 11.76 3.202 13 1 13H.5a.5.5 0 0 1 0-1H1c1.798 0 3.173-1.01 4.126-2.082A9.624 9.624 0 0 0 6.444 8a9.624 9.624 0 0 0-1.317-1.918C4.172 5.01 2.796 4 1 4H.5a.5.5 0 0 1-.5-.5z"/>
  <path
      d="M13 5.466V1.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384l-2.36 1.966a.25.25 0 0 1-.41-.192zm0 9v-3.932a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384l-2.36 1.966a.25.25 0 0 1-.41-.192z"/>
</svg>

const personsvg = <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="currentColor"
                       className="side-menu__icon" viewBox="0 0 18 18">
  <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
</svg>

const analytics = <svg width="22" height="22" viewBox="0 0 18 18" fill="currentColor" xmlns="http://www.w3.org/2000/svg"
                       className="side-menu__icon">

  <rect x="3" y="11" width="2" height="6" rx="0.5"/>
  <rect x="6" y="8" width="2" height="9" rx="0.5"/>
  <rect x="9" y="13" width="2" height="4" rx="0.5"/>
  <rect x="12" y="10" width="2" height="7" rx="0.5"/>


  <circle cx="8.5" cy="3.5" r="1.2"/>


  <circle cx="5.8" cy="2.5" r="0.9"/>
  <circle cx="11.2" cy="2.5" r="0.9"/>
  <circle cx="8.5" cy="5.8" r="0.9"/>
</svg>


const productCarbonFootprintCalcIcon = (
    <svg
        width="22" // You can adjust this or control with CSS
        height="22" // You can adjust this or control with CSS
        viewBox="0 0 18 18"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        className="side-menu__icon" // Or your desired class name
    >
      {/* Product Box */}
      <rect x="1.5" y="7.5" width="6" height="6" rx="1" />
      {/* Tape line on the box (will also be currentColor) */}
      <rect x="1.5" y="10" width="6" height="1" />

      {/* CO2 Text */}
      {/* Using dominant-baseline and text-anchor for better centering of individual letters */}
      <text
          x="10.5"
          y="3.8"
          fontFamily="Arial, sans-serif"
          fontSize="3"
          fontWeight="bold"
          textAnchor="middle"
          dominantBaseline="central"
      >
        C
      </text>
      <text
          x="10.5"
          y="6.8"
          fontFamily="Arial, sans-serif"
          fontSize="3"
          fontWeight="bold"
          textAnchor="middle"
          dominantBaseline="central"
      >
        O
      </text>
      <text
          x="12"
          y="7.6"
          fontFamily="Arial, sans-serif"
          fontSize="2"
          fontWeight="bold"
          textAnchor="middle"
          dominantBaseline="central"
      >
        2
      </text>

      {/* Calculator symbols (Minus/Equals) */}
      <rect x="9.5" y="10.5" width="5" height="1" rx="0.5" /> {/* Minus or generic bar */}
      <rect x="9.5" y="12.5" width="5" height="1" rx="0.5" /> {/* Equals bar 1 */}
      <rect x="9.5" y="14.5" width="5" height="1" rx="0.5" /> {/* Equals bar 2 */}
    </svg>
);

const finance = (
    <svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="brainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#FFC107;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#F44336;stop-opacity:1" />
        </linearGradient>
      </defs>

      <rect width="200" height="200" fill="#f0f4f8"/>

      <path d="M100 20 C50 20 50 70 100 70 C150 70 150 20 100 20 Z M100 70 C80 70 80 100 100 100 C120 100 120 70 100 70" fill="url(#brainGradient)"/>
      <path d="M90 70 C90 50 70 50 70 70 C70 90 90 90 90 70 Z" fill="#fff" opacity="0.8"/>
      <path d="M110 70 C110 50 130 50 130 70 C130 90 110 90 110 70 Z" fill="#fff" opacity="0.8"/>
      <circle cx="100" cy="95" r="5" fill="#fff"/>
      <path d="M100 100 L100 180" stroke="#2196F3" stroke-width="4" stroke-linecap="round"/>

      <polyline points="20,150 60,110 100,130 140,90 180,110" fill="none" stroke="url(#chartGradient)" stroke-width="6" stroke-linecap="round"/>
      <circle cx="20" cy="150" r="5" fill="#F44336"/>
      <circle cx="60" cy="110" r="5" fill="#FFC107"/>
      <circle cx="100" cy="130" r="5" fill="#4CAF50"/>
      <circle cx="140" cy="90" r="5" fill="#2196F3"/>
      <circle cx="180" cy="110" r="5" fill="#9C27B0"/>

      <circle cx="150" cy="50" r="25" fill="none" stroke="#333" stroke-width="4"/>
      <line x1="168" y1="68" x2="185" y2="85" stroke="#333" stroke-width="4" stroke-linecap="round"/>
    </svg>

);


export interface Menuitemtype {
  menutitle?: string;
  path?: string;
  title?: string;
  icon?: any;
  type?: 'link' | 'empty' | 'sub';
  active?: boolean;
  selected?: boolean;
  dirchange?: boolean;
  children?: Menuitemtype[];
  badgetxt?: string;
  class?: string;
  menusub?: boolean;
}


export const MENUITEMS: Menuitemtype[] = [

  //
  // {
  //   //path: `${import.meta.env.BASE_URL}dashboard/dashboard`,
  //   title: "CSRD Reporting",
  //   icon: tablessvg,
  //   type: "link",
  //   active: false,
  //   selected: false,
  //   dirchange: false
  // },


  {menutitle: "ESG Reporting"},


  {
    path: `${import.meta.env.BASE_URL}mithril/CreateNewProjectDMA`,
    title: "Double Materiality",
    icon: shufflesvg,
    type: "link",
    active: false,
    selected: false,
    dirchange: false,

  },




  {
    path: `${import.meta.env.BASE_URL}csrd/create`,
    title: "CSRD",
    icon: tablessvg,
    type: "link",
    active: false,
    selected: false,
    dirchange: false
  },

  // {menutitle: "CO2 Calculation"},
  //
  // {
  //   path: `${import.meta.env.BASE_URL}csrd/create`,
  //   title: "PCF",
  //   icon: productCarbonFootprintCalcIcon,
  //   type: "link",
  //   active: false,
  //   selected: false,
  //   dirchange: false
  // },
  //
  // {
  //   path: `${import.meta.env.BASE_URL}csrd/create`,
  //   title: "CCF",
  //   icon: tablessvg,
  //   type: "link",
  //   active: false,
  //   selected: false,
  //   dirchange: false
  // },

  {menutitle: "Apollo AI"},
  {
    path: `${import.meta.env.BASE_URL}analytics`,
    title: "Analytics",
    icon: analytics,
    type: "link",
    active: false,
    selected: false,
    dirchange: false
  },

  {
    path: `${import.meta.env.BASE_URL}finance`,
    title: "Finance",
    icon: analytics,
    type: "link",
    active: false,
    selected: false,
    dirchange: false
  },


  {menutitle: "Support"},

  {
    path: `${import.meta.env.BASE_URL}pages/settings`,
    title: "Settings",
    icon: zahnrad,
    type: "link",
    active: false,
    selected: false,
    dirchange: false
  },
  {
    path: `${import.meta.env.BASE_URL}pages/aboutus`,
    title: "Contact",
    icon: personsvg,
    type: "link",
    active: false,
    selected: false,
    dirchange: false
  },

];