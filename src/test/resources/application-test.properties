# Test Configuration
spring.profiles.active=test

# H2 Database Configuration for Unit Tests
spring.datasource.url=jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.defer-datasource-initialization=true

# Multi-database configuration
spring.datasource.csrd.url=jdbc:h2:mem:testdb_csrd;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
spring.datasource.csrd.username=sa
spring.datasource.csrd.password=
spring.datasource.csrd.driver-class-name=org.h2.Driver

spring.datasource.vector.url=jdbc:h2:mem:testdb_vector;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
spring.datasource.vector.username=sa
spring.datasource.vector.password=
spring.datasource.vector.driver-class-name=org.h2.Driver

# Security Configuration
spring.security.user.name=testuser
spring.security.user.password=testpass

# JWT Configuration for Tests
jwt.secret=testSecretKeyForJwtTokenGenerationThatIsLongEnoughForHS512Algorithm
jwt.expiration=86400000

# Mail Configuration (Mock)
spring.mail.host=localhost
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=testpass
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Logging Configuration
logging.level.com.example.parabella_csrd_db=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.sql=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Disable Flyway for tests
spring.flyway.enabled=false

# Test-specific settings - using H2 database for tests