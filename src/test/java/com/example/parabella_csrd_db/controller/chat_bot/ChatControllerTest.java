package com.example.parabella_csrd_db.controller.chat_bot;

import com.example.parabella_csrd_db.config.TestConfig;
import com.example.parabella_csrd_db.config.TestSecurityConfig;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.chatBot.Conversation;
import com.example.parabella_csrd_db.database.maindatabase.model.chatBot.Message;
import com.example.parabella_csrd_db.dto.ai.ChatMessageRequest;
import com.example.parabella_csrd_db.dto.ai.ChatMessageResponse;
import com.example.parabella_csrd_db.dto.ai.MessageDTO;
import com.example.parabella_csrd_db.dto.ai.mithril.DescriptionEffectRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.ExtendIrrelevanceReasonRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.ReasonForIrrelevanceRequest;
import com.example.parabella_csrd_db.security.services.UserDetailsServiceImpl;
import com.example.parabella_csrd_db.service.ai.PromptingService;
import com.example.parabella_csrd_db.service.chat_bot.ConversationService;
import com.example.parabella_csrd_db.service.chat_bot.OpenAIService;
import com.example.parabella_csrd_db.service.mithril.ValueChainObjectService;
import com.example.parabella_csrd_db.utils.TestDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ChatController.class)
@ActiveProfiles("test")
@Import({TestConfig.class, TestSecurityConfig.class})
@DisplayName("Chat Controller Tests")
class ChatControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ConversationService conversationService;

    @MockBean
    private OpenAIService openAIService;

    @MockBean
    private UserDetailsServiceImpl userService;

    @MockBean
    private ValueChainObjectService valueChainObjectService;

    @MockBean
    private PromptingService promptingService;

    private User testUser;
    private Conversation testConversation;
    private ChatMessageRequest testChatRequest;

    @BeforeEach
    void setUp() {
        testUser = TestDataBuilder.aUser().build();
        testConversation = new Conversation();
        testConversation.setId(1L);
        testConversation.setUser(testUser);
        
        testChatRequest = new ChatMessageRequest();
        testChatRequest.setMessage("What is CSRD compliance?");
    }

    @Nested
    @DisplayName("Send Message Tests")
    class SendMessageTests {

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should send message and return AI response")
        void whenSendMessage_withValidRequest_thenReturnsResponse() throws Exception {
            // Arrange
            String expectedResponse = "CSRD stands for Corporate Sustainability Reporting Directive.";
            List<Map<String, String>> mockMessages = Arrays.asList(
                Map.of("role", "user", "content", "What is CSRD compliance?")
            );

            when(userService.findByUsername("testuser")).thenReturn(testUser);
            when(conversationService.getOrCreateConversation(testUser)).thenReturn(testConversation);
            doNothing().when(conversationService).addMessage(eq(testConversation), eq("user"), anyString());
            when(conversationService.buildMessages(testConversation)).thenReturn(mockMessages);
            when(openAIService.getChatCompletion(mockMessages)).thenReturn(expectedResponse);
            doNothing().when(conversationService).addMessage(eq(testConversation), eq("assistant"), eq(expectedResponse));

            // Act & Assert
            mockMvc.perform(post("/api/chat/send")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testChatRequest)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.message").value(expectedResponse));

            // Verify interactions
            verify(userService).findByUsername("testuser");
            verify(conversationService).getOrCreateConversation(testUser);
            verify(conversationService).addMessage(testConversation, "user", testChatRequest.getMessage());
            verify(openAIService).getChatCompletion(mockMessages);
            verify(conversationService).addMessage(testConversation, "assistant", expectedResponse);
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle empty message")
        void whenSendMessage_withEmptyMessage_thenProcessesRequest() throws Exception {
            // Arrange
            ChatMessageRequest emptyRequest = new ChatMessageRequest();
            emptyRequest.setMessage("");
            String expectedResponse = "Please provide a message.";

            when(userService.findByUsername("testuser")).thenReturn(testUser);
            when(conversationService.getOrCreateConversation(testUser)).thenReturn(testConversation);
            when(conversationService.buildMessages(testConversation)).thenReturn(Arrays.asList());
            when(openAIService.getChatCompletion(any())).thenReturn(expectedResponse);

            // Act & Assert
            mockMvc.perform(post("/api/chat/send")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(emptyRequest)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.message").value(expectedResponse));
        }

        @Test
        @DisplayName("Should return unauthorized when not authenticated")
        void whenSendMessage_withoutAuthentication_thenReturnsUnauthorized() throws Exception {
            // Act & Assert
            mockMvc.perform(post("/api/chat/send")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testChatRequest)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized());

            verify(userService, never()).findByUsername(any());
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle OpenAI service exceptions")
        void whenSendMessage_withOpenAIException_thenReturnsServerError() throws Exception {
            // Arrange
            when(userService.findByUsername("testuser")).thenReturn(testUser);
            when(conversationService.getOrCreateConversation(testUser)).thenReturn(testConversation);
            when(conversationService.buildMessages(testConversation)).thenReturn(Arrays.asList());
            when(openAIService.getChatCompletion(any())).thenThrow(new RuntimeException("OpenAI API error"));

            // Act & Assert
            mockMvc.perform(post("/api/chat/send")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testChatRequest)))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle invalid request body")
        void whenSendMessage_withInvalidRequest_thenReturnsBadRequest() throws Exception {
            // Act & Assert
            mockMvc.perform(post("/api/chat/send")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content("{ invalid json }"))
                    .andDo(print())
                    .andExpect(status().isBadRequest());
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle conversation service exceptions")
        void whenSendMessage_withConversationServiceException_thenReturnsServerError() throws Exception {
            // Arrange
            when(userService.findByUsername("testuser")).thenReturn(testUser);
            when(conversationService.getOrCreateConversation(testUser))
                    .thenThrow(new RuntimeException("Database error"));

            // Act & Assert
            mockMvc.perform(post("/api/chat/send")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testChatRequest)))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());
        }
    }

    @Nested
    @DisplayName("Conversation History Tests")
    class ConversationHistoryTests {

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should return conversation history")
        void whenGetConversationHistory_withExistingHistory_thenReturnsMessages() throws Exception {
            // Arrange
            Message message1 = new Message();
            message1.setId(1L);
            message1.setRole("user");
            message1.setContent("Hello");
            message1.setTimestamp(LocalDateTime.now());

            Message message2 = new Message();
            message2.setId(2L);
            message2.setRole("assistant");
            message2.setContent("Hi there!");
            message2.setTimestamp(LocalDateTime.now());

            testConversation.setMessages(Arrays.asList(message1, message2));

            when(userService.findByUsername("testuser")).thenReturn(testUser);
            when(conversationService.getOrCreateConversation(testUser)).thenReturn(testConversation);

            // Act & Assert
            mockMvc.perform(get("/api/chat/history"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.length()").value(2))
                    .andExpect(jsonPath("$[0].id").value(1))
                    .andExpect(jsonPath("$[0].role").value("user"))
                    .andExpect(jsonPath("$[0].content").value("Hello"))
                    .andExpect(jsonPath("$[1].id").value(2))
                    .andExpect(jsonPath("$[1].role").value("assistant"))
                    .andExpect(jsonPath("$[1].content").value("Hi there!"));

            verify(userService).findByUsername("testuser");
            verify(conversationService).getOrCreateConversation(testUser);
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should return empty history for new conversation")
        void whenGetConversationHistory_withNewConversation_thenReturnsEmptyList() throws Exception {
            // Arrange
            testConversation.setMessages(Arrays.asList());

            when(userService.findByUsername("testuser")).thenReturn(testUser);
            when(conversationService.getOrCreateConversation(testUser)).thenReturn(testConversation);

            // Act & Assert
            mockMvc.perform(get("/api/chat/history"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.length()").value(0));
        }

        @Test
        @DisplayName("Should return unauthorized when not authenticated")
        void whenGetConversationHistory_withoutAuthentication_thenReturnsUnauthorized() throws Exception {
            // Act & Assert
            mockMvc.perform(get("/api/chat/history"))
                    .andDo(print())
                    .andExpect(status().isUnauthorized());

            verify(userService, never()).findByUsername(any());
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle service exceptions gracefully")
        void whenGetConversationHistory_withServiceException_thenReturnsServerError() throws Exception {
            // Arrange
            when(userService.findByUsername("testuser")).thenReturn(testUser);
            when(conversationService.getOrCreateConversation(testUser))
                    .thenThrow(new RuntimeException("Database connection error"));

            // Act & Assert
            mockMvc.perform(get("/api/chat/history"))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());
        }
    }

    @Nested
    @DisplayName("Generate Irrelevance Reason Tests")
    class GenerateIrrelevanceReasonTests {

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should generate irrelevance reason successfully")
        void whenGenerateIrrelevanceReason_withValidRequest_thenReturnsResponse() throws Exception {
            // Arrange
            ReasonForIrrelevanceRequest request = new ReasonForIrrelevanceRequest();
            // Set request properties as needed
            
            String mockPrompt = "Generate irrelevance reason for...";
            String expectedResponse = "This topic is not relevant because...";

            when(promptingService.buildIrrelevanceReasonPrompt(any())).thenReturn(mockPrompt);
            when(openAIService.getCompletionForReason(mockPrompt)).thenReturn(expectedResponse);

            // Act & Assert
            mockMvc.perform(post("/api/chat/generate-irrelevance-reason")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.message").value(expectedResponse));

            verify(promptingService).buildIrrelevanceReasonPrompt(any());
            verify(openAIService).getCompletionForReason(mockPrompt);
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle OpenAI service exceptions")
        void whenGenerateIrrelevanceReason_withOpenAIException_thenReturnsServerError() throws Exception {
            // Arrange
            ReasonForIrrelevanceRequest request = new ReasonForIrrelevanceRequest();
            String mockPrompt = "Generate irrelevance reason for...";

            when(promptingService.buildIrrelevanceReasonPrompt(any())).thenReturn(mockPrompt);
            when(openAIService.getCompletionForReason(mockPrompt))
                    .thenThrow(new RuntimeException("OpenAI API error"));

            // Act & Assert
            mockMvc.perform(post("/api/chat/generate-irrelevance-reason")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());
        }

        @Test
        @DisplayName("Should return unauthorized when not authenticated")
        void whenGenerateIrrelevanceReason_withoutAuthentication_thenReturnsUnauthorized() throws Exception {
            // Arrange
            ReasonForIrrelevanceRequest request = new ReasonForIrrelevanceRequest();

            // Act & Assert
            mockMvc.perform(post("/api/chat/generate-irrelevance-reason")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized());

            verify(promptingService, never()).buildIrrelevanceReasonPrompt(any());
        }
    }

    @Nested
    @DisplayName("Extend Irrelevance Reason Tests")
    class ExtendIrrelevanceReasonTests {

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should extend irrelevance reason successfully")
        void whenExtendIrrelevanceReason_withValidRequest_thenReturnsResponse() throws Exception {
            // Arrange
            ExtendIrrelevanceReasonRequest request = new ExtendIrrelevanceReasonRequest();
            // Set request properties as needed
            
            String mockPrompt = "Extend this irrelevance reason...";
            String expectedResponse = "The extended reason is...";

            when(promptingService.buildExtendIrrelevanceReasonPrompt(any())).thenReturn(mockPrompt);
            when(openAIService.getCompletionForReason(mockPrompt)).thenReturn(expectedResponse);

            // Act & Assert
            mockMvc.perform(post("/api/chat/extend-irrelevance-reason")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.message").value(expectedResponse));

            verify(promptingService).buildExtendIrrelevanceReasonPrompt(any());
            verify(openAIService).getCompletionForReason(mockPrompt);
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle prompting service exceptions")
        void whenExtendIrrelevanceReason_withPromptingException_thenReturnsServerError() throws Exception {
            // Arrange
            ExtendIrrelevanceReasonRequest request = new ExtendIrrelevanceReasonRequest();

            when(promptingService.buildExtendIrrelevanceReasonPrompt(any()))
                    .thenThrow(new RuntimeException("Prompting service error"));

            // Act & Assert
            mockMvc.perform(post("/api/chat/extend-irrelevance-reason")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());
        }

        @Test
        @DisplayName("Should return unauthorized when not authenticated")
        void whenExtendIrrelevanceReason_withoutAuthentication_thenReturnsUnauthorized() throws Exception {
            // Arrange
            ExtendIrrelevanceReasonRequest request = new ExtendIrrelevanceReasonRequest();

            // Act & Assert
            mockMvc.perform(post("/api/chat/extend-irrelevance-reason")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized());

            verify(promptingService, never()).buildExtendIrrelevanceReasonPrompt(any());
        }
    }

    @Nested
    @DisplayName("Generate Description Effect Tests")
    class GenerateDescriptionEffectTests {

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should generate description/effect successfully")
        void whenGenerateDescriptionEffect_withValidRequest_thenReturnsResponse() throws Exception {
            // Arrange
            DescriptionEffectRequest request = new DescriptionEffectRequest();
            request.setGenerationType("description");
            
            String mockPrompt = "Generate description for...";
            String expectedResponse = "The description is...";

            when(promptingService.buildDescriptionEffectPrompt(any())).thenReturn(mockPrompt);
            when(openAIService.getCompletionForReason(mockPrompt)).thenReturn(expectedResponse);

            // Act & Assert
            mockMvc.perform(post("/api/chat/generateDescriptionEffect")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.message").value(expectedResponse));

            verify(promptingService).buildDescriptionEffectPrompt(any());
            verify(openAIService).getCompletionForReason(mockPrompt);
        }

        @Test
        @WithMockUser(username = "testuser") 
        @DisplayName("Should handle different generation types")
        void whenGenerateDescriptionEffect_withEffectType_thenReturnsResponse() throws Exception {
            // Arrange
            DescriptionEffectRequest request = new DescriptionEffectRequest();
            request.setGenerationType("effect");
            
            String mockPrompt = "Generate effect for...";
            String expectedResponse = "The effect is...";

            when(promptingService.buildDescriptionEffectPrompt(any())).thenReturn(mockPrompt);
            when(openAIService.getCompletionForReason(mockPrompt)).thenReturn(expectedResponse);

            // Act & Assert
            mockMvc.perform(post("/api/chat/generateDescriptionEffect")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.message").value(expectedResponse));
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle OpenAI service exceptions with generation type in message")
        void whenGenerateDescriptionEffect_withOpenAIException_thenReturnsServerErrorWithType() throws Exception {
            // Arrange
            DescriptionEffectRequest request = new DescriptionEffectRequest();
            request.setGenerationType("description");
            String mockPrompt = "Generate description for...";

            when(promptingService.buildDescriptionEffectPrompt(any())).thenReturn(mockPrompt);
            when(openAIService.getCompletionForReason(mockPrompt))
                    .thenThrow(new RuntimeException("OpenAI API error"));

            // Act & Assert
            mockMvc.perform(post("/api/chat/generateDescriptionEffect")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());
        }

        @Test
        @DisplayName("Should return unauthorized when not authenticated")
        void whenGenerateDescriptionEffect_withoutAuthentication_thenReturnsUnauthorized() throws Exception {
            // Arrange
            DescriptionEffectRequest request = new DescriptionEffectRequest();

            // Act & Assert
            mockMvc.perform(post("/api/chat/generateDescriptionEffect")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized());

            verify(promptingService, never()).buildDescriptionEffectPrompt(any());
        }

        @Test
        @WithMockUser(username = "testuser")
        @DisplayName("Should handle invalid request body")
        void whenGenerateDescriptionEffect_withInvalidRequest_thenReturnsBadRequest() throws Exception {
            // Act & Assert
            mockMvc.perform(post("/api/chat/generateDescriptionEffect")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content("{ invalid json }"))
                    .andDo(print())
                    .andExpect(status().isBadRequest());

            verify(promptingService, never()).buildDescriptionEffectPrompt(any());
        }
    }

    // TODO: Add integration tests with actual OpenAI service
    // TODO: Add tests for CORS functionality 
    // TODO: Add tests for message validation and sanitization
    // TODO: Add tests for conversation limits and cleanup
    // TODO: Add tests for concurrent user conversations
    // TODO: Add performance tests for high-volume chat requests
    // TODO: Add tests for rate limiting on AI endpoints
}