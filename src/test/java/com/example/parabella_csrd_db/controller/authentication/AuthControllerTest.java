package com.example.parabella_csrd_db.controller.authentication;

import com.example.parabella_csrd_db.config.TestConfig;
import com.example.parabella_csrd_db.config.TestSecurityConfig;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.request.LoginRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SignUpRequest;
import com.example.parabella_csrd_db.security.jwt.JwtUtils;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;
import com.example.parabella_csrd_db.security.utils.ForgotPasswordService;
import com.example.parabella_csrd_db.security.utils.TotpUtils;
import com.example.parabella_csrd_db.utils.TestDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

@WebMvcTest(AuthController.class)
@ActiveProfiles("test")
@Import({TestConfig.class, TestSecurityConfig.class})
@DisplayName("Authentication Controller Tests")
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AuthenticationManager authenticationManager;

    @MockBean
    private UserRepository userRepository;

    @MockBean
    private VerificationTokenRepository tokenRepository;

    @MockBean
    private RoleRepository roleRepository;

    @MockBean
    private PasswordEncoder passwordEncoder;

    @MockBean
    private JwtUtils jwtUtils;

    @MockBean
    private ForgotPasswordService forgotPasswordService;

    private MockedStatic<TotpUtils> totpUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        totpUtilsMockedStatic = mockStatic(TotpUtils.class);
    }

    @AfterEach
    void tearDown() {
        totpUtilsMockedStatic.close();
    }

    private Authentication createMockAuthentication() {
        UserDetailsImpl userDetails = TestDataBuilder.aUserDetails();
        return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    }

    private Authentication createMockAuthentication(String username, String email, String role) {
        UserDetailsImpl userDetails = TestDataBuilder.aUserDetails(1L, username, email, role);
        return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    }

    @Nested
    @DisplayName("Sign In Tests")
    class SignInTests {

        @Test
        @DisplayName("Should return JWT response when credentials and TOTP are valid")
        void whenSignIn_withValidCredentialsAndTotp_thenReturnsJwtResponse() throws Exception {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest();
            User mockUser = TestDataBuilder.aUser()
                    .withTotpSecret("valid-secret")
                    .build();
            Authentication mockAuthentication = createMockAuthentication();
            
            when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                    .thenReturn(mockAuthentication);
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
            totpUtilsMockedStatic.when(() -> TotpUtils.validateCode("valid-secret", 123456)).thenReturn(true);
            when(jwtUtils.generateJwtToken(mockAuthentication)).thenReturn("dummy.jwt.token");

            // Act & Assert
            mockMvc.perform(post("/api/auth/signin")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(loginRequest)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.accessToken").value("dummy.jwt.token"))
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.permissions[0]").value("VIEW_DASHBOARD"));
        }

        @Test
        @DisplayName("Should return unauthorized when credentials are invalid")
        void whenSignIn_withInvalidCredentials_thenReturnsUnauthorized() throws Exception {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest("testuser", "wrongpassword", 0);
            when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                    .thenThrow(new BadCredentialsException("Bad credentials"));

            // Act & Assert
            mockMvc.perform(post("/api/auth/signin")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(loginRequest)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").exists());
        }

        @Test
        @DisplayName("Should require 2FA setup for first-time user")
        void whenSignIn_forFirstTime_thenRequires2faSetup() throws Exception {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest("newuser", "password", 0);
            User mockUser = TestDataBuilder.aUser()
                    .withUsername("newuser")
                    .withEmail("<EMAIL>")
                    .withoutTotpSecret()
                    .build();
            Authentication mockAuthentication = createMockAuthentication("newuser", "<EMAIL>", "ROLE_USER");
            
            when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
            when(userRepository.findByUsername("newuser")).thenReturn(Optional.of(mockUser));
            totpUtilsMockedStatic.when(TotpUtils::generateSecret).thenReturn("new-secret");
            totpUtilsMockedStatic.when(() -> TotpUtils.getQrCodeUrl(anyString(), anyString(), anyString()))
                    .thenReturn("http://qr.code/url");

            // Act & Assert
            mockMvc.perform(post("/api/auth/signin")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(loginRequest)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").value("2FA setup required"))
                    .andExpect(jsonPath("$.qrCodeUrl").value("http://qr.code/url"));
        }

        @ParameterizedTest
        @ValueSource(ints = {999999, 000000, 111111})
        @DisplayName("Should return unauthorized for invalid TOTP codes")
        void whenSignIn_withInvalidTotpCode_thenReturnsUnauthorized(int invalidCode) throws Exception {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest("testuser", "password", invalidCode);
            User mockUser = TestDataBuilder.aUser().withTotpSecret("valid-secret").build();
            
            when(authenticationManager.authenticate(any())).thenReturn(createMockAuthentication());
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
            totpUtilsMockedStatic.when(() -> TotpUtils.validateCode("valid-secret", invalidCode)).thenReturn(false);

            // Act & Assert
            mockMvc.perform(post("/api/auth/signin")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(loginRequest)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").value("Error: Invalid or missing TOTP code"));
        }
    }

    @Nested
    @DisplayName("User Registration Tests")
    class UserRegistrationTests {

        @Test
        @DisplayName("Should successfully register new user with valid data")
        void whenRegisterUser_withValidData_thenSucceeds() throws Exception {
            // Arrange
            SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest();
            
            when(userRepository.existsByUsername("newuser")).thenReturn(false);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
            when(passwordEncoder.encode("password123")).thenReturn("encodedPassword123");
            when(roleRepository.findByName("User")).thenReturn(Optional.of(TestDataBuilder.aRole().build()));
            totpUtilsMockedStatic.when(TotpUtils::generateSecret).thenReturn("new-user-secret");
            totpUtilsMockedStatic.when(() -> TotpUtils.getQrCodeUrl(any(), any(), any()))
                    .thenReturn("http://new.qr/url");

            // Act & Assert
            mockMvc.perform(post("/api/auth/signup")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(signUpRequest)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.message").value("User registered successfully! Set up your authenticator with this QR:"))
                    .andExpect(jsonPath("$.qrCodeUrl").value("http://new.qr/url"));
            
            verify(userRepository).save(any(User.class));
        }

        @Test
        @DisplayName("Should return bad request when username already exists")
        void whenRegisterUser_withExistingUsername_thenReturnsBadRequest() throws Exception {
            // Arrange
            SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest("existinguser", "<EMAIL>", "password123");
            when(userRepository.existsByUsername("existinguser")).thenReturn(true);

            // Act & Assert
            mockMvc.perform(post("/api/auth/signup")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(signUpRequest)))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Error: Username is already taken!"));
            
            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should return bad request when email already exists")
        void whenRegisterUser_withExistingEmail_thenReturnsBadRequest() throws Exception {
            // Arrange
            SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest("newuser", "<EMAIL>", "password123");
            when(userRepository.existsByUsername("newuser")).thenReturn(false);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

            // Act & Assert
            mockMvc.perform(post("/api/auth/signup")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(signUpRequest)))
                    .andDo(print())
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Error: Email is already in use!"));
            
            verify(userRepository, never()).save(any(User.class));
        }
    }

    @Nested
    @DisplayName("Password Reset Tests")
    class PasswordResetTests {

        @Test
        @DisplayName("Should accept forgot password request")
        void whenForgotPassword_thenDelegatesToService() throws Exception {
            // Arrange
            Map<String, String> request = Map.of("email", "<EMAIL>");

            // Act & Assert
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.message")
                            .value("If that email is associated with an account, a reset link was sent."));
            
            verify(forgotPasswordService).initiatePasswordReset("<EMAIL>");
        }

        @Test
        @DisplayName("Should successfully reset password with valid token")
        void whenResetPassword_withValidToken_thenSucceeds() throws Exception {
            // Arrange
            when(forgotPasswordService.resetPassword("valid-token", "newStrongPassword")).thenReturn(true);

            // Act & Assert
            mockMvc.perform(post("/api/auth/reset-password")
                            .param("token", "valid-token")
                            .param("newPassword", "newStrongPassword"))
                    .andDo(print())
                    .andExpect(status().isOk());
            
            verify(forgotPasswordService).resetPassword("valid-token", "newStrongPassword");
        }

        @Test
        @DisplayName("Should return bad request for invalid token")
        void whenResetPassword_withInvalidToken_thenReturnsBadRequest() throws Exception {
            // Arrange
            when(forgotPasswordService.resetPassword("invalid-token", "newStrongPassword")).thenReturn(false);

            // Act & Assert
            mockMvc.perform(post("/api/auth/reset-password")
                            .param("token", "invalid-token")
                            .param("newPassword", "newStrongPassword"))
                    .andDo(print())
                    .andExpect(status().isBadRequest());
        }
    }

    @Nested
    @DisplayName("2FA Verification Tests")
    class TwoFactorAuthTests {

        @Test
        @DisplayName("Should successfully verify 2FA with valid code")
        void whenVerify2FA_withValidCode_thenSucceeds() throws Exception {
            // Arrange
            Map<String, String> payload = Map.of("username", "testuser", "code", "123456");
            User mockUser = TestDataBuilder.aUser().withTotpSecret("valid-secret").build();
            
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
            totpUtilsMockedStatic.when(() -> TotpUtils.validateCode("valid-secret", 123456)).thenReturn(true);

            // Act & Assert
            mockMvc.perform(post("/api/auth/verify2fa")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(payload)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.message").value("2FA verified. You can now log in with TOTP code."));
        }

        @Test
        @DisplayName("Should return unauthorized for invalid 2FA code")
        void whenVerify2FA_withInvalidCode_thenReturnsUnauthorized() throws Exception {
            // Arrange
            Map<String, String> payload = Map.of("username", "testuser", "code", "999999");
            User mockUser = TestDataBuilder.aUser().withTotpSecret("valid-secret").build();
            
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
            totpUtilsMockedStatic.when(() -> TotpUtils.validateCode("valid-secret", 999999)).thenReturn(false);

            // Act & Assert
            mockMvc.perform(post("/api/auth/verify2fa")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(payload)))
                    .andDo(print())
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").value("Invalid TOTP code"));
        }

        @Test
        @DisplayName("Should return not found for non-existent user")
        void whenVerify2FA_withNonExistentUser_thenReturnsNotFound() throws Exception {
            // Arrange
            Map<String, String> payload = Map.of("username", "nonexistent", "code", "123456");
            when(userRepository.findByUsername("nonexistent")).thenReturn(Optional.empty());

            // Act & Assert
            mockMvc.perform(post("/api/auth/verify2fa")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(payload)))
                    .andDo(print())
                    .andExpect(status().isNotFound());
        }
    }
}