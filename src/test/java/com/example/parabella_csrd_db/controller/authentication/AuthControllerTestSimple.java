package com.example.parabella_csrd_db.controller.authentication;

import com.example.parabella_csrd_db.config.TestConfig;
import com.example.parabella_csrd_db.config.TestSecurityConfig;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.request.LoginRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SignUpRequest;
import com.example.parabella_csrd_db.security.jwt.JwtUtils;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;
import com.example.parabella_csrd_db.security.utils.ForgotPasswordService;
import com.example.parabella_csrd_db.security.utils.TotpUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AuthController.class)
@ActiveProfiles("test")
@Import({TestConfig.class, TestSecurityConfig.class})
@DisplayName("Authentication Controller Simple Tests")
class AuthControllerTestSimple {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AuthenticationManager authenticationManager;

    @MockBean
    private UserRepository userRepository;

    @MockBean
    private VerificationTokenRepository tokenRepository;

    @MockBean
    private RoleRepository roleRepository;

    @MockBean
    private PasswordEncoder passwordEncoder;

    @MockBean
    private JwtUtils jwtUtils;

    @MockBean
    private ForgotPasswordService forgotPasswordService;

    private MockedStatic<TotpUtils> totpUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        totpUtilsMockedStatic = mockStatic(TotpUtils.class);
    }

    @AfterEach
    void tearDown() {
        totpUtilsMockedStatic.close();
    }

    @Test
    @DisplayName("Should return unauthorized when credentials are invalid")
    void testSignInWithInvalidCredentials() throws Exception {
        // Arrange
        LoginRequest loginRequest = new LoginRequest("testuser", "wrongpassword", 0);
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenThrow(new BadCredentialsException("Bad credentials"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/signin")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("Should return bad request when username already exists during signup")
    void testSignUpWithExistingUsername() throws Exception {
        // Arrange
        SignUpRequest signUpRequest = new SignUpRequest("existinguser", "<EMAIL>", "password123");
        when(userRepository.existsByUsername("existinguser")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/auth/signup")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signUpRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Error: Username is already taken!"));
    }

    @Test
    @DisplayName("Should return bad request when email already exists during signup")
    void testSignUpWithExistingEmail() throws Exception {
        // Arrange
        SignUpRequest signUpRequest = new SignUpRequest("newuser", "<EMAIL>", "password123");
        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/auth/signup")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signUpRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Error: Email is already in use!"));
    }

    @Test
    @DisplayName("Should accept forgot password request")
    void testForgotPasswordRequest() throws Exception {
        // Arrange
        Map<String, String> request = Map.of("email", "<EMAIL>");

        // Act & Assert
        mockMvc.perform(post("/api/auth/forgot-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message")
                        .value("If that email is associated with an account, a reset link was sent."));
        
        verify(forgotPasswordService).initiatePasswordReset("<EMAIL>");
    }

    @Test
    @DisplayName("Should successfully reset password with valid token")
    void testResetPasswordWithValidToken() throws Exception {
        // Arrange
        when(forgotPasswordService.resetPassword("valid-token", "newStrongPassword")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/auth/reset-password")
                        .param("token", "valid-token")
                        .param("newPassword", "newStrongPassword"))
                .andExpect(status().isOk());
        
        verify(forgotPasswordService).resetPassword("valid-token", "newStrongPassword");
    }

    @Test
    @DisplayName("Should return bad request for invalid reset token")
    void testResetPasswordWithInvalidToken() throws Exception {
        // Arrange
        when(forgotPasswordService.resetPassword("invalid-token", "newStrongPassword")).thenReturn(false);

        // Act & Assert
        mockMvc.perform(post("/api/auth/reset-password")
                        .param("token", "invalid-token")
                        .param("newPassword", "newStrongPassword"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("Should return not found for non-existent user in 2FA verification")
    void testVerify2FAWithNonExistentUser() throws Exception {
        // Arrange
        Map<String, String> payload = Map.of("username", "nonexistent", "code", "123456");
        when(userRepository.findByUsername("nonexistent")).thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(post("/api/auth/verify2fa")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isNotFound());
    }
}