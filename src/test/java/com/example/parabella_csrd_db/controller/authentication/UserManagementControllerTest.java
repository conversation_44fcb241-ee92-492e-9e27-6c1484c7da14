package com.example.parabella_csrd_db.controller.authentication;

import com.example.parabella_csrd_db.config.TestConfig;
import com.example.parabella_csrd_db.config.TestSecurityConfig;
import com.example.parabella_csrd_db.dto.authentication.user.UserDto;
import com.example.parabella_csrd_db.dto.authentication.user.UserInviteRequest;
import com.example.parabella_csrd_db.dto.authentication.user.UserUpdateRoleRequest;
import com.example.parabella_csrd_db.service.authentication.UserManagementService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(UserManagementController.class)
@ActiveProfiles("test")
@Import({TestConfig.class, TestSecurityConfig.class})
@DisplayName("User Management Controller Tests")
class UserManagementControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private UserManagementService userManagementService;

    private UserDto testUserDto;
    private UserInviteRequest testInviteRequest;
    private UserUpdateRoleRequest testUpdateRequest;

    @BeforeEach
    void setUp() {
        testUserDto = new UserDto(1L, "testuser", "<EMAIL>", "ROLE_USER", 1);
        testInviteRequest = new UserInviteRequest("<EMAIL>", 1);
        testUpdateRequest = new UserUpdateRoleRequest(2);
    }

    @Nested
    @DisplayName("Get Users Tests")
    class GetUsersTests {

        @Test
        @WithMockUser(authorities = "user.view")
        @DisplayName("Should return all users when authorized")
        void whenGetUsers_withValidPermission_thenReturnsUsers() throws Exception {
            // Arrange
            List<UserDto> users = Arrays.asList(testUserDto);
            when(userManagementService.findAllUsers()).thenReturn(users);

            // Act & Assert
            mockMvc.perform(get("/api/users"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.length()").value(1))
                    .andExpect(jsonPath("$[0].id").value(1))
                    .andExpect(jsonPath("$[0].username").value("testuser"))
                    .andExpect(jsonPath("$[0].email").value("<EMAIL>"));

            verify(userManagementService).findAllUsers();
        }

        @Test
        @WithMockUser(authorities = "wrong.permission")
        @DisplayName("Should return forbidden when not authorized")
        void whenGetUsers_withoutPermission_thenReturnsForbidden() throws Exception {
            // Act & Assert
            mockMvc.perform(get("/api/users"))
                    .andDo(print())
                    .andExpect(status().isForbidden());

            verify(userManagementService, never()).findAllUsers();
        }

        @Test
        @DisplayName("Should return unauthorized when not authenticated")
        void whenGetUsers_withoutAuthentication_thenReturnsUnauthorized() throws Exception {
            // Act & Assert
            mockMvc.perform(get("/api/users"))
                    .andDo(print())
                    .andExpect(status().isUnauthorized());

            verify(userManagementService, never()).findAllUsers();
        }

        @Test
        @WithMockUser(authorities = "user.view")
        @DisplayName("Should return empty list when no users exist")
        void whenGetUsers_withNoUsers_thenReturnsEmptyList() throws Exception {
            // Arrange
            when(userManagementService.findAllUsers()).thenReturn(Collections.emptyList());

            // Act & Assert
            mockMvc.perform(get("/api/users"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(0));
        }
    }

    @Nested
    @DisplayName("Get Assignable Users Tests")
    class GetAssignableUsersTests {

        @Test
        @WithMockUser
        @DisplayName("Should return assignable users")
        void whenGetAssignableUsers_thenReturnsUsers() throws Exception {
            // Arrange
            List<UserDto> assignableUsers = Arrays.asList(testUserDto);
            when(userManagementService.findAssignableUsers()).thenReturn(assignableUsers);

            // Act & Assert
            mockMvc.perform(get("/api/users/assignable"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.length()").value(1))
                    .andExpect(jsonPath("$[0].id").value(1))
                    .andExpect(jsonPath("$[0].username").value("testuser"));

            verify(userManagementService).findAssignableUsers();
        }

        @Test
        @DisplayName("Should return unauthorized when not authenticated")
        void whenGetAssignableUsers_withoutAuthentication_thenReturnsUnauthorized() throws Exception {
            // Act & Assert
            mockMvc.perform(get("/api/users/assignable"))
                    .andDo(print())
                    .andExpect(status().isUnauthorized());

            verify(userManagementService, never()).findAssignableUsers();
        }

        @Test
        @WithMockUser
        @DisplayName("Should return empty list when no assignable users")
        void whenGetAssignableUsers_withNoUsers_thenReturnsEmptyList() throws Exception {
            // Arrange
            when(userManagementService.findAssignableUsers()).thenReturn(Collections.emptyList());

            // Act & Assert
            mockMvc.perform(get("/api/users/assignable"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(0));
        }
    }

    @Nested
    @DisplayName("Invite User Tests")
    class InviteUserTests {

        @Test
        @WithMockUser(authorities = "user.invite")
        @DisplayName("Should successfully invite new user")
        void whenInviteUser_withValidData_thenReturnsCreated() throws Exception {
            // Arrange
            when(userManagementService.inviteUser(any(UserInviteRequest.class))).thenReturn(testUserDto);

            // Act & Assert
            mockMvc.perform(post("/api/users/invite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testInviteRequest)))
                    .andDo(print())
                    .andExpect(status().isCreated())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.id").value(1))
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"));

            verify(userManagementService).inviteUser(any(UserInviteRequest.class));
        }

        @Test
        @WithMockUser(authorities = "wrong.permission")
        @DisplayName("Should return forbidden when not authorized")
        void whenInviteUser_withoutPermission_thenReturnsForbidden() throws Exception {
            // Act & Assert
            mockMvc.perform(post("/api/users/invite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testInviteRequest)))
                    .andDo(print())
                    .andExpect(status().isForbidden());

            verify(userManagementService, never()).inviteUser(any());
        }

        @Test
        @WithMockUser(authorities = "user.invite")
        @DisplayName("Should return bad request when email already exists")
        void whenInviteUser_withExistingEmail_thenReturnsBadRequest() throws Exception {
            // Arrange
            when(userManagementService.inviteUser(any(UserInviteRequest.class)))
                    .thenThrow(new IllegalArgumentException("Error: Email is already in use!"));

            // Act & Assert
            mockMvc.perform(post("/api/users/invite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testInviteRequest)))
                    .andDo(print())
                    .andExpect(status().isBadRequest());
        }

        @Test
        @WithMockUser(authorities = "user.invite")
        @DisplayName("Should return not found when role not found")
        void whenInviteUser_withNonExistentRole_thenReturnsNotFound() throws Exception {
            // Arrange
            when(userManagementService.inviteUser(any(UserInviteRequest.class)))
                    .thenThrow(new EntityNotFoundException("Role not found with id: 999"));

            // Act & Assert
            mockMvc.perform(post("/api/users/invite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testInviteRequest)))
                    .andDo(print())
                    .andExpect(status().isNotFound());
        }

        @Test
        @WithMockUser(authorities = "user.invite")
        @DisplayName("Should return bad request for invalid request body")
        void whenInviteUser_withInvalidData_thenReturnsBadRequest() throws Exception {
            // Arrange
            UserInviteRequest invalidRequest = new UserInviteRequest("", null); // Invalid email and role

            // Act & Assert
            mockMvc.perform(post("/api/users/invite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andDo(print())
                    .andExpect(status().isBadRequest());

            verify(userManagementService, never()).inviteUser(any());
        }

        @Test
        @WithMockUser(authorities = "user.invite")
        @DisplayName("Should return server error when email service fails")
        void whenInviteUser_andEmailServiceFails_thenReturnsServerError() throws Exception {
            // Arrange
            when(userManagementService.inviteUser(any(UserInviteRequest.class)))
                    .thenThrow(new RuntimeException("Unable to send invitation email. Please try again later."));

            // Act & Assert
            mockMvc.perform(post("/api/users/invite")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testInviteRequest)))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());
        }
    }

    @Nested
    @DisplayName("Update User Role Tests")
    class UpdateUserRoleTests {

        @Test
        @WithMockUser(authorities = "user.edit")
        @DisplayName("Should successfully update user role")
        void whenUpdateUserRole_withValidData_thenReturnsUpdatedUser() throws Exception {
            // Arrange
            UserDto updatedUser = new UserDto(1L, "testuser", "<EMAIL>", "ROLE_ADMIN", 2);
            when(userManagementService.updateUserRole(eq(1L), any(UserUpdateRoleRequest.class)))
                    .thenReturn(updatedUser);

            // Act & Assert
            mockMvc.perform(put("/api/users/1/role")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testUpdateRequest)))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.id").value(1))
                    .andExpect(jsonPath("$.roleName").value("ROLE_ADMIN"))
                    .andExpect(jsonPath("$.roleId").value(2));

            verify(userManagementService).updateUserRole(1L, testUpdateRequest);
        }

        @Test
        @WithMockUser(authorities = "wrong.permission")
        @DisplayName("Should return forbidden when not authorized")
        void whenUpdateUserRole_withoutPermission_thenReturnsForbidden() throws Exception {
            // Act & Assert
            mockMvc.perform(put("/api/users/1/role")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testUpdateRequest)))
                    .andDo(print())
                    .andExpect(status().isForbidden());

            verify(userManagementService, never()).updateUserRole(any(), any());
        }

        @Test
        @WithMockUser(authorities = "user.edit")
        @DisplayName("Should return not found when user not found")
        void whenUpdateUserRole_withNonExistentUser_thenReturnsNotFound() throws Exception {
            // Arrange
            when(userManagementService.updateUserRole(eq(999L), any(UserUpdateRoleRequest.class)))
                    .thenThrow(new EntityNotFoundException("User not found with id: 999"));

            // Act & Assert
            mockMvc.perform(put("/api/users/999/role")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(testUpdateRequest)))
                    .andDo(print())
                    .andExpect(status().isNotFound());
        }

        @Test
        @WithMockUser(authorities = "user.edit")
        @DisplayName("Should return not found when role not found")
        void whenUpdateUserRole_withNonExistentRole_thenReturnsNotFound() throws Exception {
            // Arrange
            UserUpdateRoleRequest invalidRoleRequest = new UserUpdateRoleRequest(999);
            when(userManagementService.updateUserRole(eq(1L), any(UserUpdateRoleRequest.class)))
                    .thenThrow(new EntityNotFoundException("Role not found with id: 999"));

            // Act & Assert
            mockMvc.perform(put("/api/users/1/role")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRoleRequest)))
                    .andDo(print())
                    .andExpect(status().isNotFound());
        }

        @Test
        @WithMockUser(authorities = "user.edit")
        @DisplayName("Should return bad request for invalid request body")
        void whenUpdateUserRole_withInvalidData_thenReturnsBadRequest() throws Exception {
            // Arrange
            UserUpdateRoleRequest invalidRequest = new UserUpdateRoleRequest(null);

            // Act & Assert
            mockMvc.perform(put("/api/users/1/role")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andDo(print())
                    .andExpect(status().isBadRequest());

            verify(userManagementService, never()).updateUserRole(any(), any());
        }
    }

    @Nested
    @DisplayName("Delete User Tests")
    class DeleteUserTests {

        @Test
        @WithMockUser(authorities = "user.delete")
        @DisplayName("Should successfully delete user")
        void whenDeleteUser_withValidId_thenReturnsNoContent() throws Exception {
            // Arrange
            doNothing().when(userManagementService).deleteUser(1L);

            // Act & Assert
            mockMvc.perform(delete("/api/users/1"))
                    .andDo(print())
                    .andExpect(status().isNoContent());

            verify(userManagementService).deleteUser(1L);
        }

        @Test
        @WithMockUser(authorities = "wrong.permission")
        @DisplayName("Should return forbidden when not authorized")
        void whenDeleteUser_withoutPermission_thenReturnsForbidden() throws Exception {
            // Act & Assert
            mockMvc.perform(delete("/api/users/1"))
                    .andDo(print())
                    .andExpect(status().isForbidden());

            verify(userManagementService, never()).deleteUser(any());
        }

        @Test
        @WithMockUser(authorities = "user.delete")
        @DisplayName("Should return not found when user not found")
        void whenDeleteUser_withNonExistentUser_thenReturnsNotFound() throws Exception {
            // Arrange
            doThrow(new EntityNotFoundException("User not found with id: 999"))
                    .when(userManagementService).deleteUser(999L);

            // Act & Assert
            mockMvc.perform(delete("/api/users/999"))
                    .andDo(print())
                    .andExpect(status().isNotFound());
        }

        @Test
        @WithMockUser(authorities = "user.delete")
        @DisplayName("Should return bad request when user tries to delete themselves")
        void whenDeleteUser_andUserDeletesThemselves_thenReturnsBadRequest() throws Exception {
            // Arrange 
            doThrow(new IllegalArgumentException("You cannot delete your own account"))
                    .when(userManagementService).deleteUser(1L);

            // Act & Assert
            mockMvc.perform(delete("/api/users/1"))
                    .andDo(print())
                    .andExpect(status().isBadRequest());
        }

        @Test
        @WithMockUser(authorities = "user.delete")
        @DisplayName("Should return conflict when user has constraints")
        void whenDeleteUser_withConstraintViolation_thenReturnsConflict() throws Exception {
            // Arrange
            doThrow(new IllegalStateException("Cannot delete user because they are assigned as a stakeholder in one or more projects"))
                    .when(userManagementService).deleteUser(1L);

            // Act & Assert
            mockMvc.perform(delete("/api/users/1"))
                    .andDo(print())
                    .andExpect(status().isConflict());
        }

        @Test
        @WithMockUser(authorities = "user.delete") 
        @DisplayName("Should return conflict for generic foreign key violations")
        void whenDeleteUser_withGenericConstraintViolation_thenReturnsConflict() throws Exception {
            // Arrange
            doThrow(new IllegalStateException("Cannot delete user because they have associated data in the system"))
                    .when(userManagementService).deleteUser(1L);

            // Act & Assert
            mockMvc.perform(delete("/api/users/1"))
                    .andDo(print())
                    .andExpect(status().isConflict());
        }

        @Test
        @WithMockUser(authorities = "user.delete")
        @DisplayName("Should return bad request for invalid user ID format")
        void whenDeleteUser_withInvalidIdFormat_thenReturnsBadRequest() throws Exception {
            // Act & Assert
            mockMvc.perform(delete("/api/users/invalid"))
                    .andDo(print())
                    .andExpect(status().isBadRequest());

            verify(userManagementService, never()).deleteUser(any());
        }
    }

    // TODO: Add integration tests with database 
    // TODO: Add tests for role-based access control edge cases
    // TODO: Add tests for concurrent user operations
    // TODO: Add tests for audit trail generation on user management operations
    // TODO: Add performance tests for large user datasets
}