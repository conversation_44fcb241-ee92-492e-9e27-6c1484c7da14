package com.example.parabella_csrd_db.utils;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Permission;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.dto.authentication.request.LoginRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SignUpRequest;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Set;

public class TestDataBuilder {

    // User Builder
    public static class UserBuilder {
        private String username = "testuser";
        private String email = "<EMAIL>";
        private String password = "encodedPassword";
        private String totpSecret = "validTotpSecret";
        private Role role = createDefaultRole();
        private Long id = 1L;

        public UserBuilder withUsername(String username) {
            this.username = username;
            return this;
        }

        public UserBuilder withEmail(String email) {
            this.email = email;
            return this;
        }

        public UserBuilder withPassword(String password) {
            this.password = password;
            return this;
        }

        public UserBuilder withTotpSecret(String totpSecret) {
            this.totpSecret = totpSecret;
            return this;
        }

        public UserBuilder withRole(Role role) {
            this.role = role;
            return this;
        }

        public UserBuilder withId(Long id) {
            this.id = id;
            return this;
        }

        public UserBuilder withoutTotpSecret() {
            this.totpSecret = null;
            return this;
        }

        public User build() {
            User user = new User(username, email, password);
            user.setId(id);
            user.setTotpSecret(totpSecret);
            if (role != null) {
                user.setRole(role);
            }
            return user;
        }
    }

    // Role Builder
    public static class RoleBuilder {
        private String name = "ROLE_USER";
        private Set<Permission> permissions = Set.of(createDefaultPermission());

        public RoleBuilder withName(String name) {
            this.name = name;
            return this;
        }

        public RoleBuilder withPermissions(Set<Permission> permissions) {
            this.permissions = permissions;
            return this;
        }

        public Role build() {
            Role role = new Role();
            role.setName(name);
            role.setPermissions(permissions);
            return role;
        }
    }

    // Company Builder
    public static class CompanyBuilder {
        private Long id = 1L;
        private String name = "Test Company";
        private String address = "Test Address";
        private String email = "<EMAIL>";

        public CompanyBuilder withId(Long id) {
            this.id = id;
            return this;
        }

        public CompanyBuilder withName(String name) {
            this.name = name;
            return this;
        }

        public CompanyBuilder withAddress(String address) {
            this.address = address;
            return this;
        }

        public CompanyBuilder withEmail(String email) {
            this.email = email;
            return this;
        }

        public Company build() {
            Company company = new Company();
            company.setId(id);
            company.setCompanyName(name);
            company.setAddress(address);
            // Note: Company entity doesn't have email field, skipping
            return company;
        }
    }

    // Project Builder
    public static class ProjectBuilder {
        private Long id = 1L;
        private String name = "Test Project";
        private String description = "Test Description";
        private LocalDateTime createdAt = LocalDateTime.now();
        private Company company;
        private CompanyGroup companyGroup;

        public ProjectBuilder withId(Long id) {
            this.id = id;
            return this;
        }

        public ProjectBuilder withName(String name) {
            this.name = name;
            return this;
        }

        public ProjectBuilder withDescription(String description) {
            this.description = description;
            return this;
        }

        public ProjectBuilder withCompany(Company company) {
            this.company = company;
            return this;
        }

        public ProjectBuilder withCompanyGroup(CompanyGroup companyGroup) {
            this.companyGroup = companyGroup;
            return this;
        }

        public Project build() {
            Project project = new Project();
            project.setId(id);
            project.setProjectName(name);
            project.setProjectDescription(description);
            // Note: createdAt is managed by BaseAuditedEntity
            // Note: Project entity relationship setters may differ, skipping for now
            return project;
        }
    }

    // Static factory methods
    public static UserBuilder aUser() {
        return new UserBuilder();
    }

    public static RoleBuilder aRole() {
        return new RoleBuilder();
    }

    public static CompanyBuilder aCompany() {
        return new CompanyBuilder();
    }

    public static ProjectBuilder aProject() {
        return new ProjectBuilder();
    }

    // Request builders
    public static LoginRequest aLoginRequest() {
        return new LoginRequest("testuser", "password", 123456);
    }

    public static LoginRequest aLoginRequest(String username, String password, int totpCode) {
        return new LoginRequest(username, password, totpCode);
    }

    public static SignUpRequest aSignUpRequest() {
        return new SignUpRequest("newuser", "<EMAIL>", "password123");
    }

    public static SignUpRequest aSignUpRequest(String username, String email, String password) {
        return new SignUpRequest(username, email, password);
    }

    // UserDetails builder
    public static UserDetailsImpl aUserDetails() {
        return new UserDetailsImpl(1L, "testuser", "<EMAIL>", "password", 
                                 Collections.emptyList(), "ROLE_USER");
    }

    public static UserDetailsImpl aUserDetails(Long id, String username, String email, String role) {
        return new UserDetailsImpl(id, username, email, "password", 
                                 Collections.emptyList(), role);
    }

    // Helper methods
    private static Role createDefaultRole() {
        Role role = new Role();
        role.setName("ROLE_USER");
        role.setPermissions(Set.of(createDefaultPermission()));
        return role;
    }

    private static Permission createDefaultPermission() {
        Permission permission = new Permission();
        permission.setFunctionName("VIEW_DASHBOARD");
        return permission;
    }

    public static Role createAdminRole() {
        Role role = new Role();
        role.setName("ROLE_ADMIN");
        Permission viewDashboard = new Permission();
        viewDashboard.setFunctionName("VIEW_DASHBOARD");
        Permission manageUsers = new Permission();
        manageUsers.setFunctionName("MANAGE_USERS");
        role.setPermissions(Set.of(viewDashboard, manageUsers));
        return role;
    }
}