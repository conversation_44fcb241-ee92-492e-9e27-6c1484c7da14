package com.example.parabella_csrd_db.service.chat_bot;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("OpenAI Service Tests")
class OpenAIServiceTest {

    @Mock
    private HttpClient httpClient;

    @Mock
    private HttpResponse<String> httpResponse;

    private OpenAIService openAIService;
    private ObjectMapper objectMapper;

    private final String testApiKey = "test-api-key";
    private final String testPrompt = "Tell me about CSRD compliance";

    @BeforeEach
    void setUp() {
        openAIService = new OpenAIService();
        objectMapper = new ObjectMapper();
        
        // Set the API key via reflection
        ReflectionTestUtils.setField(openAIService, "API_KEY", testApiKey);
        // Replace the HttpClient with our mock
        ReflectionTestUtils.setField(openAIService, "httpClient", httpClient);
    }

    private Map<String, Object> createSuccessfulOpenAIResponse(String content) {
        Map<String, Object> message = new HashMap<>();
        message.put("role", "assistant");
        message.put("content", content);

        Map<String, Object> choice = new HashMap<>();
        choice.put("message", message);
        choice.put("finish_reason", "stop");

        Map<String, Object> response = new HashMap<>();
        response.put("choices", Arrays.asList(choice));
        response.put("model", "gpt-4o-mini");
        
        return response;
    }

    @Nested
    @DisplayName("Chat Completion Tests")
    class ChatCompletionTests {

        @Test
        @DisplayName("Should return AI response for valid request")
        void whenGetChatCompletion_withValidMessages_thenReturnsResponse() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            String expectedResponse = "Hello! How can I help you with CSRD compliance today?";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            String result = openAIService.getChatCompletion(messages);

            // Assert
            assertThat(result).isEqualTo(expectedResponse);
            
            // Verify the request was constructed correctly
            ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
            verify(httpClient).send(requestCaptor.capture(), any(HttpResponse.BodyHandler.class));
            
            HttpRequest capturedRequest = requestCaptor.getValue();
            assertThat(capturedRequest.uri().toString()).isEqualTo("https://api.openai.com/v1/chat/completions");
            assertThat(capturedRequest.headers().firstValue("Content-Type")).contains("application/json");
            assertThat(capturedRequest.headers().firstValue("Authorization")).contains("Bearer " + testApiKey);
        }

        @Test
        @DisplayName("Should handle multiple messages in conversation")
        void whenGetChatCompletion_withConversationHistory_thenReturnsResponse() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "What is CSRD?"),
                Map.of("role", "assistant", "content", "CSRD stands for Corporate Sustainability Reporting Directive."),
                Map.of("role", "user", "content", "When does it come into effect?")
            );
            
            String expectedResponse = "The CSRD comes into effect for different companies on various dates, starting from 2024.";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            String result = openAIService.getChatCompletion(messages);

            // Assert
            assertThat(result).isEqualTo(expectedResponse);
        }

        @Test
        @DisplayName("Should trim whitespace from AI response")
        void whenGetChatCompletion_withWhitespaceInResponse_thenTrimsWhitespace() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            String responseWithWhitespace = "  \n  Hello! How can I help?  \n  ";
            String expectedTrimmedResponse = "Hello! How can I help?";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(responseWithWhitespace);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            String result = openAIService.getChatCompletion(messages);

            // Assert
            assertThat(result).isEqualTo(expectedTrimmedResponse);
        }

        @Test
        @DisplayName("Should handle empty messages list")
        void whenGetChatCompletion_withEmptyMessages_thenSendsRequest() throws Exception {
            // Arrange
            List<Map<String, String>> emptyMessages = Arrays.asList();
            
            String expectedResponse = "How can I help you?";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            String result = openAIService.getChatCompletion(emptyMessages);

            // Assert
            assertThat(result).isEqualTo(expectedResponse);
        }

        @Test
        @DisplayName("Should include correct model in request")
        void whenGetChatCompletion_thenUsesCorrectModel() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Test")
            );
            
            String expectedResponse = "Test response";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            openAIService.getChatCompletion(messages);

            // Assert
            ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
            verify(httpClient).send(requestCaptor.capture(), any(HttpResponse.BodyHandler.class));
            
            // Verify that the request body contains the correct model
            // Note: In a real test, you'd want to parse the request body to verify the model
        }
    }

    @Nested
    @DisplayName("Error Handling Tests")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should throw exception for non-200 status codes")
        void whenGetChatCompletion_withErrorStatus_thenThrowsException() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            when(httpResponse.statusCode()).thenReturn(401);
            when(httpResponse.body()).thenReturn("{\"error\": \"Unauthorized\"}");
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act & Assert
            assertThatThrownBy(() -> openAIService.getChatCompletion(messages))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("API request failed with status code 401");
        }

        @Test
        @DisplayName("Should throw exception for API rate limiting")
        void whenGetChatCompletion_withRateLimit_thenThrowsException() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            when(httpResponse.statusCode()).thenReturn(429);
            when(httpResponse.body()).thenReturn("{\"error\": \"Rate limit exceeded\"}");
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act & Assert
            assertThatThrownBy(() -> openAIService.getChatCompletion(messages))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("API request failed with status code 429");
        }

        @Test
        @DisplayName("Should throw exception for server errors")
        void whenGetChatCompletion_withServerError_thenThrowsException() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            when(httpResponse.statusCode()).thenReturn(500);
            when(httpResponse.body()).thenReturn("{\"error\": \"Internal server error\"}");
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act & Assert
            assertThatThrownBy(() -> openAIService.getChatCompletion(messages))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("API request failed with status code 500");
        }

        @Test
        @DisplayName("Should propagate HTTP client exceptions")
        void whenGetChatCompletion_withHttpClientException_thenPropagatesException() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenThrow(new java.io.IOException("Network error"));

            // Act & Assert
            assertThatThrownBy(() -> openAIService.getChatCompletion(messages))
                    .isInstanceOf(java.io.IOException.class)
                    .hasMessage("Network error");
        }

        @Test
        @DisplayName("Should handle malformed JSON response")
        void whenGetChatCompletion_withMalformedJson_thenThrowsException() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn("{ invalid json }");
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act & Assert
            assertThatThrownBy(() -> openAIService.getChatCompletion(messages))
                    .isInstanceOf(Exception.class);
        }

        @Test
        @DisplayName("Should handle missing choices in response")
        void whenGetChatCompletion_withMissingChoices_thenThrowsException() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Hello")
            );
            
            Map<String, Object> malformedResponse = Map.of("model", "gpt-4o-mini");
            String responseJson = objectMapper.writeValueAsString(malformedResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act & Assert
            assertThatThrownBy(() -> openAIService.getChatCompletion(messages))
                    .isInstanceOf(Exception.class);
        }
    }

    @Nested
    @DisplayName("Completion For Reason Tests")
    class CompletionForReasonTests {

        @Test
        @DisplayName("Should return completion for simple prompt")
        void whenGetCompletionForReason_withPrompt_thenReturnsCompletion() throws Exception {
            // Arrange
            String expectedResponse = "The reason for this is due to regulatory requirements.";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            String result = openAIService.getCompletionForReason(testPrompt);

            // Assert
            assertThat(result).isEqualTo(expectedResponse);
            
            // Verify that a single user message was sent
            ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
            verify(httpClient).send(requestCaptor.capture(), any(HttpResponse.BodyHandler.class));
        }

        @Test
        @DisplayName("Should handle empty prompt")
        void whenGetCompletionForReason_withEmptyPrompt_thenSendsRequest() throws Exception {
            // Arrange
            String emptyPrompt = "";
            String expectedResponse = "Please provide more information.";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            String result = openAIService.getCompletionForReason(emptyPrompt);

            // Assert
            assertThat(result).isEqualTo(expectedResponse);
        }

        @Test
        @DisplayName("Should handle null prompt gracefully")
        void whenGetCompletionForReason_withNullPrompt_thenSendsRequest() throws Exception {
            // Arrange
            String expectedResponse = "Please provide a valid prompt.";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            String result = openAIService.getCompletionForReason(null);

            // Assert
            assertThat(result).isEqualTo(expectedResponse);
        }

        @Test
        @DisplayName("Should propagate exceptions from getChatCompletion")
        void whenGetCompletionForReason_withApiError_thenPropagatesException() throws Exception {
            // Arrange
            when(httpResponse.statusCode()).thenReturn(500);
            when(httpResponse.body()).thenReturn("{\"error\": \"Server error\"}");
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act & Assert
            assertThatThrownBy(() -> openAIService.getCompletionForReason(testPrompt))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("API request failed with status code 500");
        }
    }

    @Nested
    @DisplayName("Configuration Tests")
    class ConfigurationTests {

        @Test
        @DisplayName("Should use configured API key")
        void whenServiceInitialized_thenUsesConfiguredApiKey() throws Exception {
            // Arrange
            String customApiKey = "custom-api-key";
            ReflectionTestUtils.setField(openAIService, "API_KEY", customApiKey);
            
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Test")
            );
            
            String expectedResponse = "Test response";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            openAIService.getChatCompletion(messages);

            // Assert
            ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
            verify(httpClient).send(requestCaptor.capture(), any(HttpResponse.BodyHandler.class));
            
            HttpRequest capturedRequest = requestCaptor.getValue();
            assertThat(capturedRequest.headers().firstValue("Authorization"))
                    .contains("Bearer " + customApiKey);
        }

        @Test
        @DisplayName("Should use correct API URL")
        void whenMakingRequest_thenUsesCorrectApiUrl() throws Exception {
            // Arrange
            List<Map<String, String>> messages = Arrays.asList(
                Map.of("role", "user", "content", "Test")
            );
            
            String expectedResponse = "Test response";
            Map<String, Object> openAIResponse = createSuccessfulOpenAIResponse(expectedResponse);
            String responseJson = objectMapper.writeValueAsString(openAIResponse);
            
            when(httpResponse.statusCode()).thenReturn(200);
            when(httpResponse.body()).thenReturn(responseJson);
            when(httpClient.send(any(HttpRequest.class), any(HttpResponse.BodyHandler.class)))
                    .thenReturn(httpResponse);

            // Act
            openAIService.getChatCompletion(messages);

            // Assert
            ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
            verify(httpClient).send(requestCaptor.capture(), any(HttpResponse.BodyHandler.class));
            
            HttpRequest capturedRequest = requestCaptor.getValue();
            assertThat(capturedRequest.uri().toString())
                    .isEqualTo("https://api.openai.com/v1/chat/completions");
        }
    }

    // TODO: Add integration tests with actual OpenAI API
    // TODO: Add tests for request body content validation
    // TODO: Add tests for different OpenAI models
    // TODO: Add tests for token usage tracking
    // TODO: Add tests for retry mechanism on transient failures
    // TODO: Add performance tests for concurrent requests
    // TODO: Add tests for request/response logging
}