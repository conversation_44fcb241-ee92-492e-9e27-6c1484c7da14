package com.example.parabella_csrd_db.service.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.VerificationToken;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.user.UserDto;
import com.example.parabella_csrd_db.dto.authentication.user.UserInviteRequest;
import com.example.parabella_csrd_db.dto.authentication.user.UserUpdateRoleRequest;
import com.example.parabella_csrd_db.service.mail.EmailService;
import com.example.parabella_csrd_db.utils.TestDataBuilder;
import jakarta.mail.MessagingException;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("User Management Service Tests")
class UserManagementServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private ManagementMapper mapper;

    @Mock
    private EmailService emailService;

    @Mock
    private VerificationTokenRepository tokenRepository;

    @InjectMocks
    private UserManagementService userManagementService;

    private User testUser;
    private Role testRole;
    private UserDto testUserDto;

    @BeforeEach
    void setUp() {
        testRole = TestDataBuilder.aRole().build();
        testUser = TestDataBuilder.aUser().withRole(testRole).build();
        testUserDto = new UserDto(1L, "testuser", "<EMAIL>", "ROLE_USER", 1);
        
        // Set the assignable roles property for testing
        ReflectionTestUtils.setField(userManagementService, "assignableRoleNames", List.of("ROLE_USER", "ROLE_ADMIN"));
        ReflectionTestUtils.setField(userManagementService, "tokenExpiryHours", 24);
    }

    @Nested
    @DisplayName("Find Users Tests")
    class FindUsersTests {

        @Test
        @DisplayName("Should return all users as DTOs")
        void whenFindAllUsers_thenReturnsAllUserDtos() {
            // Arrange
            List<User> users = List.of(testUser);
            when(userRepository.findAll()).thenReturn(users);
            when(mapper.toUserDto(testUser)).thenReturn(testUserDto);

            // Act
            List<UserDto> result = userManagementService.findAllUsers();

            // Assert
            assertThat(result).hasSize(1);
            assertThat(result.get(0)).isEqualTo(testUserDto);
            verify(userRepository).findAll();
            verify(mapper).toUserDto(testUser);
        }

        @Test
        @DisplayName("Should return assignable users")
        void whenFindAssignableUsers_thenReturnsUsersWithAssignableRoles() {
            // Arrange
            List<String> assignableRoles = List.of("ROLE_USER", "ROLE_ADMIN");
            List<User> users = List.of(testUser);
            when(userRepository.findByRole_NameIn(assignableRoles)).thenReturn(users);
            when(mapper.toUserDto(testUser)).thenReturn(testUserDto);

            // Act
            List<UserDto> result = userManagementService.findAssignableUsers();

            // Assert
            assertThat(result).hasSize(1);
            assertThat(result.get(0)).isEqualTo(testUserDto);
            verify(userRepository).findByRole_NameIn(assignableRoles);
        }

        @Test
        @DisplayName("Should return empty list when no assignable roles configured")
        void whenFindAssignableUsers_withNoAssignableRoles_thenReturnsEmptyList() {
            // Arrange
            ReflectionTestUtils.setField(userManagementService, "assignableRoleNames", List.of());

            // Act
            List<UserDto> result = userManagementService.findAssignableUsers();

            // Assert
            assertThat(result).isEmpty();
            verify(userRepository, never()).findByRole_NameIn(any());
        }
    }

    @Nested
    @DisplayName("User Invitation Tests")
    class UserInvitationTests {

        @Test
        @DisplayName("Should successfully invite new user")
        void whenInviteUser_withValidData_thenCreatesUserAndSendsEmail() throws MessagingException {
            // Arrange
            UserInviteRequest inviteRequest = new UserInviteRequest("<EMAIL>", 1);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
            when(roleRepository.findById(1)).thenReturn(Optional.of(testRole));
            when(passwordEncoder.encode(anyString())).thenReturn("encodedTempPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);
            when(mapper.toUserDto(testUser)).thenReturn(testUserDto);
            doNothing().when(emailService).sendUserInvitationEmail(anyString(), anyString());

            // Act
            UserDto result = userManagementService.inviteUser(inviteRequest);

            // Assert
            assertThat(result).isEqualTo(testUserDto);
            
            ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
            verify(userRepository).save(userCaptor.capture());
            User savedUser = userCaptor.getValue();
            assertThat(savedUser.getEmail()).isEqualTo("<EMAIL>");
            assertThat(savedUser.getUsername()).isEqualTo("<EMAIL>");
            assertThat(savedUser.isEnabled()).isFalse();

            ArgumentCaptor<VerificationToken> tokenCaptor = ArgumentCaptor.forClass(VerificationToken.class);
            verify(tokenRepository).save(tokenCaptor.capture());
            
            verify(emailService).sendUserInvitationEmail(eq("<EMAIL>"), anyString());
        }

        @Test
        @DisplayName("Should throw exception when email already exists")
        void whenInviteUser_withExistingEmail_thenThrowsException() {
            // Arrange
            UserInviteRequest inviteRequest = new UserInviteRequest("<EMAIL>", 1);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.inviteUser(inviteRequest))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Error: Email is already in use!");
            
            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when role not found")
        void whenInviteUser_withNonExistentRole_thenThrowsException() {
            // Arrange
            UserInviteRequest inviteRequest = new UserInviteRequest("<EMAIL>", 999);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
            when(roleRepository.findById(999)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.inviteUser(inviteRequest))
                    .isInstanceOf(EntityNotFoundException.class)
                    .hasMessage("Role not found with id: 999");
        }

        @Test
        @DisplayName("Should throw runtime exception when email sending fails")
        void whenInviteUser_andEmailFails_thenThrowsRuntimeException() throws MessagingException {
            // Arrange
            UserInviteRequest inviteRequest = new UserInviteRequest("<EMAIL>", 1);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
            when(roleRepository.findById(1)).thenReturn(Optional.of(testRole));
            when(passwordEncoder.encode(anyString())).thenReturn("encodedTempPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);
            doThrow(new MessagingException("SMTP error")).when(emailService)
                    .sendUserInvitationEmail(anyString(), anyString());

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.inviteUser(inviteRequest))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessage("Unable to send invitation email. Please try again later.")
                    .hasCauseInstanceOf(MessagingException.class);
        }
    }

    @Nested
    @DisplayName("User Role Update Tests")
    class UserRoleUpdateTests {

        @Test
        @DisplayName("Should successfully update user role")
        void whenUpdateUserRole_withValidData_thenUpdatesRole() {
            // Arrange
            UserUpdateRoleRequest updateRequest = new UserUpdateRoleRequest(2);
            Role newRole = TestDataBuilder.createAdminRole();
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(roleRepository.findById(2)).thenReturn(Optional.of(newRole));
            when(userRepository.save(testUser)).thenReturn(testUser);
            when(mapper.toUserDto(testUser)).thenReturn(testUserDto);

            // Act
            UserDto result = userManagementService.updateUserRole(1L, updateRequest);

            // Assert
            assertThat(result).isEqualTo(testUserDto);
            verify(userRepository).save(testUser);
            assertThat(testUser.getRole()).isEqualTo(newRole);
        }

        @Test
        @DisplayName("Should throw exception when user not found")
        void whenUpdateUserRole_withNonExistentUser_thenThrowsException() {
            // Arrange
            UserUpdateRoleRequest updateRequest = new UserUpdateRoleRequest(2);
            when(userRepository.findById(999L)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.updateUserRole(999L, updateRequest))
                    .isInstanceOf(EntityNotFoundException.class)
                    .hasMessage("User not found with id: 999");
        }

        @Test
        @DisplayName("Should throw exception when role not found")
        void whenUpdateUserRole_withNonExistentRole_thenThrowsException() {
            // Arrange
            UserUpdateRoleRequest updateRequest = new UserUpdateRoleRequest(999);
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(roleRepository.findById(999)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.updateUserRole(1L, updateRequest))
                    .isInstanceOf(EntityNotFoundException.class)
                    .hasMessage("Role not found with id: 999");
        }
    }

    @Nested
    @DisplayName("User Deletion Tests")
    class UserDeletionTests {

        @Test
        @DisplayName("Should successfully delete user")
        void whenDeleteUser_withValidId_thenDeletesUser() {
            // Arrange
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));

            // Act
            userManagementService.deleteUser(1L);

            // Assert
            verify(userRepository).deleteById(1L);
        }

        @Test
        @DisplayName("Should throw exception when user not found")
        void whenDeleteUser_withNonExistentId_thenThrowsException() {
            // Arrange
            when(userRepository.findById(999L)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.deleteUser(999L))
                    .isInstanceOf(EntityNotFoundException.class)
                    .hasMessage("User not found with id: 999");
            
            verify(userRepository, never()).deleteById(any());
        }

        @Test
        @DisplayName("Should throw exception when user tries to delete themselves")
        void whenDeleteUser_andUserDeletesThemselves_thenThrowsException() {
            // Arrange
            SecurityContext securityContext = mock(SecurityContext.class);
            Authentication authentication = mock(Authentication.class);
            when(securityContext.getAuthentication()).thenReturn(authentication);
            when(authentication.getName()).thenReturn("testuser");
            SecurityContextHolder.setContext(securityContext);
            
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.deleteUser(1L))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("You cannot delete your own account");
            
            verify(userRepository, never()).deleteById(any());
            
            // Cleanup
            SecurityContextHolder.clearContext();
        }

        @Test
        @DisplayName("Should throw specific exception for stakeholder constraint violation")
        void whenDeleteUser_withStakeholderConstraint_thenThrowsSpecificException() {
            // Arrange
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            doThrow(new DataIntegrityViolationException("foreign key constraint violation: stakeholder"))
                    .when(userRepository).deleteById(1L);

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.deleteUser(1L))
                    .isInstanceOf(IllegalStateException.class)
                    .hasMessage("Cannot delete user because they are assigned as a stakeholder in one or more projects");
        }

        @Test
        @DisplayName("Should throw generic exception for other foreign key violations")
        void whenDeleteUser_withForeignKeyConstraint_thenThrowsGenericException() {
            // Arrange
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            doThrow(new DataIntegrityViolationException("foreign key constraint violation"))
                    .when(userRepository).deleteById(1L);

            // Act & Assert
            assertThatThrownBy(() -> userManagementService.deleteUser(1L))
                    .isInstanceOf(IllegalStateException.class)
                    .hasMessage("Cannot delete user because they have associated data in the system");
        }
    }
}