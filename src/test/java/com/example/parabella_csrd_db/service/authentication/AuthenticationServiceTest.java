package com.example.parabella_csrd_db.service.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.VerificationToken;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.request.LoginRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SetPasswordRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SignUpRequest;
import com.example.parabella_csrd_db.dto.authentication.response.JwtResponse;
import com.example.parabella_csrd_db.security.jwt.JwtUtils;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;
import com.example.parabella_csrd_db.security.utils.TotpUtils;
import com.example.parabella_csrd_db.service.mail.EmailService;
import com.example.parabella_csrd_db.utils.TestDataBuilder;
import jakarta.mail.MessagingException;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Authentication Service Tests")
class AuthenticationServiceTest {

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private VerificationTokenRepository tokenRepository;

    @Mock
    private PasswordEncoder encoder;

    @Mock
    private JwtUtils jwtUtils;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private AuthenticationService authenticationService;

    private User testUser;
    private Role testRole;
    private Authentication mockAuthentication;
    private UserDetailsImpl userDetails;

    @BeforeEach
    void setUp() {
        testRole = TestDataBuilder.aRole().build();
        testUser = TestDataBuilder.aUser().withRole(testRole).build();
        userDetails = TestDataBuilder.aUserDetails();
        mockAuthentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        
        ReflectionTestUtils.setField(authenticationService, "tokenExpiryHours", 1);
    }

    @Nested
    @DisplayName("User Authentication Tests")
    class AuthenticationTests {

        @Test
        @DisplayName("Should return JWT response for valid credentials and TOTP")
        void whenAuthenticate_withValidCredentialsAndTotp_thenReturnsJwtResponse() {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest();
            testUser.setTotpSecret("valid-secret");
            
            when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                    .thenReturn(mockAuthentication);
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
            when(jwtUtils.generateJwtToken(mockAuthentication)).thenReturn("jwt.token.here");

            try (MockedStatic<TotpUtils> totpUtilsMock = mockStatic(TotpUtils.class)) {
                totpUtilsMock.when(() -> TotpUtils.validateCode("valid-secret", 123456)).thenReturn(true);

                // Act
                Object result = authenticationService.authenticate(loginRequest);

                // Assert
                assertThat(result).isInstanceOf(JwtResponse.class);
                JwtResponse jwtResponse = (JwtResponse) result;
                assertThat(jwtResponse.getAccessToken()).isEqualTo("jwt.token.here");
                assertThat(jwtResponse.getUsername()).isEqualTo("testuser");
            }
        }

        @Test
        @DisplayName("Should initiate 2FA setup for first-time user")
        void whenAuthenticate_withFirstTimeUser_thenInitiates2faSetup() {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest("newuser", "password", 0);
            testUser.setTotpSecret(null);
            testUser.setUsername("newuser");
            
            when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
            when(userRepository.findByUsername("newuser")).thenReturn(Optional.of(testUser));

            try (MockedStatic<TotpUtils> totpUtilsMock = mockStatic(TotpUtils.class)) {
                totpUtilsMock.when(TotpUtils::generateSecret).thenReturn("new-secret");
                totpUtilsMock.when(() -> TotpUtils.getQrCodeUrl("new-secret", testUser.getEmail(), "Parabella_Csrd_App"))
                        .thenReturn("qr.code.url");

                // Act
                Object result = authenticationService.authenticate(loginRequest);

                // Assert
                assertThat(result).isInstanceOf(Map.class);
                @SuppressWarnings("unchecked")
                Map<String, Object> response = (Map<String, Object>) result;
                assertThat(response.get("message")).isEqualTo("2FA setup required");
                assertThat(response.get("qrCodeUrl")).isEqualTo("qr.code.url");
                assertThat(response.get("username")).isEqualTo("newuser");

                ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
                verify(userRepository).save(userCaptor.capture());
                assertThat(userCaptor.getValue().getTotpSecret()).isEqualTo("new-secret");
            }
        }

        @Test
        @DisplayName("Should throw exception for invalid TOTP code")
        void whenAuthenticate_withInvalidTotpCode_thenThrowsException() {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest("testuser", "password", 999999);
            testUser.setTotpSecret("valid-secret");
            
            when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

            try (MockedStatic<TotpUtils> totpUtilsMock = mockStatic(TotpUtils.class)) {
                totpUtilsMock.when(() -> TotpUtils.validateCode("valid-secret", 999999)).thenReturn(false);

                // Act & Assert
                assertThatThrownBy(() -> authenticationService.authenticate(loginRequest))
                        .isInstanceOf(IllegalArgumentException.class)
                        .hasMessage("Error: Invalid or missing TOTP code");
            }
        }

        @Test
        @DisplayName("Should throw exception for missing TOTP code")
        void whenAuthenticate_withMissingTotpCode_thenThrowsException() {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest("testuser", "password", null);
            testUser.setTotpSecret("valid-secret");
            
            when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.authenticate(loginRequest))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Error: Invalid or missing TOTP code");
        }

        @Test
        @DisplayName("Should throw exception when user not found after authentication")
        void whenAuthenticate_andUserNotFound_thenThrowsException() {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest();
            
            when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
            when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.authenticate(loginRequest))
                    .isInstanceOf(EntityNotFoundException.class)
                    .hasMessage("User not found after authentication");
        }

        @Test
        @DisplayName("Should propagate authentication manager exceptions")
        void whenAuthenticate_withBadCredentials_thenPropagatesException() {
            // Arrange
            LoginRequest loginRequest = TestDataBuilder.aLoginRequest();
            
            when(authenticationManager.authenticate(any()))
                    .thenThrow(new BadCredentialsException("Invalid credentials"));

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.authenticate(loginRequest))
                    .isInstanceOf(BadCredentialsException.class)
                    .hasMessage("Invalid credentials");
        }
    }

    @Nested
    @DisplayName("User Registration Tests")
    class RegistrationTests {

        @Test
        @DisplayName("Should successfully register new user")
        void whenRegister_withValidData_thenRegistersUser() {
            // Arrange
            SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest();
            
            when(userRepository.existsByUsername("newuser")).thenReturn(false);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
            when(encoder.encode("password123")).thenReturn("encoded-password");
            when(roleRepository.findByName("User")).thenReturn(Optional.of(testRole));

            try (MockedStatic<TotpUtils> totpUtilsMock = mockStatic(TotpUtils.class)) {
                totpUtilsMock.when(TotpUtils::generateSecret).thenReturn("new-secret");
                totpUtilsMock.when(() -> TotpUtils.getQrCodeUrl("new-secret", "<EMAIL>", "Parabella_Csrd_App"))
                        .thenReturn("qr.code.url");

                // Act
                Map<String, Object> result = authenticationService.register(signUpRequest);

                // Assert
                assertThat(result.get("message")).isEqualTo("User registered successfully! Set up your authenticator with this QR:");
                assertThat(result.get("qrCodeUrl")).isEqualTo("qr.code.url");
                assertThat(result.get("username")).isEqualTo("newuser");

                ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
                verify(userRepository).save(userCaptor.capture());
                User savedUser = userCaptor.getValue();
                assertThat(savedUser.getUsername()).isEqualTo("newuser");
                assertThat(savedUser.getEmail()).isEqualTo("<EMAIL>");
                assertThat(savedUser.getTotpSecret()).isEqualTo("new-secret");
            }
        }

        @Test
        @DisplayName("Should throw exception when username already exists")
        void whenRegister_withExistingUsername_thenThrowsException() {
            // Arrange
            SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest("existinguser", "<EMAIL>", "password123");
            when(userRepository.existsByUsername("existinguser")).thenReturn(true);

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.register(signUpRequest))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Error: Username is already taken!");
            
            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when email already exists")
        void whenRegister_withExistingEmail_thenThrowsException() {
            // Arrange
            SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest("newuser", "<EMAIL>", "password123");
            when(userRepository.existsByUsername("newuser")).thenReturn(false);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.register(signUpRequest))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Error: Email is already in use!");
            
            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when default role not found")
        void whenRegister_withMissingDefaultRole_thenThrowsException() {
            // Arrange
            SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest();
            when(userRepository.existsByUsername("newuser")).thenReturn(false);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
            when(roleRepository.findByName("User")).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.register(signUpRequest))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessage("Error: Default 'User' role not found.");
        }
    }

    @Nested
    @DisplayName("Password Reset Tests")
    class PasswordResetTests {

        @Test
        @DisplayName("Should reset password with valid token")
        void whenResetPassword_withValidToken_thenResetsPassword() {
            // Arrange
            String token = "valid-token";
            String newPassword = "newPassword123";
            VerificationToken verificationToken = new VerificationToken();
            verificationToken.setToken(token);
            verificationToken.setUser(testUser);
            verificationToken.setExpiryDate(LocalDateTime.now().plusHours(1));
            
            when(tokenRepository.findByToken(token)).thenReturn(Optional.of(verificationToken));
            when(encoder.encode(newPassword)).thenReturn("encoded-new-password");

            // Act
            authenticationService.resetPassword(token, newPassword);

            // Assert
            verify(userRepository).save(testUser);
            verify(tokenRepository).delete(verificationToken);
            assertThat(testUser.getPassword()).isEqualTo("encoded-new-password");
        }

        @Test
        @DisplayName("Should throw exception for invalid token")
        void whenResetPassword_withInvalidToken_thenThrowsException() {
            // Arrange
            String invalidToken = "invalid-token";
            when(tokenRepository.findByToken(invalidToken)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.resetPassword(invalidToken, "newPassword"))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Invalid password reset token.");
        }

        @Test
        @DisplayName("Should throw exception for expired token")
        void whenResetPassword_withExpiredToken_thenThrowsException() {
            // Arrange
            String token = "expired-token";
            VerificationToken expiredToken = new VerificationToken();
            expiredToken.setToken(token);
            expiredToken.setExpiryDate(LocalDateTime.now().minusHours(1)); // Expired
            
            when(tokenRepository.findByToken(token)).thenReturn(Optional.of(expiredToken));

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.resetPassword(token, "newPassword"))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Password reset token has expired.");
            
            verify(tokenRepository).delete(expiredToken);
        }
    }

    @Nested
    @DisplayName("Set New Password Tests")
    class SetNewPasswordTests {

        @Test
        @DisplayName("Should set new password with valid token")
        void whenSetNewPassword_withValidToken_thenSetsPasswordAndActivatesUser() {
            // Arrange
            SetPasswordRequest request = new SetPasswordRequest("valid-token", "newPassword123");
            VerificationToken verificationToken = new VerificationToken();
            verificationToken.setToken(request.token());
            verificationToken.setUser(testUser);
            verificationToken.setExpiryDate(LocalDateTime.now().plusHours(1));
            testUser.setEnabled(false);
            
            when(tokenRepository.findByToken(request.token())).thenReturn(Optional.of(verificationToken));
            when(encoder.encode(request.password())).thenReturn("encoded-new-password");

            // Act
            authenticationService.setNewPassword(request);

            // Assert
            verify(userRepository).save(testUser);
            verify(tokenRepository).delete(verificationToken);
            assertThat(testUser.getPassword()).isEqualTo("encoded-new-password");
            assertThat(testUser.isEnabled()).isTrue();
        }

        @Test
        @DisplayName("Should throw exception for invalid token")
        void whenSetNewPassword_withInvalidToken_thenThrowsException() {
            // Arrange
            SetPasswordRequest request = new SetPasswordRequest("invalid-token", "newPassword123");
            when(tokenRepository.findByToken(request.token())).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.setNewPassword(request))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Invalid token.");
        }

        @Test
        @DisplayName("Should throw exception for expired token")
        void whenSetNewPassword_withExpiredToken_thenThrowsException() {
            // Arrange
            SetPasswordRequest request = new SetPasswordRequest("expired-token", "newPassword123");
            VerificationToken expiredToken = new VerificationToken();
            expiredToken.setToken(request.token());
            expiredToken.setExpiryDate(LocalDateTime.now().minusHours(1)); // Expired
            
            when(tokenRepository.findByToken(request.token())).thenReturn(Optional.of(expiredToken));

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.setNewPassword(request))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Token has expired.");
            
            verify(tokenRepository).delete(expiredToken);
        }
    }

    @Nested
    @DisplayName("2FA Verification Tests")
    class TwoFactorVerificationTests {

        @Test
        @DisplayName("Should verify 2FA with valid code")
        void whenVerify2fa_withValidCode_thenSucceeds() {
            // Arrange
            String username = "testuser";
            Integer code = 123456;
            testUser.setTotpSecret("valid-secret");
            
            when(userRepository.findByUsername(username)).thenReturn(Optional.of(testUser));

            try (MockedStatic<TotpUtils> totpUtilsMock = mockStatic(TotpUtils.class)) {
                totpUtilsMock.when(() -> TotpUtils.validateCode("valid-secret", code)).thenReturn(true);

                // Act & Assert - should not throw exception
                authenticationService.verify2fa(username, code);
            }
        }

        @Test
        @DisplayName("Should throw exception for invalid 2FA code")
        void whenVerify2fa_withInvalidCode_thenThrowsException() {
            // Arrange
            String username = "testuser";
            Integer code = 999999;
            testUser.setTotpSecret("valid-secret");
            
            when(userRepository.findByUsername(username)).thenReturn(Optional.of(testUser));

            try (MockedStatic<TotpUtils> totpUtilsMock = mockStatic(TotpUtils.class)) {
                totpUtilsMock.when(() -> TotpUtils.validateCode("valid-secret", code)).thenReturn(false);

                // Act & Assert
                assertThatThrownBy(() -> authenticationService.verify2fa(username, code))
                        .isInstanceOf(IllegalArgumentException.class)
                        .hasMessage("Invalid TOTP code.");
            }
        }

        @Test
        @DisplayName("Should throw exception when user not found")
        void whenVerify2fa_withNonExistentUser_thenThrowsException() {
            // Arrange
            String username = "nonexistent";
            Integer code = 123456;
            
            when(userRepository.findByUsername(username)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.verify2fa(username, code))
                    .isInstanceOf(EntityNotFoundException.class)
                    .hasMessage("User not found");
        }

        @Test
        @DisplayName("Should throw exception when 2FA not initiated")
        void whenVerify2fa_with2faNotInitiated_thenThrowsException() {
            // Arrange
            String username = "testuser";
            Integer code = 123456;
            testUser.setTotpSecret(null); // No 2FA setup
            
            when(userRepository.findByUsername(username)).thenReturn(Optional.of(testUser));

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.verify2fa(username, code))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("2FA setup has not been initiated for this user.");
        }

        @Test
        @DisplayName("Should throw exception when 2FA secret is empty")
        void whenVerify2fa_with2faSecretEmpty_thenThrowsException() {
            // Arrange
            String username = "testuser";
            Integer code = 123456;
            testUser.setTotpSecret(""); // Empty 2FA secret
            
            when(userRepository.findByUsername(username)).thenReturn(Optional.of(testUser));

            // Act & Assert
            assertThatThrownBy(() -> authenticationService.verify2fa(username, code))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("2FA setup has not been initiated for this user.");
        }
    }

    // TODO: Add integration tests for password reset initiation once email functionality is enabled
    // TODO: Add tests for concurrent authentication attempts  
    // TODO: Add tests for token cleanup and expiry scenarios
    // TODO: Add performance tests for authentication under load
}