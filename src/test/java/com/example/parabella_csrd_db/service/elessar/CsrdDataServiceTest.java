package com.example.parabella_csrd_db.service.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.displayContent.CsrdSubtopic;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.displayContent.CsrdTopic;
import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdTopicRepository;
import com.example.parabella_csrd_db.database.vectordatabase.model.EsrsDatapoint;
import com.example.parabella_csrd_db.database.vectordatabase.repository.EsrsDatapointRepository;
import com.example.parabella_csrd_db.dto.elessar.CsrdSubtopicDTO;
import com.example.parabella_csrd_db.dto.elessar.CsrdTopicDTO;
import com.example.parabella_csrd_db.dto.elessar.EsrsDatapointDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CSRD Data Service Tests")
class CsrdDataServiceTest {

    @Mock
    private CsrdTopicRepository csrdTopicRepository;

    @Mock
    private EsrsDatapointRepository esrsDatapointRepository;

    @InjectMocks
    private CsrdDataService csrdDataService;

    private CsrdTopic testTopic;
    private CsrdSubtopic testSubtopic;
    private EsrsDatapoint testDatapoint;

    @BeforeEach
    void setUp() {
        // Setup test topic
        testTopic = new CsrdTopic();
        testTopic.setId(1L);
        testTopic.setTopicCode("E1");
        testTopic.setTopicName("Climate change");
        testTopic.setTopicDescription("Climate change related disclosures");

        // Setup test subtopic
        testSubtopic = new CsrdSubtopic();
        testSubtopic.setId(1L);
        testSubtopic.setSubtopicCode("E1-1");
        testSubtopic.setSubtopicName("Transition plan");
        testSubtopic.setSubtopicDescription("Transition plan for climate change");
        testSubtopic.setCsrdTopic(testTopic);

        testTopic.setSubtopics(Arrays.asList(testSubtopic));

        // Setup test datapoint
        testDatapoint = new EsrsDatapoint();
        testDatapoint.setId("datapoint-1");
        testDatapoint.setDisclosureRequirement("E1-1");
        testDatapoint.setDatapointCode("E1-1_01");
        testDatapoint.setDatapointLabel("Transition plan description");
        testDatapoint.setDataType("Narrative");
    }

    @Nested
    @DisplayName("Get Aggregated CSRD Data Tests")
    class GetAggregatedCsrdDataTests {

        @Test
        @DisplayName("Should return aggregated CSRD data with topics and datapoints")
        void whenGetAggregatedCsrdData_withValidData_thenReturnsAggregatedData() {
            // Arrange
            List<CsrdTopic> topics = Arrays.asList(testTopic);
            List<EsrsDatapoint> datapoints = Arrays.asList(testDatapoint);

            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(topics);
            when(esrsDatapointRepository.findAll()).thenReturn(datapoints);

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);

            CsrdTopicDTO topicDTO = result.get(0);
            assertThat(topicDTO.getId()).isEqualTo(1L);
            assertThat(topicDTO.getTopicCode()).isEqualTo("E1");
            assertThat(topicDTO.getTopicName()).isEqualTo("Climate change");
            assertThat(topicDTO.getTopicDescription()).isEqualTo("Climate change related disclosures");

            // Verify subtopics are included
            assertThat(topicDTO.getSubtopics()).isNotNull();
            assertThat(topicDTO.getSubtopics()).hasSize(1);

            CsrdSubtopicDTO subtopicDTO = topicDTO.getSubtopics().get(0);
            assertThat(subtopicDTO.getId()).isEqualTo(1L);
            assertThat(subtopicDTO.getSubtopicCode()).isEqualTo("E1-1");
            assertThat(subtopicDTO.getSubtopicName()).isEqualTo("Transition plan");

            // Verify datapoints are associated
            assertThat(subtopicDTO.getDatapoints()).isNotNull();
            assertThat(subtopicDTO.getDatapoints()).hasSize(1);

            EsrsDatapointDTO datapointDTO = subtopicDTO.getDatapoints().get(0);
            assertThat(datapointDTO.getId()).isEqualTo("datapoint-1");
            assertThat(datapointDTO.getDatapointCode()).isEqualTo("E1-1_01");
            assertThat(datapointDTO.getDatapointLabel()).isEqualTo("Transition plan description");

            verify(csrdTopicRepository).findAllWithSubtopics();
            verify(esrsDatapointRepository).findAll();
        }

        @Test
        @DisplayName("Should handle empty topics list")
        void whenGetAggregatedCsrdData_withEmptyTopics_thenReturnsEmptyList() {
            // Arrange
            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(Collections.emptyList());
            when(esrsDatapointRepository.findAll()).thenReturn(Arrays.asList(testDatapoint));

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEmpty();

            verify(csrdTopicRepository).findAllWithSubtopics();
            verify(esrsDatapointRepository).findAll();
        }

        @Test
        @DisplayName("Should handle empty datapoints list")
        void whenGetAggregatedCsrdData_withEmptyDatapoints_thenReturnsTopicsWithoutDatapoints() {
            // Arrange
            List<CsrdTopic> topics = Arrays.asList(testTopic);
            
            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(topics);
            when(esrsDatapointRepository.findAll()).thenReturn(Collections.emptyList());

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);

            CsrdTopicDTO topicDTO = result.get(0);
            assertThat(topicDTO.getSubtopics()).isNotNull();
            assertThat(topicDTO.getSubtopics()).hasSize(1);

            CsrdSubtopicDTO subtopicDTO = topicDTO.getSubtopics().get(0);
            assertThat(subtopicDTO.getDatapoints()).isNotNull();
            assertThat(subtopicDTO.getDatapoints()).isEmpty();

            verify(csrdTopicRepository).findAllWithSubtopics();
            verify(esrsDatapointRepository).findAll();
        }

        @Test
        @DisplayName("Should handle topics with no subtopics")
        void whenGetAggregatedCsrdData_withTopicsWithoutSubtopics_thenReturnsTopicsOnly() {
            // Arrange
            CsrdTopic topicWithoutSubtopics = new CsrdTopic();
            topicWithoutSubtopics.setId(2L);
            topicWithoutSubtopics.setTopicCode("E2");
            topicWithoutSubtopics.setTopicName("Pollution");
            topicWithoutSubtopics.setSubtopics(Collections.emptyList());

            List<CsrdTopic> topics = Arrays.asList(topicWithoutSubtopics);
            
            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(topics);
            when(esrsDatapointRepository.findAll()).thenReturn(Arrays.asList(testDatapoint));

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);

            CsrdTopicDTO topicDTO = result.get(0);
            assertThat(topicDTO.getId()).isEqualTo(2L);
            assertThat(topicDTO.getTopicCode()).isEqualTo("E2");
            assertThat(topicDTO.getSubtopics()).isNotNull();
            assertThat(topicDTO.getSubtopics()).isEmpty();
        }

        @Test
        @DisplayName("Should handle multiple topics with multiple subtopics")
        void whenGetAggregatedCsrdData_withMultipleTopicsAndSubtopics_thenReturnsAllData() {
            // Arrange
            CsrdTopic topic2 = new CsrdTopic();
            topic2.setId(2L);
            topic2.setTopicCode("E2");
            topic2.setTopicName("Pollution");

            CsrdSubtopic subtopic2 = new CsrdSubtopic();
            subtopic2.setId(2L);
            subtopic2.setSubtopicCode("E2-1");
            subtopic2.setSubtopicName("Air emissions");
            subtopic2.setCsrdTopic(topic2);

            topic2.setSubtopics(Arrays.asList(subtopic2));

            EsrsDatapoint datapoint2 = new EsrsDatapoint();
            datapoint2.setId("datapoint-2");
            datapoint2.setDisclosureRequirement("E2-1");
            datapoint2.setDatapointCode("E2-1_01");
            datapoint2.setDatapointLabel("Air emissions data");

            List<CsrdTopic> topics = Arrays.asList(testTopic, topic2);
            List<EsrsDatapoint> datapoints = Arrays.asList(testDatapoint, datapoint2);

            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(topics);
            when(esrsDatapointRepository.findAll()).thenReturn(datapoints);

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(2);

            // Verify first topic
            CsrdTopicDTO topicDTO1 = result.stream()
                    .filter(t -> "E1".equals(t.getTopicCode()))
                    .findFirst()
                    .orElse(null);
            assertThat(topicDTO1).isNotNull();
            assertThat(topicDTO1.getSubtopics()).hasSize(1);
            assertThat(topicDTO1.getSubtopics().get(0).getDatapoints()).hasSize(1);

            // Verify second topic
            CsrdTopicDTO topicDTO2 = result.stream()
                    .filter(t -> "E2".equals(t.getTopicCode()))
                    .findFirst()
                    .orElse(null);
            assertThat(topicDTO2).isNotNull();
            assertThat(topicDTO2.getSubtopics()).hasSize(1);
            assertThat(topicDTO2.getSubtopics().get(0).getDatapoints()).hasSize(1);
        }

        @Test
        @DisplayName("Should handle datapoints that don't match any subtopic")
        void whenGetAggregatedCsrdData_withUnmatchedDatapoints_thenIgnoresUnmatchedDatapoints() {
            // Arrange
            EsrsDatapoint unmatchedDatapoint = new EsrsDatapoint();
            unmatchedDatapoint.setId("datapoint-unmatched");
            unmatchedDatapoint.setDisclosureRequirement("E3-1"); // No matching subtopic
            unmatchedDatapoint.setDatapointCode("E3-1_01");

            List<CsrdTopic> topics = Arrays.asList(testTopic);
            List<EsrsDatapoint> datapoints = Arrays.asList(testDatapoint, unmatchedDatapoint);

            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(topics);
            when(esrsDatapointRepository.findAll()).thenReturn(datapoints);

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);

            CsrdTopicDTO topicDTO = result.get(0);
            CsrdSubtopicDTO subtopicDTO = topicDTO.getSubtopics().get(0);
            
            // Should only contain the matched datapoint
            assertThat(subtopicDTO.getDatapoints()).hasSize(1);
            assertThat(subtopicDTO.getDatapoints().get(0).getId()).isEqualTo("datapoint-1");
        }

        @Test
        @DisplayName("Should propagate repository exceptions")
        void whenGetAggregatedCsrdData_withRepositoryException_thenPropagatesException() {
            // Arrange
            when(csrdTopicRepository.findAllWithSubtopics())
                    .thenThrow(new DataAccessException("Database connection error") {});

            // Act & Assert
            assertThatThrownBy(() -> csrdDataService.getAggregatedCsrdData())
                    .isInstanceOf(DataAccessException.class)
                    .hasMessage("Database connection error");

            verify(csrdTopicRepository).findAllWithSubtopics();
            verify(esrsDatapointRepository, never()).findAll();
        }

        @Test
        @DisplayName("Should handle vector database exceptions")
        void whenGetAggregatedCsrdData_withVectorDbException_thenPropagatesException() {
            // Arrange
            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(Arrays.asList(testTopic));
            when(esrsDatapointRepository.findAll())
                    .thenThrow(new DataAccessException("Vector database error") {});

            // Act & Assert
            assertThatThrownBy(() -> csrdDataService.getAggregatedCsrdData())
                    .isInstanceOf(DataAccessException.class)
                    .hasMessage("Vector database error");

            verify(csrdTopicRepository).findAllWithSubtopics();
            verify(esrsDatapointRepository).findAll();
        }

        @Test
        @DisplayName("Should handle null subtopics gracefully")
        void whenGetAggregatedCsrdData_withNullSubtopics_thenHandlesGracefully() {
            // Arrange
            CsrdTopic topicWithNullSubtopics = new CsrdTopic();
            topicWithNullSubtopics.setId(1L);
            topicWithNullSubtopics.setTopicCode("E1");
            topicWithNullSubtopics.setTopicName("Climate change");
            topicWithNullSubtopics.setSubtopics(null);

            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(Arrays.asList(topicWithNullSubtopics));
            when(esrsDatapointRepository.findAll()).thenReturn(Arrays.asList(testDatapoint));

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);

            CsrdTopicDTO topicDTO = result.get(0);
            assertThat(topicDTO.getSubtopics()).isNotNull();
            assertThat(topicDTO.getSubtopics()).isEmpty();
        }

        @Test
        @DisplayName("Should handle null disclosure requirement in datapoints")
        void whenGetAggregatedCsrdData_withNullDisclosureRequirement_thenHandlesGracefully() {
            // Arrange
            EsrsDatapoint datapointWithNullDisclosure = new EsrsDatapoint();
            datapointWithNullDisclosure.setId("datapoint-null");
            datapointWithNullDisclosure.setDisclosureRequirement(null);
            datapointWithNullDisclosure.setDatapointCode("NULL_01");

            List<CsrdTopic> topics = Arrays.asList(testTopic);
            List<EsrsDatapoint> datapoints = Arrays.asList(testDatapoint, datapointWithNullDisclosure);

            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(topics);
            when(esrsDatapointRepository.findAll()).thenReturn(datapoints);

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);

            CsrdTopicDTO topicDTO = result.get(0);
            CsrdSubtopicDTO subtopicDTO = topicDTO.getSubtopics().get(0);
            
            // Should only contain the datapoint with valid disclosure requirement
            assertThat(subtopicDTO.getDatapoints()).hasSize(1);
            assertThat(subtopicDTO.getDatapoints().get(0).getId()).isEqualTo("datapoint-1");
        }
    }

    @Nested
    @DisplayName("Performance and Memory Tests")
    class PerformanceMemoryTests {

        @Test
        @DisplayName("Should handle large datasets efficiently")
        void whenGetAggregatedCsrdData_withLargeDataset_thenHandlesEfficiently() {
            // Arrange - Create a large dataset
            List<CsrdTopic> largeTopicList = Collections.nCopies(100, testTopic);
            List<EsrsDatapoint> largeDatapointList = Collections.nCopies(1000, testDatapoint);

            when(csrdTopicRepository.findAllWithSubtopics()).thenReturn(largeTopicList);
            when(esrsDatapointRepository.findAll()).thenReturn(largeDatapointList);

            // Act
            List<CsrdTopicDTO> result = csrdDataService.getAggregatedCsrdData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(100);

            verify(csrdTopicRepository).findAllWithSubtopics();
            verify(esrsDatapointRepository).findAll();
        }
    }

    // TODO: Add integration tests with actual database connections
    // TODO: Add tests for transaction boundary behavior
    // TODO: Add tests for concurrent access scenarios
    // TODO: Add tests for datapoint grouping optimization
    // TODO: Add performance benchmarks for large datasets
    // TODO: Add tests for different transaction manager configurations
    // TODO: Add tests for DTO mapping edge cases
}