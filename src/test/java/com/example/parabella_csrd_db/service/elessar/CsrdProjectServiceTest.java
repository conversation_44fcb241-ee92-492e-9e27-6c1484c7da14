package com.example.parabella_csrd_db.service.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.CsrdProject;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.landing.CompanyInfo;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdProjectRepository;
import com.example.parabella_csrd_db.dto.elessar.CompanyInfoDTO;
import com.example.parabella_csrd_db.dto.elessar.CsrdProjectDTO;
import com.example.parabella_csrd_db.utilities.mapper.CsrdDtoMapper;
import com.example.parabella_csrd_db.utils.TestDataBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;
import org.springframework.web.server.ResponseStatusException;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CSRD Project Service Tests")
class CsrdProjectServiceTest {

    @Mock
    private CsrdProjectRepository csrdProjectRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private CsrdDtoMapper csrdDtoMapper;

    @InjectMocks
    private CsrdProjectService csrdProjectService;

    private User testUser;
    private CsrdProject testProject;
    private CsrdProjectDTO testProjectDTO;
    private CompanyInfo testCompanyInfo;
    private CompanyInfoDTO testCompanyInfoDTO;

    @BeforeEach
    void setUp() {
        testUser = TestDataBuilder.aUser().build();

        testCompanyInfo = new CompanyInfo();
        testCompanyInfo.setId(1L);
        testCompanyInfo.setCompanyName("Test Company");
        testCompanyInfo.setSize("Large");

        testProject = new CsrdProject();
        testProject.setId(1L);
        testProject.setProjectName("Test CSRD Project");
        testProject.setUser(testUser);
        testProject.setCompanyInfo(testCompanyInfo);

        testProjectDTO = new CsrdProjectDTO();
        testProjectDTO.setId(1L);
        testProjectDTO.setProjectName("Test CSRD Project");
        testProjectDTO.setUserId(testUser.getId());

        testCompanyInfoDTO = new CompanyInfoDTO();
        testCompanyInfoDTO.setId(1L);
        testCompanyInfoDTO.setCompanyName("Test Company");
        testCompanyInfoDTO.setSize("Large");
    }

    @Nested
    @DisplayName("Get User CSRD Projects Tests")
    class GetUserCsrdProjectsTests {

        @Test
        @DisplayName("Should return user's CSRD projects")
        void whenGetUserCsrdProjects_withValidUserId_thenReturnsProjects() {
            // Arrange
            List<CsrdProject> projects = Arrays.asList(testProject);
            List<CsrdProjectDTO> projectDTOs = Arrays.asList(testProjectDTO);

            when(userRepository.existsById(testUser.getId())).thenReturn(true);
            when(csrdProjectRepository.findByUserId(testUser.getId())).thenReturn(projects);
            when(csrdDtoMapper.toCsrdProjectDTOList(projects)).thenReturn(projectDTOs);

            // Act
            List<CsrdProjectDTO> result = csrdProjectService.getUserCsrdProjects(testUser.getId());

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0)).isEqualTo(testProjectDTO);

            verify(userRepository).existsById(testUser.getId());
            verify(csrdProjectRepository).findByUserId(testUser.getId());
            verify(csrdDtoMapper).toCsrdProjectDTOList(projects);
        }

        @Test
        @DisplayName("Should return empty list when user has no projects")
        void whenGetUserCsrdProjects_withNoProjects_thenReturnsEmptyList() {
            // Arrange
            when(userRepository.existsById(testUser.getId())).thenReturn(true);
            when(csrdProjectRepository.findByUserId(testUser.getId())).thenReturn(Collections.emptyList());
            when(csrdDtoMapper.toCsrdProjectDTOList(Collections.emptyList())).thenReturn(Collections.emptyList());

            // Act
            List<CsrdProjectDTO> result = csrdProjectService.getUserCsrdProjects(testUser.getId());

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEmpty();

            verify(userRepository).existsById(testUser.getId());
            verify(csrdProjectRepository).findByUserId(testUser.getId());
        }

        @Test
        @DisplayName("Should throw exception when user not found")
        void whenGetUserCsrdProjects_withNonExistentUser_thenThrowsException() {
            // Arrange
            Long nonExistentUserId = 999L;
            when(userRepository.existsById(nonExistentUserId)).thenReturn(false);

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.getUserCsrdProjects(nonExistentUserId))
                    .isInstanceOf(ResponseStatusException.class)
                    .hasMessageContaining("User not found with id: " + nonExistentUserId);

            verify(userRepository).existsById(nonExistentUserId);
            verify(csrdProjectRepository, never()).findByUserId(any());
        }

        @Test
        @DisplayName("Should handle repository exceptions")
        void whenGetUserCsrdProjects_withRepositoryException_thenPropagatesException() {
            // Arrange
            when(userRepository.existsById(testUser.getId())).thenReturn(true);
            when(csrdProjectRepository.findByUserId(testUser.getId()))
                    .thenThrow(new DataAccessException("Database error") {});

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.getUserCsrdProjects(testUser.getId()))
                    .isInstanceOf(DataAccessException.class)
                    .hasMessage("Database error");

            verify(userRepository).existsById(testUser.getId());
            verify(csrdProjectRepository).findByUserId(testUser.getId());
        }
    }

    @Nested
    @DisplayName("Get CSRD Project By ID Tests")
    class GetCsrdProjectByIdTests {

        @Test
        @DisplayName("Should return project by ID")
        void whenGetCsrdProjectById_withValidId_thenReturnsProject() {
            // Arrange
            when(csrdProjectRepository.findByIdWithCompanyInfo(testProject.getId()))
                    .thenReturn(Optional.of(testProject));
            when(csrdDtoMapper.toCsrdProjectDTO(testProject)).thenReturn(testProjectDTO);

            // Act
            CsrdProjectDTO result = csrdProjectService.getCsrdProjectById(testProject.getId());

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(testProjectDTO);

            verify(csrdProjectRepository).findByIdWithCompanyInfo(testProject.getId());
            verify(csrdDtoMapper).toCsrdProjectDTO(testProject);
        }

        @Test
        @DisplayName("Should throw exception when project not found")
        void whenGetCsrdProjectById_withNonExistentId_thenThrowsException() {
            // Arrange
            Long nonExistentId = 999L;
            when(csrdProjectRepository.findByIdWithCompanyInfo(nonExistentId))
                    .thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.getCsrdProjectById(nonExistentId))
                    .isInstanceOf(ResponseStatusException.class)
                    .hasMessageContaining("CsrdProject not found with id: " + nonExistentId);

            verify(csrdProjectRepository).findByIdWithCompanyInfo(nonExistentId);
            verify(csrdDtoMapper, never()).toCsrdProjectDTO(any());
        }

        @Test
        @DisplayName("Should handle repository exceptions")
        void whenGetCsrdProjectById_withRepositoryException_thenPropagatesException() {
            // Arrange
            when(csrdProjectRepository.findByIdWithCompanyInfo(testProject.getId()))
                    .thenThrow(new DataAccessException("Database connection error") {});

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.getCsrdProjectById(testProject.getId()))
                    .isInstanceOf(DataAccessException.class)
                    .hasMessage("Database connection error");
        }
    }

    @Nested
    @DisplayName("Create CSRD Project Tests")
    class CreateCsrdProjectTests {

        @Test
        @DisplayName("Should create new CSRD project with empty company info")
        void whenCreateCsrdProject_withValidDTO_thenCreatesProjectWithCompanyInfo() {
            // Arrange
            CsrdProject newProject = new CsrdProject();
            newProject.setId(null); // New project
            newProject.setProjectName(testProjectDTO.getProjectName());
            newProject.setUser(testUser);

            CsrdProject savedProject = new CsrdProject();
            savedProject.setId(1L);
            savedProject.setProjectName(testProjectDTO.getProjectName());
            savedProject.setUser(testUser);
            savedProject.setCompanyInfo(new CompanyInfo()); // Auto-created empty company info

            when(userRepository.findById(testProjectDTO.getUserId())).thenReturn(Optional.of(testUser));
            when(csrdDtoMapper.projectDtoToEntityForCreation(testProjectDTO, testUser)).thenReturn(newProject);
            when(csrdProjectRepository.save(any(CsrdProject.class))).thenReturn(savedProject);
            when(csrdDtoMapper.toCsrdProjectDTO(savedProject)).thenReturn(testProjectDTO);

            // Act
            CsrdProjectDTO result = csrdProjectService.createCsrdProject(testProjectDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(testProjectDTO);

            // Verify that the project was saved with company info
            ArgumentCaptor<CsrdProject> projectCaptor = ArgumentCaptor.forClass(CsrdProject.class);
            verify(csrdProjectRepository).save(projectCaptor.capture());
            CsrdProject capturedProject = projectCaptor.getValue();
            assertThat(capturedProject.getCompanyInfo()).isNotNull();

            verify(userRepository).findById(testProjectDTO.getUserId());
            verify(csrdDtoMapper).projectDtoToEntityForCreation(testProjectDTO, testUser);
            verify(csrdDtoMapper).toCsrdProjectDTO(savedProject);
        }

        @Test
        @DisplayName("Should throw exception when user not found")
        void whenCreateCsrdProject_withNonExistentUser_thenThrowsException() {
            // Arrange
            Long nonExistentUserId = 999L;
            testProjectDTO.setUserId(nonExistentUserId);
            when(userRepository.findById(nonExistentUserId)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.createCsrdProject(testProjectDTO))
                    .isInstanceOf(ResponseStatusException.class)
                    .hasMessageContaining("User specified in request not found with id: " + nonExistentUserId);

            verify(userRepository).findById(nonExistentUserId);
            verify(csrdProjectRepository, never()).save(any(CsrdProject.class));
        }

        @Test
        @DisplayName("Should handle repository save exceptions")
        void whenCreateCsrdProject_withSaveException_thenPropagatesException() {
            // Arrange
            CsrdProject newProject = new CsrdProject();
            newProject.setProjectName(testProjectDTO.getProjectName());
            newProject.setUser(testUser);

            when(userRepository.findById(testProjectDTO.getUserId())).thenReturn(Optional.of(testUser));
            when(csrdDtoMapper.projectDtoToEntityForCreation(testProjectDTO, testUser)).thenReturn(newProject);
            when(csrdProjectRepository.save(any(CsrdProject.class)))
                    .thenThrow(new DataAccessException("Save failed") {});

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.createCsrdProject(testProjectDTO))
                    .isInstanceOf(DataAccessException.class)
                    .hasMessage("Save failed");

            verify(csrdProjectRepository).save(any(CsrdProject.class));
        }

        @Test
        @DisplayName("Should handle mapper exceptions")
        void whenCreateCsrdProject_withMapperException_thenPropagatesException() {
            // Arrange
            when(userRepository.findById(testProjectDTO.getUserId())).thenReturn(Optional.of(testUser));
            when(csrdDtoMapper.projectDtoToEntityForCreation(testProjectDTO, testUser))
                    .thenThrow(new RuntimeException("Mapping error"));

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.createCsrdProject(testProjectDTO))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessage("Mapping error");

            verify(csrdProjectRepository, never()).save(any(CsrdProject.class));
        }
    }

    @Nested
    @DisplayName("Save Or Update Company Info Tests")
    class SaveOrUpdateCompanyInfoTests {

        @Test
        @DisplayName("Should update existing company info")
        void whenSaveOrUpdateCompanyInfo_withExistingCompanyInfo_thenUpdatesInfo() {
            // Arrange
            CsrdProject savedProject = new CsrdProject();
            savedProject.setId(testProject.getId());
            savedProject.setCompanyInfo(testCompanyInfo);

            when(csrdProjectRepository.findById(testProject.getId())).thenReturn(Optional.of(testProject));
            doNothing().when(csrdDtoMapper).updateCompanyInfoFromDTO(testCompanyInfoDTO, testCompanyInfo);
            when(csrdProjectRepository.save(testProject)).thenReturn(savedProject);
            when(csrdDtoMapper.toCompanyInfoDTO(testCompanyInfo)).thenReturn(testCompanyInfoDTO);

            // Act
            CompanyInfoDTO result = csrdProjectService.saveOrUpdateCompanyInfo(testProject.getId(), testCompanyInfoDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(testCompanyInfoDTO);

            verify(csrdProjectRepository).findById(testProject.getId());
            verify(csrdDtoMapper).updateCompanyInfoFromDTO(testCompanyInfoDTO, testCompanyInfo);
            verify(csrdProjectRepository).save(testProject);
            verify(csrdDtoMapper).toCompanyInfoDTO(testCompanyInfo);
        }

        @Test
        @DisplayName("Should create new company info when null")
        void whenSaveOrUpdateCompanyInfo_withNullCompanyInfo_thenCreatesNewInfo() {
            // Arrange
            CsrdProject projectWithoutCompanyInfo = new CsrdProject();
            projectWithoutCompanyInfo.setId(testProject.getId());
            projectWithoutCompanyInfo.setCompanyInfo(null);

            CsrdProject savedProject = new CsrdProject();
            savedProject.setId(testProject.getId());
            savedProject.setCompanyInfo(testCompanyInfo);

            when(csrdProjectRepository.findById(testProject.getId())).thenReturn(Optional.of(projectWithoutCompanyInfo));
            when(csrdDtoMapper.toCompanyInfoEntity(testCompanyInfoDTO)).thenReturn(testCompanyInfo);
            when(csrdProjectRepository.save(projectWithoutCompanyInfo)).thenReturn(savedProject);
            when(csrdDtoMapper.toCompanyInfoDTO(testCompanyInfo)).thenReturn(testCompanyInfoDTO);

            // Act
            CompanyInfoDTO result = csrdProjectService.saveOrUpdateCompanyInfo(testProject.getId(), testCompanyInfoDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(testCompanyInfoDTO);

            verify(csrdDtoMapper).toCompanyInfoEntity(testCompanyInfoDTO);
            verify(csrdDtoMapper, never()).updateCompanyInfoFromDTO(any(), any());
        }

        @Test
        @DisplayName("Should throw exception when project not found")
        void whenSaveOrUpdateCompanyInfo_withNonExistentProject_thenThrowsException() {
            // Arrange
            Long nonExistentId = 999L;
            when(csrdProjectRepository.findById(nonExistentId)).thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.saveOrUpdateCompanyInfo(nonExistentId, testCompanyInfoDTO))
                    .isInstanceOf(ResponseStatusException.class)
                    .hasMessageContaining("CsrdProject not found with id: " + nonExistentId);

            verify(csrdProjectRepository).findById(nonExistentId);
            verify(csrdProjectRepository, never()).save(any());
        }

        @Test
        @DisplayName("Should throw exception when company info is null after save")
        void whenSaveOrUpdateCompanyInfo_withNullCompanyInfoAfterSave_thenThrowsException() {
            // Arrange
            CsrdProject savedProject = new CsrdProject();
            savedProject.setId(testProject.getId());
            savedProject.setCompanyInfo(null); // Null after save

            when(csrdProjectRepository.findById(testProject.getId())).thenReturn(Optional.of(testProject));
            doNothing().when(csrdDtoMapper).updateCompanyInfoFromDTO(testCompanyInfoDTO, testCompanyInfo);
            when(csrdProjectRepository.save(testProject)).thenReturn(savedProject);

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.saveOrUpdateCompanyInfo(testProject.getId(), testCompanyInfoDTO))
                    .isInstanceOf(ResponseStatusException.class)
                    .hasMessageContaining("Failed to retrieve company info after save");

            verify(csrdProjectRepository).save(testProject);
        }
    }

    @Nested
    @DisplayName("Get Company Info By Project ID Tests")
    class GetCompanyInfoByProjectIdTests {

        @Test
        @DisplayName("Should return company info for valid project")
        void whenGetCompanyInfoByProjectId_withValidProject_thenReturnsCompanyInfo() {
            // Arrange
            when(csrdProjectRepository.findByIdWithCompanyInfo(testProject.getId()))
                    .thenReturn(Optional.of(testProject));
            when(csrdDtoMapper.toCompanyInfoDTO(testCompanyInfo)).thenReturn(testCompanyInfoDTO);

            // Act
            CompanyInfoDTO result = csrdProjectService.getCompanyInfoByProjectId(testProject.getId());

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(testCompanyInfoDTO);

            verify(csrdProjectRepository).findByIdWithCompanyInfo(testProject.getId());
            verify(csrdDtoMapper).toCompanyInfoDTO(testCompanyInfo);
        }

        @Test
        @DisplayName("Should throw exception when project not found")
        void whenGetCompanyInfoByProjectId_withNonExistentProject_thenThrowsException() {
            // Arrange
            Long nonExistentId = 999L;
            when(csrdProjectRepository.findByIdWithCompanyInfo(nonExistentId))
                    .thenReturn(Optional.empty());

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.getCompanyInfoByProjectId(nonExistentId))
                    .isInstanceOf(ResponseStatusException.class)
                    .hasMessageContaining("CsrdProject not found with id: " + nonExistentId);

            verify(csrdProjectRepository).findByIdWithCompanyInfo(nonExistentId);
            verify(csrdDtoMapper, never()).toCompanyInfoDTO(any());
        }

        @Test
        @DisplayName("Should throw exception when company info is null")
        void whenGetCompanyInfoByProjectId_withNullCompanyInfo_thenThrowsException() {
            // Arrange
            CsrdProject projectWithoutCompanyInfo = new CsrdProject();
            projectWithoutCompanyInfo.setId(testProject.getId());
            projectWithoutCompanyInfo.setCompanyInfo(null);

            when(csrdProjectRepository.findByIdWithCompanyInfo(testProject.getId()))
                    .thenReturn(Optional.of(projectWithoutCompanyInfo));

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.getCompanyInfoByProjectId(testProject.getId()))
                    .isInstanceOf(ResponseStatusException.class)
                    .hasMessageContaining("CompanyInfo not found for CsrdProject with id: " + testProject.getId());

            verify(csrdProjectRepository).findByIdWithCompanyInfo(testProject.getId());
            verify(csrdDtoMapper, never()).toCompanyInfoDTO(any());
        }

        @Test
        @DisplayName("Should handle repository exceptions")
        void whenGetCompanyInfoByProjectId_withRepositoryException_thenPropagatesException() {
            // Arrange
            when(csrdProjectRepository.findByIdWithCompanyInfo(testProject.getId()))
                    .thenThrow(new DataAccessException("Database error") {});

            // Act & Assert
            assertThatThrownBy(() -> csrdProjectService.getCompanyInfoByProjectId(testProject.getId()))
                    .isInstanceOf(DataAccessException.class)
                    .hasMessage("Database error");
        }
    }

    // TODO: Add integration tests with actual database
    // TODO: Add tests for security checks when implemented
    // TODO: Add tests for transaction rollback on failures
    // TODO: Add tests for concurrent access scenarios
    // TODO: Add performance tests for large project datasets
    // TODO: Add tests for company info cascading operations
    // TODO: Add tests for audit trail generation
}