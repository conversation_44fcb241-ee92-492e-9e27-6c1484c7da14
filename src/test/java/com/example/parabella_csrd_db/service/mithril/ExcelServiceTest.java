package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.utilities.ExcelManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Excel Service Tests")
class ExcelServiceTest {

    @Mock
    private ExcelManager excelManager;

    @InjectMocks
    private ExcelService excelService;

    private List<Map<String, String>> testExcelData;
    private Map<String, List<Map<String, String>>> testAllExcelData;

    @BeforeEach
    void setUp() {
        // Setup test Excel data
        Map<String, String> row1 = new HashMap<>();
        row1.put("Topic", "Climate Change");
        row1.put("Code", "E1");
        row1.put("Description", "Climate-related disclosures");

        Map<String, String> row2 = new HashMap<>();
        row2.put("Topic", "Pollution");
        row2.put("Code", "E2");
        row2.put("Description", "Pollution-related disclosures");

        testExcelData = Arrays.asList(row1, row2);

        // Setup test data for all Excel files
        testAllExcelData = new HashMap<>();
        testAllExcelData.put("environmental_topics.xlsx", testExcelData);
        
        List<Map<String, String>> socialData = Arrays.asList(
            Map.of("Topic", "Workers", "Code", "S1", "Description", "Worker-related disclosures")
        );
        testAllExcelData.put("social_topics.xlsx", socialData);
    }

    @Nested
    @DisplayName("Get Excel Data Tests")
    class GetExcelDataTests {

        @Test
        @DisplayName("Should return Excel data for valid filename")
        void whenGetExcelData_withValidFileName_thenReturnsData() throws IOException {
            // Arrange
            String fileName = "environmental_topics.xlsx";
            when(excelManager.readExcel(fileName)).thenReturn(testExcelData);

            // Act
            List<Map<String, String>> result = excelService.getExcelData(fileName);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(2);
            assertThat(result.get(0).get("Topic")).isEqualTo("Climate Change");
            assertThat(result.get(0).get("Code")).isEqualTo("E1");
            assertThat(result.get(1).get("Topic")).isEqualTo("Pollution");
            assertThat(result.get(1).get("Code")).isEqualTo("E2");

            verify(excelManager).readExcel(fileName);
        }

        @Test
        @DisplayName("Should return empty list for empty Excel file")
        void whenGetExcelData_withEmptyFile_thenReturnsEmptyList() throws IOException {
            // Arrange
            String fileName = "empty_file.xlsx";
            when(excelManager.readExcel(fileName)).thenReturn(Collections.emptyList());

            // Act
            List<Map<String, String>> result = excelService.getExcelData(fileName);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEmpty();

            verify(excelManager).readExcel(fileName);
        }

        @Test
        @DisplayName("Should handle single row Excel file")
        void whenGetExcelData_withSingleRow_thenReturnsSingleRow() throws IOException {
            // Arrange
            String fileName = "single_row.xlsx";
            List<Map<String, String>> singleRowData = Arrays.asList(
                Map.of("Topic", "Water", "Code", "E3", "Description", "Water-related disclosures")
            );
            when(excelManager.readExcel(fileName)).thenReturn(singleRowData);

            // Act
            List<Map<String, String>> result = excelService.getExcelData(fileName);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).get("Topic")).isEqualTo("Water");
            assertThat(result.get(0).get("Code")).isEqualTo("E3");

            verify(excelManager).readExcel(fileName);
        }

        @Test
        @DisplayName("Should handle Excel files with missing columns")
        void whenGetExcelData_withMissingColumns_thenHandlesGracefully() throws IOException {
            // Arrange
            String fileName = "incomplete_data.xlsx";
            List<Map<String, String>> incompleteData = Arrays.asList(
                Map.of("Topic", "Biodiversity", "Code", "E4"), // Missing Description
                Map.of("Topic", "Circular Economy", "Description", "Resource use disclosures") // Missing Code
            );
            when(excelManager.readExcel(fileName)).thenReturn(incompleteData);

            // Act
            List<Map<String, String>> result = excelService.getExcelData(fileName);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(2);
            assertThat(result.get(0).get("Topic")).isEqualTo("Biodiversity");
            assertThat(result.get(0).get("Code")).isEqualTo("E4");
            assertThat(result.get(0).get("Description")).isNull();
            assertThat(result.get(1).get("Code")).isNull();

            verify(excelManager).readExcel(fileName);
        }

        @Test
        @DisplayName("Should propagate IOException from ExcelManager")
        void whenGetExcelData_withIOException_thenPropagatesException() throws IOException {
            // Arrange
            String fileName = "nonexistent_file.xlsx";
            when(excelManager.readExcel(fileName)).thenThrow(new IOException("File not found"));

            // Act & Assert
            assertThatThrownBy(() -> excelService.getExcelData(fileName))
                    .isInstanceOf(IOException.class)
                    .hasMessage("File not found");

            verify(excelManager).readExcel(fileName);
        }

        @Test
        @DisplayName("Should handle null filename gracefully")
        void whenGetExcelData_withNullFileName_thenPropagatesException() throws IOException {
            // Arrange
            when(excelManager.readExcel(null)).thenThrow(new IllegalArgumentException("Filename cannot be null"));

            // Act & Assert
            assertThatThrownBy(() -> excelService.getExcelData(null))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Filename cannot be null");

            verify(excelManager).readExcel(null);
        }

        @Test
        @DisplayName("Should handle empty filename")
        void whenGetExcelData_withEmptyFileName_thenPropagatesException() throws IOException {
            // Arrange
            String emptyFileName = "";
            when(excelManager.readExcel(emptyFileName)).thenThrow(new IllegalArgumentException("Filename cannot be empty"));

            // Act & Assert
            assertThatThrownBy(() -> excelService.getExcelData(emptyFileName))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessage("Filename cannot be empty");

            verify(excelManager).readExcel(emptyFileName);
        }

        @Test
        @DisplayName("Should handle Excel files with special characters in data")
        void whenGetExcelData_withSpecialCharacters_thenHandlesCorrectly() throws IOException {
            // Arrange
            String fileName = "special_chars.xlsx";
            List<Map<String, String>> specialCharData = Arrays.asList(
                Map.of("Topic", "Climate Change & Adaptation", "Code", "E1-α", "Description", "Émissions de CO₂"),
                Map.of("Topic", "Workers' Rights", "Code", "S1", "Description", "Safety & Health")
            );
            when(excelManager.readExcel(fileName)).thenReturn(specialCharData);

            // Act
            List<Map<String, String>> result = excelService.getExcelData(fileName);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(2);
            assertThat(result.get(0).get("Topic")).isEqualTo("Climate Change & Adaptation");
            assertThat(result.get(0).get("Code")).isEqualTo("E1-α");
            assertThat(result.get(0).get("Description")).isEqualTo("Émissions de CO₂");

            verify(excelManager).readExcel(fileName);
        }
    }

    @Nested
    @DisplayName("Get All Excel Data Tests")
    class GetAllExcelDataTests {

        @Test
        @DisplayName("Should return all Excel data from multiple files")
        void whenGetAllExcelData_withMultipleFiles_thenReturnsAllData() throws IOException {
            // Arrange
            when(excelManager.readAllExcelFiles()).thenReturn(testAllExcelData);

            // Act
            Map<String, List<Map<String, String>>> result = excelService.getAllExcelData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(2);
            assertThat(result).containsKey("environmental_topics.xlsx");
            assertThat(result).containsKey("social_topics.xlsx");

            List<Map<String, String>> environmentalData = result.get("environmental_topics.xlsx");
            assertThat(environmentalData).hasSize(2);
            assertThat(environmentalData.get(0).get("Topic")).isEqualTo("Climate Change");

            List<Map<String, String>> socialData = result.get("social_topics.xlsx");
            assertThat(socialData).hasSize(1);
            assertThat(socialData.get(0).get("Topic")).isEqualTo("Workers");

            verify(excelManager).readAllExcelFiles();
        }

        @Test
        @DisplayName("Should return empty map when no Excel files found")
        void whenGetAllExcelData_withNoFiles_thenReturnsEmptyMap() throws IOException {
            // Arrange
            when(excelManager.readAllExcelFiles()).thenReturn(Collections.emptyMap());

            // Act
            Map<String, List<Map<String, String>>> result = excelService.getAllExcelData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).isEmpty();

            verify(excelManager).readAllExcelFiles();
        }

        @Test
        @DisplayName("Should handle files with empty data")
        void whenGetAllExcelData_withEmptyFiles_thenHandlesGracefully() throws IOException {
            // Arrange
            Map<String, List<Map<String, String>>> dataWithEmptyFile = new HashMap<>();
            dataWithEmptyFile.put("environmental_topics.xlsx", testExcelData);
            dataWithEmptyFile.put("empty_file.xlsx", Collections.emptyList());
            
            when(excelManager.readAllExcelFiles()).thenReturn(dataWithEmptyFile);

            // Act
            Map<String, List<Map<String, String>>> result = excelService.getAllExcelData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(2);
            assertThat(result.get("environmental_topics.xlsx")).hasSize(2);
            assertThat(result.get("empty_file.xlsx")).isEmpty();

            verify(excelManager).readAllExcelFiles();
        }

        @Test
        @DisplayName("Should propagate IOException from ExcelManager")
        void whenGetAllExcelData_withIOException_thenPropagatesException() throws IOException {
            // Arrange
            when(excelManager.readAllExcelFiles()).thenThrow(new IOException("Directory not accessible"));

            // Act & Assert
            assertThatThrownBy(() -> excelService.getAllExcelData())
                    .isInstanceOf(IOException.class)
                    .hasMessage("Directory not accessible");

            verify(excelManager).readAllExcelFiles();
        }

        @Test
        @DisplayName("Should handle large number of Excel files")
        void whenGetAllExcelData_withManyFiles_thenHandlesEfficiently() throws IOException {
            // Arrange
            Map<String, List<Map<String, String>>> manyFiles = new HashMap<>();
            for (int i = 1; i <= 100; i++) {
                String fileName = "file_" + i + ".xlsx";
                List<Map<String, String>> fileData = Arrays.asList(
                    Map.of("Topic", "Topic " + i, "Code", "T" + i, "Description", "Description " + i)
                );
                manyFiles.put(fileName, fileData);
            }
            when(excelManager.readAllExcelFiles()).thenReturn(manyFiles);

            // Act
            long startTime = System.currentTimeMillis();
            Map<String, List<Map<String, String>>> result = excelService.getAllExcelData();
            long duration = System.currentTimeMillis() - startTime;

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(100);
            assertThat(duration).isLessThan(1000); // Should complete within 1 second
            
            verify(excelManager).readAllExcelFiles();
        }

        @Test
        @DisplayName("Should handle files with duplicate names")
        void whenGetAllExcelData_withDuplicateFileNames_thenHandlesAsPerExcelManager() throws IOException {
            // Arrange - This behavior depends on ExcelManager implementation
            Map<String, List<Map<String, String>>> dataWithDuplicates = new HashMap<>();
            dataWithDuplicates.put("topics.xlsx", testExcelData); // Later entry might overwrite
            
            when(excelManager.readAllExcelFiles()).thenReturn(dataWithDuplicates);

            // Act
            Map<String, List<Map<String, String>>> result = excelService.getAllExcelData();

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).containsKey("topics.xlsx");
            
            verify(excelManager).readAllExcelFiles();
        }
    }

    @Nested
    @DisplayName("Service Integration Tests")
    class ServiceIntegrationTests {

        @Test
        @DisplayName("Should maintain consistent behavior between single and batch operations")
        void whenComparingSingleVsBatchOperations_thenBehaviorIsConsistent() throws IOException {
            // Arrange
            String fileName = "environmental_topics.xlsx";
            when(excelManager.readExcel(fileName)).thenReturn(testExcelData);
            when(excelManager.readAllExcelFiles()).thenReturn(testAllExcelData);

            // Act
            List<Map<String, String>> singleFileResult = excelService.getExcelData(fileName);
            Map<String, List<Map<String, String>>> allFilesResult = excelService.getAllExcelData();

            // Assert
            assertThat(singleFileResult).isEqualTo(allFilesResult.get(fileName));
            
            verify(excelManager).readExcel(fileName);
            verify(excelManager).readAllExcelFiles();
        }

        @Test
        @DisplayName("Should handle concurrent access gracefully")
        void whenConcurrentAccess_thenHandlesGracefully() throws IOException {
            // Arrange
            String fileName = "concurrent_test.xlsx";
            when(excelManager.readExcel(fileName)).thenReturn(testExcelData);

            // Act - Simulate concurrent calls
            List<Map<String, String>> result1 = excelService.getExcelData(fileName);
            List<Map<String, String>> result2 = excelService.getExcelData(fileName);

            // Assert
            assertThat(result1).isEqualTo(result2);
            verify(excelManager, times(2)).readExcel(fileName);
        }
    }

    // TODO: Add integration tests with actual Excel files
    // TODO: Add tests for different Excel formats (.xls vs .xlsx)
    // TODO: Add tests for Excel files with formulas and calculated values
    // TODO: Add tests for Excel files with multiple sheets
    // TODO: Add performance benchmarks for large Excel files
    // TODO: Add tests for memory usage optimization
    // TODO: Add tests for file locking scenarios
    // TODO: Add tests for Excel files with merged cells
}