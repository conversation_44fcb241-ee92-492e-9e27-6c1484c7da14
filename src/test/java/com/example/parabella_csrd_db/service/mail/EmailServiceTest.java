package com.example.parabella_csrd_db.service.mail;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Email Service Tests")
class EmailServiceTest {

    @Mock
    private JavaMailSender emailSender;

    @Mock
    private MimeMessage mimeMessage;

    @InjectMocks
    private EmailService emailService;

    private final String testEmail = "<EMAIL>";
    private final String testSubject = "Test Subject";
    private final String testContent = "Test content";
    private final String testToken = "test-token-123";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(emailService, "frontendUrl", "http://localhost:5173");
        ReflectionTestUtils.setField(emailService, "tokenExpiryHours", 24);
        
        when(emailSender.createMimeMessage()).thenReturn(mimeMessage);
    }

    @Nested
    @DisplayName("Plain/HTML Email Tests")
    class PlainHtmlEmailTests {

        @Test
        @DisplayName("Should send plain text email successfully")
        void whenSendPlainOrHtmlEmail_withPlainText_thenSendsEmail() throws MessagingException {
            // Arrange
            doNothing().when(emailSender).send(any(MimeMessage.class));

            // Act
            emailService.sendPlainOrHtmlEmail(testEmail, testSubject, testContent);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should send HTML email successfully")
        void whenSendPlainOrHtmlEmail_withHtmlContent_thenSendsEmail() throws MessagingException {
            // Arrange
            String htmlContent = "<h1>Test HTML Content</h1><p>This is a test email.</p>";
            doNothing().when(emailSender).send(any(MimeMessage.class));

            // Act
            emailService.sendPlainOrHtmlEmail(testEmail, testSubject, htmlContent);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should handle special characters in email content")
        void whenSendPlainOrHtmlEmail_withSpecialCharacters_thenSendsEmail() throws MessagingException {
            // Arrange
            String specialContent = "Content with special chars: àáâãäåæçèéêë 中文 русский 🚀";
            doNothing().when(emailSender).send(any(MimeMessage.class));

            // Act
            emailService.sendPlainOrHtmlEmail(testEmail, testSubject, specialContent);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should throw runtime exception when MessagingException occurs")
        void whenSendPlainOrHtmlEmail_withMessagingException_thenThrowsRuntimeException() throws MessagingException {
            // Arrange
            doThrow(new MessagingException("SMTP error")).when(emailSender).send(any(MimeMessage.class));

            // Act & Assert
            assertThatThrownBy(() -> emailService.sendPlainOrHtmlEmail(testEmail, testSubject, testContent))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessage("Failed to send email")
                    .hasCauseInstanceOf(MessagingException.class);

            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should handle empty email content")
        void whenSendPlainOrHtmlEmail_withEmptyContent_thenSendsEmail() throws MessagingException {
            // Arrange
            doNothing().when(emailSender).send(any(MimeMessage.class));

            // Act
            emailService.sendPlainOrHtmlEmail(testEmail, testSubject, "");

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should handle null values gracefully")
        void whenSendPlainOrHtmlEmail_withNullValues_thenHandlesGracefully() throws MessagingException {
            // Arrange
            doNothing().when(emailSender).send(any(MimeMessage.class));

            // Act & Assert - Should not throw exception
            emailService.sendPlainOrHtmlEmail(testEmail, null, null);

            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }
    }

    @Nested
    @DisplayName("HTML Email with Token Tests")
    class HtmlEmailWithTokenTests {

        @Test
        @DisplayName("Should send HTML email with stakeholder details successfully")
        void whenSendHtmlEmail_withValidData_thenSendsEmail() throws MessagingException {
            // Arrange
            String stakeholderName = "John Doe";
            doNothing().when(emailSender).send(any(MimeMessage.class));

            // Mock template loading by setting up a spy
            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "<html><body>Hello {{FNAME}} {{LNAME}}, click <a href=\"{{LINK}}\">here</a></body></html>";
            doReturn(mockTemplate).when(spyEmailService).loadStakeholderHtmlTemplate();

            // Act
            spyEmailService.sendHtmlEmail(testEmail, testToken, stakeholderName);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should handle single name properly")
        void whenSendHtmlEmail_withSingleName_thenProcessesCorrectly() throws MessagingException {
            // Arrange
            String singleName = "John";
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Hello {{FNAME}} {{LNAME}}";
            doReturn(mockTemplate).when(spyEmailService).loadStakeholderHtmlTemplate();

            // Act
            spyEmailService.sendHtmlEmail(testEmail, testToken, singleName);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should handle empty name gracefully")
        void whenSendHtmlEmail_withEmptyName_thenHandlesGracefully() throws MessagingException {
            // Arrange
            String emptyName = "";
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Hello {{FNAME}} {{LNAME}}";
            doReturn(mockTemplate).when(spyEmailService).loadStakeholderHtmlTemplate();

            // Act
            spyEmailService.sendHtmlEmail(testEmail, testToken, emptyName);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should handle null name gracefully")
        void whenSendHtmlEmail_withNullName_thenHandlesGracefully() throws MessagingException {
            // Arrange
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Hello {{FNAME}} {{LNAME}}";
            doReturn(mockTemplate).when(spyEmailService).loadStakeholderHtmlTemplate();

            // Act
            spyEmailService.sendHtmlEmail(testEmail, testToken, null);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should construct correct stakeholder URL")
        void whenSendHtmlEmail_thenConstructsCorrectUrl() throws MessagingException {
            // Arrange
            String stakeholderName = "Jane Smith";
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Click here: {{LINK}}";
            doReturn(mockTemplate).when(spyEmailService).loadStakeholderHtmlTemplate();

            // Act
            spyEmailService.sendHtmlEmail(testEmail, testToken, stakeholderName);

            // Assert
            // URL should be: http://localhost:5173/stakeholder/main/test-token-123
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should throw MessagingException when email sending fails")
        void whenSendHtmlEmail_withEmailSendingFailure_thenThrowsMessagingException() throws MessagingException {
            // Arrange
            String stakeholderName = "John Doe";
            doThrow(new MessagingException("SMTP error")).when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Hello {{FNAME}} {{LNAME}}";
            doReturn(mockTemplate).when(spyEmailService).loadStakeholderHtmlTemplate();

            // Act & Assert
            assertThatThrownBy(() -> spyEmailService.sendHtmlEmail(testEmail, testToken, stakeholderName))
                    .isInstanceOf(MessagingException.class)
                    .hasMessage("SMTP error");
        }

        // TODO: Add test for template loading failure
        // TODO: Add test for template replacement verification
    }

    @Nested
    @DisplayName("User Invitation Email Tests")
    class UserInvitationEmailTests {

        @Test
        @DisplayName("Should send user invitation email successfully")
        void whenSendUserInvitationEmail_withValidData_thenSendsEmail() throws MessagingException {
            // Arrange
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "<html><body>Welcome! <a href=\"{{LINK}}\">Set your password</a></body></html>";
            doReturn(mockTemplate).when(spyEmailService).loadHtmlTemplate("emailTemplates/user_invitation_template.html");

            // Act
            spyEmailService.sendUserInvitationEmail(testEmail, testToken);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should construct correct invitation URL")
        void whenSendUserInvitationEmail_thenConstructsCorrectUrl() throws MessagingException {
            // Arrange
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Set password: {{LINK}}";
            doReturn(mockTemplate).when(spyEmailService).loadHtmlTemplate("emailTemplates/user_invitation_template.html");

            // Act
            spyEmailService.sendUserInvitationEmail(testEmail, testToken);

            // Assert
            // URL should be: http://localhost:5173/pages/authentication/setpassword/test-token-123
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should throw MessagingException when email sending fails")
        void whenSendUserInvitationEmail_withEmailSendingFailure_thenThrowsMessagingException() throws MessagingException {
            // Arrange
            doThrow(new MessagingException("SMTP configuration error")).when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Welcome! {{LINK}}";
            doReturn(mockTemplate).when(spyEmailService).loadHtmlTemplate("emailTemplates/user_invitation_template.html");

            // Act & Assert
            assertThatThrownBy(() -> spyEmailService.sendUserInvitationEmail(testEmail, testToken))
                    .isInstanceOf(MessagingException.class)
                    .hasMessage("SMTP configuration error");
        }

        @Test
        @DisplayName("Should handle empty token gracefully")
        void whenSendUserInvitationEmail_withEmptyToken_thenSendsEmail() throws MessagingException {
            // Arrange
            String emptyToken = "";
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Welcome! {{LINK}}";
            doReturn(mockTemplate).when(spyEmailService).loadHtmlTemplate("emailTemplates/user_invitation_template.html");

            // Act
            spyEmailService.sendUserInvitationEmail(testEmail, emptyToken);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        @Test
        @DisplayName("Should handle null token gracefully")
        void whenSendUserInvitationEmail_withNullToken_thenSendsEmail() throws MessagingException {
            // Arrange
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "Welcome! {{LINK}}";
            doReturn(mockTemplate).when(spyEmailService).loadHtmlTemplate("emailTemplates/user_invitation_template.html");

            // Act
            spyEmailService.sendUserInvitationEmail(testEmail, null);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
        }

        // TODO: Add test for template loading failure
        // TODO: Add test for different frontend URL configurations
    }

    @Nested
    @DisplayName("Template Loading Tests") 
    class TemplateLoadingTests {

        @Test
        @DisplayName("Should throw runtime exception when template not found")
        void whenLoadHtmlTemplate_withNonExistentTemplate_thenThrowsRuntimeException() {
            // Arrange
            EmailService realEmailService = new EmailService();

            // Act & Assert
            assertThatThrownBy(() -> realEmailService.sendUserInvitationEmail(testEmail, testToken))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("Failed to load email template");
        }

        // TODO: Add test for successful template loading
        // TODO: Add test for template with invalid encoding
        // TODO: Add test for template content validation
    }

    @Nested
    @DisplayName("Configuration Tests")
    class ConfigurationTests {

        @Test
        @DisplayName("Should use configured frontend URL")
        void whenSendingEmails_thenUsesConfiguredFrontendUrl() throws MessagingException {
            // Arrange
            String customUrl = "https://custom.domain.com";
            ReflectionTestUtils.setField(emailService, "frontendUrl", customUrl);
            doNothing().when(emailSender).send(any(MimeMessage.class));

            EmailService spyEmailService = spy(emailService);
            String mockTemplate = "URL: {{LINK}}";
            doReturn(mockTemplate).when(spyEmailService).loadHtmlTemplate(any());

            // Act
            spyEmailService.sendUserInvitationEmail(testEmail, testToken);

            // Assert
            verify(emailSender).createMimeMessage();
            verify(emailSender).send(mimeMessage);
            // The URL should contain the custom domain
        }

        @Test
        @DisplayName("Should use configured token expiry hours")
        void whenEmailServiceConfigured_thenUsesConfiguredExpiryHours() {
            // Arrange & Act
            ReflectionTestUtils.setField(emailService, "tokenExpiryHours", 48);

            // Assert
            Integer configuredHours = (Integer) ReflectionTestUtils.getField(emailService, "tokenExpiryHours");
            assertThat(configuredHours).isEqualTo(48);
        }

        @Test
        @DisplayName("Should use default frontend URL when not configured")
        void whenFrontendUrlNotConfigured_thenUsesDefault() {
            // Arrange
            EmailService defaultEmailService = new EmailService();

            // Act & Assert
            String frontendUrl = (String) ReflectionTestUtils.getField(defaultEmailService, "frontendUrl");
            assertThat(frontendUrl).isEqualTo("http://localhost:5173");
        }
    }

    // TODO: Add integration tests with actual JavaMailSender
    // TODO: Add tests for email template validation
    // TODO: Add tests for email delivery confirmation
    // TODO: Add tests for bulk email sending
    // TODO: Add tests for email queue management
    // TODO: Add performance tests for high-volume email sending
}