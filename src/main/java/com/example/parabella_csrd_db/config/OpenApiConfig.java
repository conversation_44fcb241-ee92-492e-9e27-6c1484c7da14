package com.example.parabella_csrd_db.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI configuration for Parabella CSRD FAQ Tool API documentation.
 * 
 * This configuration sets up Swagger UI and provides comprehensive API documentation
 * including authentication schemes, server configurations, and API metadata.
 * 
 * Swagger UI is available at: /swagger-ui.html
 * OpenAPI JSON is available at: /v3/api-docs
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8080}")
    private String serverPort;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server().url("http://localhost:" + serverPort).description("Local Development Server"),
                        new Server().url("https://staging.parabella.com").description("Staging Server"),
                        new Server().url("https://api.parabella.com").description("Production Server")
                ))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("bearerAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("JWT token obtained from /api/auth/signin endpoint. Include 2FA verification if enabled.")));
    }

    private Info apiInfo() {
        return new Info()
                .title("Parabella CSRD FAQ Tool API")
                .description("""
                        # Corporate Sustainability Reporting Directive (CSRD) Compliance Platform
                        
                        The Parabella CSRD FAQ Tool provides a comprehensive REST API for managing Corporate Sustainability 
                        Reporting Directive compliance, including:
                        
                        ## Core Modules
                        
                        ### 🎯 **DMA Module (Mithril)**
                        - **Double Materiality Assessment**: Stakeholder-driven materiality analysis
                        - **ESRS Topic Management**: European Sustainability Reporting Standards topic selection
                        - **IRO Assessment**: Impact, Risk, and Opportunity evaluation
                        - **Value Chain Analysis**: Supply chain mapping and analysis
                        
                        ### 📊 **CSRD Module (Elessar)**
                        - **CSRD Compliance**: Complete CSRD reporting workflow
                        - **AI-powered Analysis**: Document processing and data extraction
                        - **Dynamic Forms**: Contextual form generation for CSRD datapoints
                        - **Report Generation**: Comprehensive sustainability reporting
                        
                        ### 🌱 **PCF Module (Vilya)**
                        - **Product Carbon Footprint**: Lifecycle carbon assessment
                        - **Emission Tracking**: Phase-specific emission calculations
                        - **Carbon Analytics**: Environmental impact analysis
                        
                        ## Security & Authentication
                        - **JWT Authentication**: Secure token-based authentication
                        - **2FA Support**: TOTP-based two-factor authentication
                        - **Role-based Access**: Granular permission system (ADMIN, USER, MANAGER)
                        
                        ## AI Integration
                        - **OpenAI Integration**: GPT-4o-mini for intelligent data processing
                        - **Vector Search**: Semantic search across CSRD content
                        - **Auto-completion**: AI-powered form field suggestions
                        
                        ## Getting Started
                        1. **Authentication**: Obtain JWT token via `/api/auth/signin`
                        2. **Authorization**: Include `Bearer <token>` in Authorization header
                        3. **2FA**: Complete 2FA verification if enabled for account
                        4. **API Usage**: All authenticated endpoints require valid JWT token
                        
                        For technical support, contact the development team.
                        """)
                .version("1.0.0")
                .contact(new Contact()
                        .name("Parabella Development Team")
                        .email("<EMAIL>")
                        .url("https://www.parabella.com"))
                .license(new License()
                        .name("Proprietary")
                        .url("https://www.parabella.com/license"));
    }
}