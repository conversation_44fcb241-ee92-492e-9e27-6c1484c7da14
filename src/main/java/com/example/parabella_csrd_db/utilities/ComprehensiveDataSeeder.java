//package com.example.parabella_csrd_db.utilities;
//
//import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
//import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
//import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
//import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
//import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
//import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.*;
//import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
//import com.example.parabella_csrd_db.dto.mithril.seeder.DataSeedDto;
//import com.example.parabella_csrd_db.dto.mithril.seeder.EsrsTopicDto;
//import com.example.parabella_csrd_db.dto.mithril.seeder.IroEvaluationSeedDto;
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.text.similarity.LevenshteinDistance;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.context.annotation.Profile;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.InputStream;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.time.LocalDateTime;
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Component
//@Slf4j
//@RequiredArgsConstructor
//@Profile("!test")
//public class ComprehensiveDataSeeder implements CommandLineRunner {
//
//    // --- Repositories, Services, and Configuration ---
//    private final ObjectMapper objectMapper;
//    private final UserRepository userRepository;
//    private final RoleRepository roleRepository;
//    private final ProjectRepository projectRepository;
//    private final CompanyRepository companyRepository;
//    private final StakeholderRepository stakeholderRepository;
//    private final EsrsTopicsRepository esrsTopicRepository;
//    private final EsrsTopicSelectionRepository esrsTopicSelectionRepository;
//    private final IroRepository iroRepository;
//    private final ValueChainObjectRepository valueChainObjectRepository;
//    private final PasswordEncoder passwordEncoder;
//
//    // --- Seeder Configuration Constants ---
//    private static final String JSON_DATA_FILE_PATH = "json/gd_json.json";
//    private static final String PROJECT_NAME = "G+D DMA 2024";
//
//    private static final Map<String, String> USER_FULL_NAMES = Map.of(
//            "<EMAIL>", "Maike Reichert",
//            "<EMAIL>", "Martin Hinderer",
//            "<EMAIL>", "Parabella Admin"
//    );
//
//    // NEW: Map to store secure passwords for each user.
//    private static final Map<String, String> USER_PASSWORDS = Map.of(
//            "<EMAIL>", "G+D_MaikeR_2024!",
//            "<EMAIL>", "G+D_MartinH_2024!",
//            "<EMAIL>", "Parabella_Admin_2024!"
//    );
//
//    // --- Fuzzy Matching Threshold ---
//    private static final int LEVENSHTEIN_DISTANCE_THRESHOLD = 3;
//
//    // --- Calculation Constants ---
//    private static final int SCALE = 2;
//    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;
//
//    // --- G+D Company and Value Chain Constants ---
//    private static final String COMPANY_NAME = "Giesecke+Devrient GmbH";
//    private static final String COMPANY_ADDRESS = "Prinzregentenstraße 161, 81677 Munich, Germany";
//    private static final String COMPANY_VAT = "*********";
//    private static final String COMPANY_EMPLOYEES = "14435";
//    private static final Double COMPANY_REVENUE = **********.0;
//    private static final String COMPANY_INDUSTRY = "Digital Security, Financial Platform, Currency Technology";
//    private static final List<String> VALUE_CHAIN_DESCRIPTIONS = List.of(
//            "Extraction and growth of raw materials",
//            "Supplier manufacturing process, retailer processes",
//            "BN (banknotes): Production of paper, security features and banknotes",
//            "CT (currency technology): Design and Assembly of cash cycle systems, Services & Consulting",
//            "MS (mobile security): Design & Production of SIM cards, Production of passport documents, Personalization activities",
//            "EP (ePayments): Design and Production of payment cards, Personalization activities, Service Center activities,",
//            "Veridos: Production of passports, development of identity solutions",
//            "Secunet, Netcetera, A52: Software development, Services & Consulting activities, R&D, etc.",
//            "Customer processes (distribution of banknotes, Operation of cash centers, provision of infrastructure for the use of products, etc.)",
//            "Product use",
//            "Disposal / recycling after use"
//    );
//
//    @Override
//    @Transactional
//    public void run(String... args) throws Exception {
//        // Fetch ADMIN role first. It MUST exist in the database.
//        Role adminRole = roleRepository.findByName("Admin")
//                .orElseThrow(() -> new IllegalStateException("Seeding failed: 'Admin' role not found in the database. Please pre-populate roles."));
//
//        // Maike Reichert is the project owner
//        User projectOwner = findOrCreateAdminUser("Maike Reichert", "<EMAIL>", USER_PASSWORDS.get("<EMAIL>"), adminRole);
//
//        // Check if the seeder should run
//        log.info("Checking if seeding is required for project '{}' owned by '{}'...", PROJECT_NAME, projectOwner.getUsername());
//        Optional<Project> projectOpt = projectRepository.findByProjectNameAndUserId(PROJECT_NAME, projectOwner.getId());
//        if (projectOpt.isPresent()) {
//            Optional<Company> companyOpt = companyRepository.findByProjectId(projectOpt.get().getId());
//            if (companyOpt.isPresent() && COMPANY_NAME.equals(companyOpt.get().getCompanyName())) {
//                log.info("✅ Seeding skipped: The company '{}' already exists for this project.", COMPANY_NAME);
//                return;
//            }
//        }
//        log.info("🚀 Condition met. Starting comprehensive data seeding for project '{}'. This will create or overwrite data.", PROJECT_NAME);
//
//        // Create/Find the other required users
//        User martinHinderer = findOrCreateAdminUser("Martin Hinderer", "<EMAIL>", USER_PASSWORDS.get("<EMAIL>"), adminRole);
//        User parabellaAdmin = findOrCreateAdminUser("Parabella Admin", "<EMAIL>", USER_PASSWORDS.get("<EMAIL>"), adminRole);
//
//        // All three users will have access to the project
//        List<User> usersWithAccess = List.of(projectOwner, martinHinderer, parabellaAdmin);
//
//        // Load JSON data
//        List<DataSeedDto> data = loadJsonData();
//        if (data.isEmpty()) {
//            log.warn("No data found in JSON file [{}]. Aborting seed.", JSON_DATA_FILE_PATH);
//            return;
//        }
//
//        List<EsrsTopic> esrsTopicsFromDb = esrsTopicRepository.findAll();
//        if (esrsTopicsFromDb.isEmpty()) {
//            log.error("Seeding failed: The 'esrs_topic' table is empty. It must be pre-populated.");
//            return;
//        }
//
//        // Create project and company, granting access to all three users
//        Project project = findAndOverwriteProject(projectOwner, usersWithAccess);
//        Company company = findAndOverwriteCompany(project);
//        project.setCompanyId(company.getId());
//        projectRepository.save(project);
//
//        clearExistingProjectData(company);
//
//        // Setup all three users as responsible stakeholders
//        Map<User, Stakeholder> responsibleStakeholdersMap = setupResponsibleStakeholders(company, project, usersWithAccess);
//
//        // IRO data is still assigned to Maike Reichert's stakeholder profile (who is also the project owner)
//        Stakeholder primaryIroHolder = responsibleStakeholdersMap.get(projectOwner);
//
//        if (primaryIroHolder == null) {
//            throw new IllegalStateException("Seeding failed: Could not find the created stakeholder for the project owner.");
//        }
//
//        seedIroData(data, company, primaryIroHolder, esrsTopicsFromDb);
//
//        log.info("✅ Comprehensive data seeding and overwrite completed for Project '{}'.", PROJECT_NAME);
//    }
//
//    /**
//     * Creates or finds a user, ensuring they have the provided Admin Role entity.
//     * Now accepts a password to use for new user creation.
//     */
//    private User findOrCreateAdminUser(String username, String email, String password, Role adminRole) {
//        // This call returns a User object or null, NOT an Optional.
//        User existingUser = userRepository.findByEmail(email);
//
//        if (existingUser != null) {
//            // User exists, check the role.
//            if (!adminRole.equals(existingUser.getRole())) {
//                log.warn("User '{}' found but does not have ADMIN role. Updating role.", username);
//                existingUser.setRole(adminRole);
//                return userRepository.save(existingUser);
//            }
//            return existingUser;
//        } else {
//            // User does not exist, create a new one with the provided password.
//            log.info("Creating new ADMIN user: {}", username);
//            User newUser = new User();
//            newUser.setUsername(username);
//            newUser.setEmail(email);
//            newUser.setPassword(passwordEncoder.encode(password)); // Use the secure password
//            newUser.setRole(adminRole);
//            newUser.setEnabled(true);
//            return userRepository.save(newUser);
//        }
//    }
//
//    private Project findAndOverwriteProject(User owner, List<User> usersWithAccess) {
//        final String description = "Automatically generated project from the 2024 DMA assessment data seeder.";
//        final String projectType = "company";
//
//        Project project = projectRepository.findByProjectNameAndUserId(PROJECT_NAME, owner.getId())
//                .map(existingProject -> {
//                    log.info("Found existing project '{}'. Overwriting its details.", PROJECT_NAME);
//                    existingProject.setProjectDescription(description);
//                    existingProject.setProjectType(projectType);
//                    return existingProject;
//                })
//                .orElseGet(() -> {
//                    log.info("Project '{}' not found for owner '{}'. Creating new project.", PROJECT_NAME, owner.getUsername());
//                    Project newProject = new Project();
//                    newProject.setUser(owner);
//                    newProject.setProjectName(PROJECT_NAME);
//                    newProject.setProjectDescription(description);
//                    newProject.setProjectType(projectType);
//                    newProject.setCreatedAt(LocalDateTime.now());
//                    return newProject;
//                });
//
//        log.info("Setting project access for: {}", usersWithAccess.stream().map(User::getUsername).collect(Collectors.toList()));
//        project.getAccessibleByUsers().clear();
//        project.getAccessibleByUsers().addAll(usersWithAccess);
//
//        return projectRepository.save(project);
//    }
//
//    private Company findAndOverwriteCompany(Project project) {
//        Company company = companyRepository.findByProjectId(project.getId())
//                .map(existingCompany -> {
//                    log.info("Found existing company '{}'. Overwriting its details.", existingCompany.getCompanyName());
//                    return existingCompany;
//                })
//                .orElseGet(() -> {
//                    log.info("Company for project '{}' not found. Creating new company '{}'.", project.getProjectName(), COMPANY_NAME);
//                    Company newCompany = new Company();
//                    newCompany.setProject(project);
//                    return newCompany;
//                });
//
//        company.setCompanyName(COMPANY_NAME);
//        company.setIndustry(COMPANY_INDUSTRY);
//        company.setNumEmployees(COMPANY_EMPLOYEES);
//        company.setIsSubCompany(false);
//        company.setAddress(COMPANY_ADDRESS);
//        company.setVat(COMPANY_VAT);
//        company.setRevenues(COMPANY_REVENUE);
//
//        return companyRepository.save(company);
//    }
//
//    private Map<User, Stakeholder> setupResponsibleStakeholders(Company company, Project project, List<User> users) {
//        Map<User, Stakeholder> stakeholderMap = new HashMap<>();
//
//
//        List<ValueChainObject> valueChainObjects = VALUE_CHAIN_DESCRIPTIONS.stream()
//                .map(desc -> {
//                    ValueChainObject vco = new ValueChainObject();
//                    vco.setName(desc);
//                    vco.setIndustry(company.getIndustry());
//                    vco.setCompany(company);
//                    return valueChainObjectRepository.save(vco);
//                })
//                .collect(Collectors.toList());
//
//        for (User user : users) {
//            String stakeholderName = USER_FULL_NAMES.get(user.getEmail());
//            if (stakeholderName == null) {
//                log.error("Could not find full name for user email '{}'. Using username as fallback.", user.getEmail());
//                stakeholderName = user.getUsername();
//            }
//
//
//
//            log.info("Creating responsible stakeholder '{}' for project '{}'", stakeholderName, project.getProjectName());
//
//            Stakeholder stakeholder = new Stakeholder();
//            stakeholder.setName(stakeholderName);
//            stakeholder.setCompany(company);
//            stakeholder.setProject(project);
//            stakeholder.setEmail(user.getEmail());
//            stakeholder.setRole("Responsible Person");
//            stakeholder.setStakeholderType("internal");
//            stakeholder.setCompletedDatapoints(0);
//            stakeholder.setTotalDatapoints(0);
//            stakeholder.setToken(UUID.randomUUID().toString());
//            stakeholder.setStatus(StakeholderStatus.IN_PROGRESS);
//            stakeholder.setIs_responsible(true);
//            stakeholder.setUser(user);
//
//            if(stakeholderName.equals("Maike Reichert")) {
//                stakeholder.setValueChainObjects(new ArrayList<>(valueChainObjects));
//            }
//
//            stakeholder.setEsrsTopics(new ArrayList<>());
//
//            Stakeholder savedStakeholder = stakeholderRepository.save(stakeholder);
//            stakeholderMap.put(user, savedStakeholder);
//        }
//        return stakeholderMap;
//    }
//
//    private void clearExistingProjectData(Company company) {
//        log.warn("Clearing existing data for project ID: {} before seeding.", company.getProject().getId());
//        iroRepository.deleteAllByCompanyId(company.getId());
//        esrsTopicSelectionRepository.deleteAllByCompanyId(company.getId());
//        stakeholderRepository.deleteAllByProjectId(company.getProject().getId());
//        valueChainObjectRepository.deleteAllByCompanyId(company.getId());
//        log.info("Data clearing complete.");
//    }
//
//    // --- The methods below this line are unchanged ---
//
//    private void seedIroData(List<DataSeedDto> data, Company company, Stakeholder responsibleStakeholder, List<EsrsTopic> esrsTopicsFromDb) {
//        Map<String, EsrsTopicSelection> selectionCache = new HashMap<>();
//        Map<Long, Set<String>> iroNameCache = new HashMap<>();
//        log.info("Seeding IRO data and assigning to stakeholder: {}", responsibleStakeholder.getName());
//        for (DataSeedDto row : data) {
//            EsrsTopic esrsTopic = findBestTopicMatch(row.getEsrsTopic(), esrsTopicsFromDb);
//            if (esrsTopic == null) {
//                log.warn("ESRS topic data is missing in a row from JSON. Skipping row.");
//                continue;
//            }
//            String selectionKey = String.format("%d-%d-%d", company.getId(), esrsTopic.getId(), responsibleStakeholder.getId());
//            EsrsTopicSelection selection = selectionCache.get(selectionKey);
//            if (selection == null) {
//                log.trace("Creating new EsrsTopicSelection for key: {}", selectionKey);
//                selection = new EsrsTopicSelection();
//                selection.setCompany(company);
//                selection.setEsrsTopic(esrsTopic);
//                selection.setStakeholder(responsibleStakeholder);
//                selection.setRelevant(false);
//                selection = esrsTopicSelectionRepository.save(selection);
//                selectionCache.put(selectionKey, selection);
//            }
//            boolean isIroRelevant = row.getIro() != null && row.getIro().getName() != null && !row.getIro().getName().isBlank();
//            if (isIroRelevant) {
//                if (!selection.getRelevant()) {
//                    selection.setRelevant(true);
//                }
//                Set<String> existingIroNames = iroNameCache.computeIfAbsent(selection.getId(), k -> new HashSet<>());
//                if (existingIroNames.contains(row.getIro().getName())) {
//                    log.debug("Duplicate IRO '{}' for selection ID {}. Skipping.", row.getIro().getName(), selection.getId());
//                    continue;
//                }
//                log.trace("Creating new IRO: '{}' and linking to selection ID {}", row.getIro().getName(), selection.getId());
//                Iro iro = new Iro();
//                iro.setCompany(company);
//                iro.setStakeholder(responsibleStakeholder);
//                iro.setEsrsTopicSelection(selection);
//                iro.setName(row.getIro().getName());
//                iro.setIroType(row.getIro().getIroType().toLowerCase());
//                IroEvaluation evaluation = mapIroEvaluation(row.getIroEvaluation(), company);
//                String finalRelevance = performAndSetCalculations(iro, evaluation);
//                iro.setIroEvaluation(evaluation);
//                evaluation.setIro(iro);
//                iroRepository.save(iro);
//                existingIroNames.add(row.getIro().getName());
//                selection.setFinalRelevance(finalRelevance);
//                esrsTopicSelectionRepository.save(selection);
//            } else {
//                if (!selection.getRelevant() && row.getIroEvaluation() != null && row.getIroEvaluation().getDescription() != null) {
//                    selection.setReasonIrrelevance(row.getIroEvaluation().getDescription());
//                    esrsTopicSelectionRepository.save(selection);
//                }
//            }
//        }
//    }
//
//    private String performAndSetCalculations(Iro iro, IroEvaluation evaluation) {
//        if (iro == null || evaluation == null) {
//            return "Not Relevant";
//        }
//        BigDecimal finalScore;
//        String finalRelevance;
//        BigDecimal threshold = new BigDecimal("2.00");
//        evaluation.setImpactMaterialityPotentialImpact("0.00");
//        evaluation.setFinancialMaterialityPotentialImpact("0.00");
//        if ("impact".equalsIgnoreCase(iro.getIroType())) {
//            BigDecimal scale = Optional.ofNullable(evaluation.getScale()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE);
//            BigDecimal scope = Optional.ofNullable(evaluation.getScope()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE);
//            boolean isNegativeImpact = "negative".equalsIgnoreCase(evaluation.getPositiveNegativeImpact());
//            BigDecimal irreversibility = isNegativeImpact ? Optional.ofNullable(evaluation.getIrreversibility()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE) : BigDecimal.ZERO;
//            BigDecimal baseScore;
//            if (isNegativeImpact) {
//                baseScore = scale.add(scope).add(irreversibility).divide(new BigDecimal("3"), SCALE, ROUNDING_MODE);
//            } else {
//                baseScore = scale.add(scope).divide(new BigDecimal("2"), SCALE, ROUNDING_MODE);
//            }
//            if ("potential".equalsIgnoreCase(evaluation.getActualPotentialImpact())) {
//                BigDecimal probability = Optional.ofNullable(evaluation.getProbability()).map(BigDecimal::valueOf).orElse(new BigDecimal("0.2"));
//                finalScore = baseScore.multiply(probability).setScale(SCALE, ROUNDING_MODE);
//            } else {
//                finalScore = baseScore;
//            }
//            finalRelevance = finalScore.compareTo(threshold) >= 0 ? "Relevant" : "Not Relevant";
//            if ("yes".equalsIgnoreCase(evaluation.getHumanRightsImpact())) {
//                finalScore = new BigDecimal("5.00");
//                finalRelevance = "Relevant";
//            }
//            evaluation.setImpactMaterialityPotentialImpact(String.format(Locale.US, "%.2f", finalScore));
//            evaluation.setResultMaterialityAssessment(finalRelevance);
//        } else if ("financial".equalsIgnoreCase(iro.getIroType())) {
//            BigDecimal financialEffect = Optional.ofNullable(evaluation.getFinancialMaterialityActualImpact()).map(BigDecimal::valueOf).orElse(BigDecimal.ONE);
//            BigDecimal probability = Optional.ofNullable(evaluation.getProbability()).map(BigDecimal::valueOf).orElse(new BigDecimal("0.2"));
//            finalScore = financialEffect.multiply(probability).setScale(SCALE, ROUNDING_MODE);
//            finalRelevance = finalScore.compareTo(threshold) >= 0 ? "Relevant" : "Not Relevant";
//            evaluation.setFinancialMaterialityPotentialImpact(String.format(Locale.US, "%.2f", finalScore));
//            evaluation.setResultMaterialityAssessment(finalRelevance);
//        } else {
//            finalRelevance = "Not Relevant";
//        }
//        return finalRelevance;
//    }
//
//    private EsrsTopic findBestTopicMatch(EsrsTopicDto dto, List<EsrsTopic> allTopics) {
//        if (dto == null) return null;
//        EsrsTopic bestMatch = null;
//        int minDistance = Integer.MAX_VALUE;
//        String sourceString = buildNormalizedString(dto);
//        LevenshteinDistance levenshtein = new LevenshteinDistance(LEVENSHTEIN_DISTANCE_THRESHOLD);
//        for (EsrsTopic dbTopic : allTopics) {
//            String targetString = buildNormalizedString(dbTopic);
//            int distance = levenshtein.apply(sourceString, targetString);
//            if (distance != -1 && distance < minDistance) {
//                minDistance = distance;
//                bestMatch = dbTopic;
//                if (minDistance == 0) break;
//            }
//        }
//        if (bestMatch != null) {
//            log.trace("Fuzzy match for '{}'. Best match: '{}' with distance {}.", sourceString, buildNormalizedString(bestMatch), minDistance);
//        } else {
//            log.warn("No suitable fuzzy match found for ESRS topic: {}", sourceString);
//        }
//        return bestMatch;
//    }
//
//    private String normalize(String input) {
//        return (input == null) ? "" : input.trim().toLowerCase();
//    }
//
//    private String buildNormalizedString(EsrsTopic topic) {
//        return String.join("|", normalize(topic.getTopic()), normalize(topic.getSubtopic()), normalize(topic.getSubSubTopic()));
//    }
//
//    private String buildNormalizedString(EsrsTopicDto dto) {
//        String subSubTopic = dto.getSubSubTopic();
//        if (subSubTopic != null && ("Social".equalsIgnoreCase(dto.getArea()) || "Governance".equalsIgnoreCase(dto.getArea()))) {
//            subSubTopic = subSubTopic.replaceAll("^[A-Z][0-9]+(-[0-9]+)?[:\\s]*", "").trim();
//        }
//        return String.join("|", normalize(dto.getTopic()), normalize(dto.getSubtopic()), normalize(subSubTopic));
//    }
//
//    private List<DataSeedDto> loadJsonData() throws Exception {
//        try (InputStream inputStream = new ClassPathResource(JSON_DATA_FILE_PATH).getInputStream()) {
//            return objectMapper.readValue(inputStream, new TypeReference<>() {});
//        }
//    }
//
//    private IroEvaluation mapIroEvaluation(IroEvaluationSeedDto dto, Company company) {
//        IroEvaluation eval = new IroEvaluation();
//        eval.setCompany(company);
//        eval.setDescription(dto.getDescription());
//        eval.setDirectIndirectImpact(dto.getDirectIndirectImpact());
//        eval.setAffectedArea(dto.getAffectedArea());
//        eval.setTimeHorizon(dto.getTimeHorizon() != null ? dto.getTimeHorizon().trim() : null);
//        eval.setPositiveNegativeImpact(dto.getPositiveNegativeImpact());
//        eval.setActualPotentialImpact(dto.getActualPotentialImpact());
//        eval.setHumanRightsImpact(dto.getHumanRightsImpact());
//        eval.setScale(dto.getScale());
//        eval.setScope(dto.getScope());
//        eval.setIrreversibility(dto.getIrreversibility());
//        eval.setRiskOpportunity(dto.getRiskOpportunity());
//        eval.setFinancialMaterialityActualImpact(dto.getFinancialMaterialityActualImpact());
//        eval.setProbability(dto.getProbability());
//        eval.setIsLocked(false);
//        eval.setLastModifiedBy("System-Seeder");
//        eval.setLastModifiedAt(LocalDateTime.now());
//        return eval;
//    }
//}