package com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables;


import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
@Deprecated
public class EmissionData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String activity;
    private Double reduction;

    public void setActivity(String stringCellValue) {
        this.activity = stringCellValue;
    }

    public void setReduction(Double numericCellValue) {
        this.reduction = numericCellValue;
    }


    // Getters and Setters
}
