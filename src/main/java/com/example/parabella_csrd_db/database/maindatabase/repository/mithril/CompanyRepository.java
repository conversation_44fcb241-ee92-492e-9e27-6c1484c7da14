package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {
    List<Company> findByCompanyGroupId(Long companyGroupId);
    Optional<Company> findByProject(Project project);
    /**
     * ADDED: Finds a company associated with a specific project ID.
     * This is required by the data seeder to find the correct company
     * for the project being seeded.
     *
     * @param projectId The ID of the project.
     * @return An Optional containing the company if found.
     */
    Optional<Company> findByProjectId(Long projectId);
}
