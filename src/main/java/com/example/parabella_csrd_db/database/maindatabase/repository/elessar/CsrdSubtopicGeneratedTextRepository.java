package com.example.parabella_csrd_db.database.maindatabase.repository.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.userContent.CsrdSubtopicGeneratedText;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CsrdSubtopicGeneratedTextRepository extends JpaRepository<CsrdSubtopicGeneratedText, Long> {

    // Find generated text for a specific subtopic within a project for a given year
    Optional<CsrdSubtopicGeneratedText> findByCsrdProjectIdAndCsrdSubtopicIdAndReportingYear(
            Long csrdProjectId, Long csrdSubtopicId, Integer reportingYear);
}