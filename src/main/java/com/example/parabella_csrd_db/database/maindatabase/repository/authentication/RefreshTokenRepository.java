package com.example.parabella_csrd_db.database.maindatabase.repository.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.RefreshToken;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for RefreshToken entities.
 * Provides methods for managing refresh tokens in the database.
 */
@Repository
public interface RefreshTokenRepository extends JpaRepository<RefreshToken, Long> {
    
    Optional<RefreshToken> findByToken(String token);
    
    Optional<RefreshToken> findByUserAndIsRevokedFalse(User user);
    
    List<RefreshToken> findByUserAndIsRevokedFalseAndExpiryDateAfter(User user, Instant now);
    
    @Modifying
    int deleteByUser(User user);
    
    @Modifying
    @Query("UPDATE RefreshToken r SET r.isRevoked = true WHERE r.user = ?1")
    int revokeAllUserTokens(User user);
    
    @Modifying
    @Query("DELETE FROM RefreshToken r WHERE r.expiryDate < ?1")
    int deleteExpiredTokens(Instant now);
    
    @Query("SELECT COUNT(r) FROM RefreshToken r WHERE r.user = ?1 AND r.isRevoked = false AND r.expiryDate > ?2")
    long countActiveTokensByUser(User user, Instant now);
}