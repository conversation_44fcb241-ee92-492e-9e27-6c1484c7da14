package com.example.parabella_csrd_db.controller.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Permission;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.VerificationToken;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.request.*;
import com.example.parabella_csrd_db.dto.authentication.response.JwtResponse;
import com.example.parabella_csrd_db.dto.authentication.response.MessageResponse;
import com.example.parabella_csrd_db.dto.authentication.response.TokenRefreshResponse;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.RefreshToken;
import com.example.parabella_csrd_db.security.jwt.JwtUtils;
import com.example.parabella_csrd_db.security.jwt.exception.TokenRefreshException;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;
import com.example.parabella_csrd_db.security.utils.ForgotPasswordService;
import com.example.parabella_csrd_db.security.utils.TotpUtils;
import com.example.parabella_csrd_db.service.authentication.RefreshTokenService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
//TODO: Decouple logic, create AuthService

/**
 * Authentication Controller for the Parabella CSRD Application
 * <p>
 * This controller handles all authentication-related operations including:
 * - User registration and login
 * - Two-Factor Authentication (2FA) using TOTP (Time-based One-Time Password)
 * - Password reset functionality
 * - JWT token generation and validation
 * - Role-based access control
 * <p>
 * Security Features:
 * - CORS enabled for specific origins (Google Cloud Storage and localhost)
 * - JWT-based authentication with role-based authorization
 * - Mandatory 2FA for all users using TOTP
 * - Secure password reset with token-based verification
 * - Password encryption using BCrypt
 * <p>
 * Supported Roles:
 * - ROLE_USER: Standard user access
 * - ROLE_MODERATOR: Moderator privileges
 * - ROLE_ADMIN: Full administrative access
 * <p>
 * API Endpoints:
 * - POST /api/auth/signup: User registration with 2FA setup
 * - POST /api/auth/signin: User authentication with 2FA verification
 * - POST /api/auth/forgot-password: Initiate password reset process
 * - POST /api/auth/reset-password: Complete password reset with token
 * - POST /api/auth/verify2fa: Verify 2FA setup during registration
 * - PATCH /api/auth/enable2fa: Enable 2FA for existing users
 * - PATCH /api/auth/disable2fa: Disable 2FA (requires authentication)
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024
 */


//TODO: Decouxple logic, create AuthService
@Tag(name = "Authentication", description = "Authentication and user management operations including JWT authentication, 2FA support, and password management")
@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    
    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    UserRepository userRepository;
    @Autowired
    VerificationTokenRepository tokenRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    PasswordEncoder encoder;

    @Autowired
    JwtUtils jwtUtils;

    @Autowired
    private ForgotPasswordService forgotPasswordService;
    
    @Autowired
    private RefreshTokenService refreshTokenService;


    @Operation(
        summary = "Initiate password reset",
        description = "Send a password reset email to the specified email address. For security, always returns success message regardless of whether email exists."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Password reset email sent (if email exists)",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = MessageResponse.class),
                examples = @ExampleObject(
                    value = "{\"message\": \"If that email is associated with an account, a reset link was sent.\"}"
                )
            )
        ),
        @ApiResponse(responseCode = "400", description = "Invalid email format", content = @Content),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/forgot-password")
    public ResponseEntity<?> forgotPassword(
        @Parameter(description = "Password reset request with user email", required = true)
        @RequestBody @Valid ForgotPasswordRequest request
    ) {
        forgotPasswordService.initiatePasswordReset(request.getEmail());

        return ResponseEntity.ok(
                Collections.singletonMap("message", "If that email is associated with an account, a reset link was sent.")
        );
    }

    @Operation(
        summary = "Reset password with token",
        description = "Complete password reset process using the token from the reset email"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Password reset successful", content = @Content(schema = @Schema(type = "boolean"))),
        @ApiResponse(responseCode = "400", description = "Invalid or expired token", content = @Content),
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/reset-password")
    public boolean resetPassword(
        @Parameter(description = "Password reset request with token and new password", required = true)
        @RequestBody ResetPasswordRequest payload
    ) {
        return forgotPasswordService.resetPassword(payload.getToken(), payload.getPassword());
    }


    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest, HttpServletRequest request) {
        try {
            
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            User user = userRepository.findByUsername(userDetails.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found after authentication"));


            // --- FIXED 2FA LOGIC ---

            // CASE 1: 2FA is enabled and properly set up
            if (user.isTotpEnabled()) {
                Integer providedCode = loginRequest.getTotpCode();
                if (providedCode == null || !TotpUtils.validateCode(user.getTotpSecret(), providedCode)) {
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(new MessageResponse("Error: Invalid or missing TOTP code."));
                }
                // If code is valid, proceed to generate JWT and log in.
            } else {
                // CASE 2: 2FA is NOT yet enabled. User must set it up now.
                // This handles users who haven't completed 2FA setup or legacy users.

                // Ensure a secret exists. If not (e.g., legacy user), generate one.
                if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
                    String totpSecret = TotpUtils.generateSecret();
                    user.setTotpSecret(totpSecret);
                    userRepository.save(user);
                }

                String qrUrl = TotpUtils.getQrCodeUrl(user.getTotpSecret(), user.getEmail(), "Parabella_Csrd_App");

                // Return a response indicating that setup is required.
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new HashMap<String, Object>() {{
                            put("message", "2FA setup required. Please scan the QR code and verify.");
                            put("qrCodeUrl", qrUrl);
                            put("username", user.getUsername());
                        }});
            }

            // --- END FIXED LOGIC ---

            // This part now only runs if 2FA was enabled and the code was valid.
            String jwt = jwtUtils.generateJwtToken(authentication);
            
            // Create refresh token
            String deviceInfo = request.getHeader("User-Agent");
            String ipAddress = request.getRemoteAddr();
            RefreshToken refreshToken = refreshTokenService.createRefreshToken(userDetails.getId(), deviceInfo, ipAddress);
            
            List<String> roles = new ArrayList<>();
            if (user.getRole() != null) {
                roles.add(user.getRole().getName());
            }

            List<String> permissions = new ArrayList<>();
            Role userRole = user.getRole();
            if (userRole != null && userRole.getPermissions() != null) {
                permissions = userRole.getPermissions().stream()
                        .map(Permission::getFunctionKey)
                        .collect(Collectors.toList());
            }

            JwtResponse response = new JwtResponse(
                    jwt,
                    refreshToken.getToken(),
                    userDetails.getId(),
                    userDetails.getUsername(),
                    userDetails.getEmail(),
                    roles,
                    permissions
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Authentication failed for user: {}", loginRequest.getUsername(), e);
            
            // Generic error message to prevent information disclosure
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new MessageResponse("Error: Invalid credentials"));
        }
    }

    /**
     * Registers a new user account with automatic 2FA setup
     * <p>
     * This endpoint creates a new user account with the following features:
     * - Username and email uniqueness validation
     * - Password encryption using BCrypt
     * - Automatic role assignment (default: ROLE_USER)
     * - Automatic TOTP secret generation for 2FA
     * - QR code generation for authenticator app setup
     * <p>
     * Role Assignment Logic:
     * - No roles specified: Assigns ROLE_USER
     * - "admin": Assigns ROLE_ADMIN
     * - "mod": Assigns ROLE_MODERATOR
     * - Any other value: Assigns ROLE_USER
     * <p>
     * Security Features:
     * - Prevents duplicate usernames and emails
     * - Passwords are encrypted before storage
     * - Mandatory 2FA setup for all new accounts
     * - Returns QR code for immediate authenticator setup
     *
     * @param signUpRequest SignUpRequest containing username, email, password, and optional roles
     * @return ResponseEntity containing either:
     * - Success message with QR code URL for 2FA setup
     * - Error message if username or email already exists
     * @throws RuntimeException if required roles are not found in the database
     * @apiNote POST /api/auth/signup
     * @since 1.0
     */
    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignUpRequest signUpRequest) {
        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Username is already taken!"));
        }

        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Email is already in use!"));
        }

        User user = new User(signUpRequest.getUsername(),
                signUpRequest.getEmail(),
                encoder.encode(signUpRequest.getPassword()));

        // MODIFIED: Assign the default "User" role by name.
        // The complex switch logic is no longer needed for public signups.
        // Admin role assignment should happen through the User Management UI, not public signup.
        Role userRole = roleRepository.findByName("User")
                .orElseThrow(() -> new RuntimeException("Error: Default User role not found. Please run the data seeder."));
        user.setRole(userRole);

        // Generate TOTP secret for the new user
        String totpSecret = TotpUtils.generateSecret();
        user.setTotpSecret(totpSecret);
        userRepository.save(user);
        String qrUrl = TotpUtils.getQrCodeUrl(totpSecret, user.getEmail(), "Parabella_Csrd_App");

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("message", "User registered successfully! Set up your authenticator with this QR:");
            put("qrCodeUrl", qrUrl);
            put("username", user.getUsername());
        }});
    }

    /**
     * Verifies 2FA setup by validating a TOTP code during user registration
     * <p>
     * This endpoint is used to verify that a user has successfully set up their
     * authenticator app after registration. It validates the TOTP code generated
     * by the user's authenticator app against the stored secret.
     * <p>
     * Verification Process:
     * 1. Validates that username and TOTP code are provided
     * 2. Checks that the user exists and has a TOTP secret
     * 3. Validates the provided TOTP code against the stored secret
     * 4. Returns success or failure message
     * <p>
     * Security Considerations:
     * - Requires valid TOTP code from authenticator app
     * - Prevents authentication without proper 2FA setup
     * - Validates code format and numeric value
     *
     * @param payload Map containing "username" and "code" keys
     * @return ResponseEntity with success message if verification succeeds,
     * error message if verification fails or parameters are invalid
     * @throws RuntimeException      if user is not found
     * @throws NumberFormatException if TOTP code format is invalid
     * @apiNote POST /api/auth/verify2fa
     * @since 1.0
     */
    @PostMapping("/verify2fa")
    public ResponseEntity<?> verify2FA(@RequestBody Map<String, String> payload) {
        String username = payload.get("username");
        String codeStr = payload.get("code");

        if (username == null || codeStr == null) {

            return ResponseEntity.badRequest().body(new MessageResponse("Username and TOTP code are required"));
        }

        Integer code;

        try {
            code = Integer.valueOf(codeStr);
        } catch (NumberFormatException e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Invalid TOTP code format"));
        }

        Optional<User> userOptional = userRepository.findByUsername(username);
        
        // Prevent user enumeration by always validating the code even if user doesn't exist
        if (userOptional.isEmpty()) {
            // Perform a dummy TOTP validation to prevent timing attacks
            TotpUtils.validateCode("dummy-secret-to-prevent-timing-attacks", code);
            return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
                    .body(new MessageResponse("Invalid TOTP code"));
        }
        
        User user = userOptional.get();

        if (user.isTotpEnabled()) {
            return ResponseEntity.badRequest().body(new MessageResponse("2FA is already verified and enabled for this account."));
        }

        if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
            return ResponseEntity.badRequest().body(new MessageResponse("No TOTP secret found for user. Setup not initialized."));
        }

        if (TotpUtils.validateCode(user.getTotpSecret(), Integer.valueOf(codeStr))) {
            // --- THIS IS THE KEY ---
            // On successful verification, enable 2FA permanently.
            user.setTotpEnabled(true);
            userRepository.save(user);
            // --- END KEY ---

            return ResponseEntity.ok(new MessageResponse("2FA verified successfully. You can now log in."));
        } else {
            return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
                    .body(new MessageResponse("Invalid TOTP code"));
        }
    }


    @PatchMapping("/enable2fa")
    public ResponseEntity<?> enable2FA(Authentication authentication) {
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            User user = userRepository.findByUsername(userDetails.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
                String newSecret = TotpUtils.generateSecret();
                user.setTotpSecret(newSecret);
                userRepository.save(user);
                String qrUrl = TotpUtils.getQrCodeUrl(newSecret, user.getEmail(), "Parabella_Csrd_App");
                return ResponseEntity.ok(new MessageResponse("2FA enabled. Scan this QR: " + qrUrl));
            } else {
                return ResponseEntity.badRequest().body(new MessageResponse("2FA is already enabled."));
            }
        } catch (Exception e) {
            logger.error("Failed to enable 2FA for user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponse("Error: Unable to enable 2FA"));
        }
    }


    @PatchMapping("/disable2fa")
    public ResponseEntity<?> disable2FA(Authentication authentication) {
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            User user = userRepository.findByUsername(userDetails.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            user.setTotpSecret(null);
            user.setTotpEnabled(false);
            userRepository.save(user);
            return ResponseEntity.ok(new MessageResponse("2FA disabled."));
        } catch (Exception e) {
            logger.error("Failed to disable 2FA for user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponse("Error: Unable to disable 2FA"));
        }
    }

    /**
     * Sets a new password for a user, typically after an invitation.
     *
     * @param request
     * @return
     */
    @PostMapping("/set-password")
    public ResponseEntity<?> setNewPassword(@RequestBody SetPasswordRequest request) {
        try {
            VerificationToken verificationToken = tokenRepository.findByToken(request.token())
                    .orElseThrow(() -> new IllegalArgumentException("Invalid token."));

            if (verificationToken.isExpired()) {
                tokenRepository.delete(verificationToken); // Clean up expired token
                throw new IllegalArgumentException("Token has expired.");
            }

            User user = verificationToken.getUser();
            user.setPassword(encoder.encode(request.password()));
            user.setEnabled(true);

            userRepository.save(user);

            // Invalidate the token by deleting it
            tokenRepository.delete(verificationToken);
            return ResponseEntity.ok("Password has been set successfully. You can now log in.");
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @Operation(
        summary = "Refresh access token",
        description = "Exchange a valid refresh token for a new access token and refresh token pair"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Token refreshed successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = TokenRefreshResponse.class)
            )
        ),
        @ApiResponse(responseCode = "403", description = "Invalid or expired refresh token", content = @Content),
        @ApiResponse(responseCode = "400", description = "Bad request", content = @Content)
    })
    @PostMapping("/refresh")
    public ResponseEntity<?> refreshToken(@Valid @RequestBody TokenRefreshRequest request, HttpServletRequest httpRequest) {
        String requestRefreshToken = request.getRefreshToken();
        
        return refreshTokenService.findByToken(requestRefreshToken)
                .map(refreshTokenService::verifyExpiration)
                .map(RefreshToken::getUser)
                .map(user -> {
                    // Generate new access token
                    UserDetailsImpl userDetails = UserDetailsImpl.build(user);
                    Authentication auth = new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities());
                    String newAccessToken = jwtUtils.generateJwtToken(auth);
                    
                    // Create new refresh token (rotate for security)
                    String deviceInfo = httpRequest.getHeader("User-Agent");
                    String ipAddress = httpRequest.getRemoteAddr();
                    RefreshToken newRefreshToken = refreshTokenService.createRefreshToken(
                            user.getId(), deviceInfo, ipAddress);
                    
                    // Delete old refresh token
                    refreshTokenService.deleteByToken(requestRefreshToken);
                    
                    return ResponseEntity.ok(new TokenRefreshResponse(newAccessToken, newRefreshToken.getToken()));
                })
                .orElseThrow(() -> new TokenRefreshException(requestRefreshToken,
                        "Refresh token is not in database!"));
    }
    
    @Operation(
        summary = "Logout user",
        description = "Revoke all refresh tokens for the authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Logged out successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = MessageResponse.class)
            )
        ),
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content)
    })
    @PostMapping("/logout")
    public ResponseEntity<?> logoutUser(Authentication authentication) {
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            refreshTokenService.revokeAllUserTokens(userDetails.getId());
            return ResponseEntity.ok(new MessageResponse("Logged out successfully!"));
        }
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new MessageResponse("Error: Not authenticated"));
    }

    @Operation(
        summary = "Get active refresh tokens",
        description = "Get count of active refresh tokens for debugging (admin only)"
    )
    @GetMapping("/debug/tokens")
    public ResponseEntity<?> getActiveTokens(Authentication authentication) {
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            long count = refreshTokenService.getActiveTokenCount(userDetails.getId());
            return ResponseEntity.ok(Collections.singletonMap("activeTokens", count));
        }
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(new MessageResponse("Error: Not authenticated"));
    }

    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser() {
        // Explicitly get the authentication object from the security context
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        // Check if the user is authenticated. The principal should be an instance of UserDetailsImpl.
        if (authentication == null || !authentication.isAuthenticated() || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(new MessageResponse("Error: User is not authenticated or session is invalid."));
        }

        // The authentication object is populated by the JwtAuthTokenFilter
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();

        // Fetch the user from the DB to get the most up-to-date role and permission info
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("Authenticated user not found in database."));

        // Extract roles from the user's role
        List<String> roles = new ArrayList<>();
        Role userRole = user.getRole();
        if (userRole != null) {
            roles.add(userRole.getName());
        }

        // Extract permissions from the user's role
        List<String> permissions = new ArrayList<>();
        if (userRole != null && userRole.getPermissions() != null) {
            permissions = userRole.getPermissions().stream()
                    .map(Permission::getFunctionKey)
                    .collect(Collectors.toList());
        }

        // Return a response similar to login, but without a new token.
        // This confirms to the frontend that the token is valid and provides fresh user data.
        return ResponseEntity.ok(new JwtResponse(
                null, // A new token is not issued on this endpoint
                userDetails.getId(),
                userDetails.getUsername(),
                userDetails.getEmail(),
                roles,
                permissions
        ));
    }

}
