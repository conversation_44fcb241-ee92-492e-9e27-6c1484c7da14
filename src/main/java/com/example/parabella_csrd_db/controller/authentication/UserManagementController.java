package com.example.parabella_csrd_db.controller.authentication;


import com.example.parabella_csrd_db.dto.authentication.user.UserDto;
import com.example.parabella_csrd_db.dto.authentication.user.UserInviteRequest;
import com.example.parabella_csrd_db.dto.authentication.user.UserUpdateRoleRequest;
import com.example.parabella_csrd_db.service.authentication.UserManagementService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserManagementController {

    private final UserManagementService userService;

    @GetMapping
    @PreAuthorize("hasAuthority('user.view')")
    public ResponseEntity<List<UserDto>> getUsers() {
        return ResponseEntity.ok(userService.findAllUsers());
    }

    @PostMapping("/invite")
    @PreAuthorize("hasAuthority('user.invite')")
    public ResponseEntity<UserDto> inviteUser(@Valid @RequestBody UserInviteRequest inviteRequest) {
        return new ResponseEntity<>(userService.inviteUser(inviteRequest), HttpStatus.CREATED);
    }

    @PutMapping("/{userId}/role")
    @PreAuthorize("hasAuthority('user.edit')")
    public ResponseEntity<UserDto> updateUserRole(@PathVariable Long userId, @Valid @RequestBody UserUpdateRoleRequest updateRequest) {
        return ResponseEntity.ok(userService.updateUserRole(userId, updateRequest));
    }

    @DeleteMapping("/{userId}")
    @PreAuthorize("hasAuthority('user.delete')")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteUser(@PathVariable Long userId) {
        userService.deleteUser(userId);
    }

    /**
     * Provides a list of users who are eligible to be assigned to roles
     * like "Responsible Person" or "Stakeholder". The eligibility is determined
     * by the roles configured in the application properties.
     *
     * @return A list of assignable users.
     */
    @GetMapping("/assignable")
    @PreAuthorize("hasAuthority('user.view')")
    public ResponseEntity<List<UserDto>> getAssignableUsers() {
        return ResponseEntity.ok(userService.findAssignableUsers());
    }
}