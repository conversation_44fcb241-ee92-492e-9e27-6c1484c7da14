// src/main/java/com/example/parabella_csrd_db/controller/ai/AiAutofillController.java
package com.example.parabella_csrd_db.controller.ai;


import com.example.parabella_csrd_db.dto.elessar.AutofillDatapointRequestDTO;
import com.example.parabella_csrd_db.dto.elessar.AutofillDatapointResponseDTO;
import com.example.parabella_csrd_db.service.ai.PromptingService;
import com.example.parabella_csrd_db.service.chat_bot.OpenAIService; // Assuming OpenAIService is correctly located
import org.apache.tomcat.util.net.openssl.ciphers.Authentication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/ai") // Base path for AI related endpoints
public class AiAutofillController {

    private static final Logger log = LoggerFactory.getLogger(AiAutofillController.class);

    private final PromptingService promptingService;
    private final OpenAIService openAIService;

    @Autowired
    public AiAutofillController(PromptingService promptingService, OpenAIService openAIService) {
        this.promptingService = promptingService;
        this.openAIService = openAIService;
    }

    @PostMapping("/autofill-datapoint")
    public ResponseEntity<?> autofillDatapoint(@RequestBody AutofillDatapointRequestDTO request) {
        if (request.getProjectId() == null || request.getDatapointId() == null || request.getDatapointLabel() == null) {
            return ResponseEntity.badRequest().body("Missing required fields in request (projectId, datapointId, datapointLabel).");
        }

        log.info("Received AI autofill request for datapoint ID: {}, project ID: {}", request.getDatapointId(), request.getProjectId());

        try {
            String prompt = promptingService.buildAutofillDatapointPrompt(request);
            log.debug("Generated prompt for AI autofill:\n{}", prompt); // Be cautious logging full prompts if they contain sensitive data

            // Using the existing getCompletionForReason structure from OpenAIService
            // If you need more control (e.g., system messages), modify OpenAIService or add a new method.
            List<Map<String, String>> messages = List.of(
                    // Optional: Add a system message to further define the AI's persona or task.
                    // Map.of("role", "system", "content", "You are an ESG reporting expert creating audit-ready text."),
                    Map.of("role", "user", "content", prompt)
            );

            String generatedText = openAIService.getChatCompletion(messages); // Use getChatCompletion for list of messages
            log.info("AI generated text for datapoint ID {}: {}", request.getDatapointId(), generatedText);

            AutofillDatapointResponseDTO response = new AutofillDatapointResponseDTO(request.getDatapointId(), generatedText);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error during AI autofill for datapoint ID {}: {}", request.getDatapointId(), e.getMessage(), e);
            // Provide a generic error message to the client
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred while processing the AI autofill request.");
        }
    }

}