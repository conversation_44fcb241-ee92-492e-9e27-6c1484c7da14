package com.example.parabella_csrd_db.controller.elessar;


import com.example.parabella_csrd_db.dto.elessar.userContent.CsrdDatapointResponseDTO;
import com.example.parabella_csrd_db.service.elessar.CsrdDatapointResponseService;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/csrd/datapoint-responses")
public class CsrdDatapointResponseController {

    private static final Logger log = LoggerFactory.getLogger(CsrdDatapointResponseController.class);
    private final CsrdDatapointResponseService csrdDatapointResponseService;

    @Autowired
    public CsrdDatapointResponseController(CsrdDatapointResponseService csrdDatapointResponseService) {
        this.csrdDatapointResponseService = csrdDatapointResponseService;
    }

    /**
     * ✅ REFACTORED METHOD
     * Handles both CREATION and UPDATES of a datapoint response (Upsert).
     * This is now the single endpoint for saving data, aligning with the frontend's behavior.
     */
    @PostMapping
    @PreAuthorize("hasAuthority('csrd.edit')")
    public ResponseEntity<CsrdDatapointResponseDTO> createOrUpdateDatapointResponse(
            @Valid @RequestBody CsrdDatapointResponseDTO dto) {
        try {
            if (dto.getReportingYear() == null) {
                dto.setReportingYear(LocalDateTime.now().getYear());
            }

            boolean isUpdate = csrdDatapointResponseService.exists(dto.getCsrdProjectId(), dto.getEsrsDatapointId(), dto.getReportingYear());
            System.out.println("Repsonse source:"+ dto.getEsrsDatapointId());
            CsrdDatapointResponseDTO savedDto = csrdDatapointResponseService.saveOrUpdateDatapointResponse(dto);


            // Return 201 CREATED for new resources, 200 OK for updates.
            return new ResponseEntity<>(savedDto, isUpdate ? HttpStatus.OK : HttpStatus.CREATED);

        } catch (EntityNotFoundException e) {
            log.error("Entity not found while saving datapoint response: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND); // Return 404 if a related entity (Project/Datapoint) is missing.
        } catch (Exception e) {
            log.error("An unexpected error occurred while saving datapoint response for project {} and datapoint {}",
                    dto.getCsrdProjectId(), dto.getEsrsDatapointId(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Retrieves a specific CSRD datapoint response.
     * Note: The path was slightly adjusted to be consistent with the base mapping.
     * @param projectId The ID of the CSRD project.
     * @param datapointId The ID of the ESRS datapoint.
     * @param reportingYear Optional: The reporting year. Defaults to current year if not provided.
     */
    @GetMapping("/project/{projectId}/datapoint/{datapointId}")
    @PreAuthorize("hasAuthority('csrd.read')")
    public ResponseEntity<CsrdDatapointResponseDTO> getDatapointResponse(
            @PathVariable Long projectId,
            @PathVariable Long datapointId,
            @RequestParam(required = false) Integer reportingYear) {
        try {
            CsrdDatapointResponseDTO dto = csrdDatapointResponseService.getDatapointResponse(projectId, datapointId, reportingYear);
            return new ResponseEntity<>(dto, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("Error fetching datapoint response for project {}, datapoint {}", projectId, datapointId, e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Retrieves all CSRD datapoint responses for a given project.
     * This matches the frontend's fetchAllCsrdDatapointResponsesForProject function.
     * @param projectId The ID of the CSRD project.
     * @param reportingYear Optional: The reporting year.
     */
    @GetMapping("/project/{projectId}")
    @PreAuthorize("hasAuthority('csrd.read')")
    public ResponseEntity<List<CsrdDatapointResponseDTO>> getAllDatapointResponsesForProject(
            @PathVariable Long projectId,
            @RequestParam(required = false) Integer reportingYear) {
        try {
            List<CsrdDatapointResponseDTO> dtos = csrdDatapointResponseService.getAllDatapointResponsesForProject(projectId, reportingYear);
            return new ResponseEntity<>(dtos, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error fetching all datapoint responses for project {}", projectId, e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}