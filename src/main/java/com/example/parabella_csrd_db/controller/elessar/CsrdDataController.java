package com.example.parabella_csrd_db.controller.elessar;


import com.example.parabella_csrd_db.dto.elessar.CsrdTopicDTO;

import com.example.parabella_csrd_db.service.elessar.CsrdDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/csrd")
public class CsrdDataController {

    private final CsrdDataService csrdDataService;

    @Autowired
    public CsrdDataController(CsrdDataService csrdDataService) {
        this.csrdDataService = csrdDataService;
    }

    @GetMapping("/data")
    public ResponseEntity<List<CsrdTopicDTO>> getAllCsrdData() {
        List<CsrdTopicDTO> data = csrdDataService.getAggregatedCsrdData();
        return ResponseEntity.ok(data);
    }
}