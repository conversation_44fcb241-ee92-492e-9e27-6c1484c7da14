package com.example.parabella_csrd_db.controller.elessar;// src/main/java/com/example/parabella_csrd_db/controller/csrd/CsrdProjectController.java



import com.example.parabella_csrd_db.dto.elessar.CompanyInfoDTO;
import com.example.parabella_csrd_db.dto.elessar.CsrdProjectDTO;
import com.example.parabella_csrd_db.service.elessar.CsrdProjectService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
// import org.springframework.security.core.annotation.AuthenticationPrincipal;
// import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@RestController
@RequestMapping("/api/csrd-projects")
@RequiredArgsConstructor
public class CsrdProjectController {

    private final CsrdProjectService csrdProjectService;

    /**
     * GET /api/csrd-projects/user/{userId} - See previous security notes.
     * Ideally: GET /api/csrd-projects/my-projects using @AuthenticationPrincipal
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('csrd.read')")
    public ResponseEntity<List<CsrdProjectDTO>> getUserCsrdProjects(@PathVariable Long userId) {
        // Add security checks based on logged-in user vs path variable if necessary
        List<CsrdProjectDTO> projects = csrdProjectService.getUserCsrdProjects(userId);
        return ResponseEntity.ok(projects);
    }

    /**
     * GET /api/csrd-projects/{projectId}
     */
    @GetMapping("/{projectId}")
    @PreAuthorize("hasAuthority('csrd.read')")
    public ResponseEntity<CsrdProjectDTO> getCsrdProjectById(@PathVariable Long projectId /*, @AuthenticationPrincipal UserDetails principal */) {
        // Optional: Add security check: does principal own projectId?
        CsrdProjectDTO project = csrdProjectService.getCsrdProjectById(projectId);
        return ResponseEntity.ok(project);
    }

    /**
     * POST /api/csrd-projects/create - Uses CsrdProjectDTO for request body
     */
    @PostMapping("/create")
    @PreAuthorize("hasAuthority('csrd.create')")
    public ResponseEntity<CsrdProjectDTO> createCsrdProject(
            @Valid @RequestBody CsrdProjectDTO requestDTO // Now uses the consolidated DTO
            /*, @AuthenticationPrincipal UserDetails principal */) {

        // --- Security Consideration ---
        // ALWAYS override userId from request with the actual logged-in user's ID
        // Long loggedInUserId = getUserIdFromPrincipal(principal); // Implement this helper
        // requestDTO.setUserId(loggedInUserId);
        // ---

        CsrdProjectDTO createdProject = csrdProjectService.createCsrdProject(requestDTO);

        URI location = ServletUriComponentsBuilder
                .fromCurrentContextPath().path("/api/csrd-projects/{id}")
                .buildAndExpand(createdProject.getId()).toUri();

        return ResponseEntity.created(location).body(createdProject);
    }

    /**
     * PUT /api/csrd-projects/{projectId}/company-info - Uses CompanyInfoDTO for request body
     */
    @PutMapping("/{projectId}/company-info")
    @PreAuthorize("hasAuthority('csrd.edit')")
    public ResponseEntity<CompanyInfoDTO> saveOrUpdateCompanyInfo(
            @PathVariable Long projectId,
            @Valid @RequestBody CompanyInfoDTO requestDTO // Now uses the consolidated DTO
            /*, @AuthenticationPrincipal UserDetails principal */) {

        // Optional: Add security check: does principal own projectId?

        CompanyInfoDTO savedInfo = csrdProjectService.saveOrUpdateCompanyInfo(projectId, requestDTO);
        return ResponseEntity.ok(savedInfo);
    }

    /**
     * GET /api/csrd-projects/{projectId}/company-info
     */
    @GetMapping("/{projectId}/company-info")
    @PreAuthorize("hasAuthority('csrd.read')")
    public ResponseEntity<CompanyInfoDTO> getCompanyInfo(
            @PathVariable Long projectId
            /*, @AuthenticationPrincipal UserDetails principal */) {
        // Optional: Add security check
        CompanyInfoDTO companyInfo = csrdProjectService.getCompanyInfoByProjectId(projectId);
        return ResponseEntity.ok(companyInfo);
    }

    // TODO: Add DELETE, COPY endpoints if needed
}