package com.example.parabella_csrd_db.controller.utilities;

import com.example.parabella_csrd_db.service.mithril.ExcelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
@RestController
@RequestMapping("/api/excel")
public class ExcelController {

    private final ExcelService excelService;

    @Autowired
    public ExcelController(ExcelService excelService) {
        this.excelService = excelService;
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})

    @GetMapping("/data")
    @PreAuthorize("hasAuthority('excel.read')")
    public List<Map<String, String>> getExcelData(@RequestParam String fileName) throws IOException {
        return excelService.getExcelData(fileName);
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})

    @GetMapping("/all-data")
    @PreAuthorize("hasAuthority('excel.read')")
    public Map<String, List<Map<String, String>>> getAllExcelData() throws IOException {
        return excelService.getAllExcelData();
    }
}
