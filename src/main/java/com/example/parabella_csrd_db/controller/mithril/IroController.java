package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.IroDto;
import com.example.parabella_csrd_db.service.mithril.IroService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/iros")
public class IroController {

    private static final Logger logger = LoggerFactory.getLogger(IroController.class);

    private final IroService iroService;

    public IroController(IroService iroService) {
        this.iroService = iroService;
    }

    @PostMapping

    public ResponseEntity<IroDto> createIro(@RequestBody IroDto iroDto) {
        logger.info("Received request to create Iro: {}", iroDto);
        System.out.println(iroDto);
        IroDto created = iroService.createIro(iroDto);
        logger.info("Created Iro with ID: {}", created.getId());
        return new ResponseEntity<>(created, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")

    public ResponseEntity<IroDto> getIroById(@PathVariable Long id) {
        logger.info("Received request to get Iro with ID: {}", id);
        IroDto dto = iroService.getIroById(id);
        logger.info("Returning Iro: {}", dto);
        return ResponseEntity.ok(dto);
    }

    @GetMapping

    public ResponseEntity<List<IroDto>> getAllIros() {
        logger.info("Received request to get all Iros");
        List<IroDto> all = iroService.getAllIros();
        logger.info("Returning {} Iros", all.size());
        return ResponseEntity.ok(all);
    }


    @GetMapping("/company/{companyId}")

    public ResponseEntity<List<IroDto>> getIrosByCompanyId(@PathVariable Long companyId) {
        logger.info("Received request to get IROs for company with ID: {}", companyId);
        List<IroDto> iroDtos = iroService.getIrosByCompanyId(companyId);
        System.out.println("Iros: " + iroDtos);
        logger.info("Returning {} IROs for company {}", iroDtos.size(), companyId);
        return ResponseEntity.ok(iroDtos);
    }


    @PutMapping("/{id}")

    public ResponseEntity<IroDto> updateIro(@PathVariable Long id, @RequestBody IroDto iroDto) {
        logger.info("Received request to update Iro with ID: {} with data: {}", id, iroDto);
        IroDto updated = iroService.updateIro(id, iroDto);
        logger.info("Updated Iro with ID: {}", updated.getId());
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{id}")

    public ResponseEntity<Void> deleteIro(@PathVariable Long id) {
        logger.info("Received request to delete Iro with ID: {}", id);
        iroService.deleteIro(id);
        logger.info("Deleted Iro with ID: {}", id);
        return ResponseEntity.noContent().build();
    }
}
