package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.DatapointStatus;
import com.example.parabella_csrd_db.dto.mithril.StakeholderProgress;
import com.example.parabella_csrd_db.service.mithril.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Dashboard Analytics Controller for Double Materiality Analysis Monitoring
 *
 * This controller provides real-time analytics and progress monitoring for
 * double materiality analysis projects. It aggregates data from various
 * components of the system to provide comprehensive insights into project
 * status, stakeholder engagement, and assessment completion rates.
 *
 * Core Functionalities:
 * - Monitor overall project progress and completion status
 * - Track stakeholder participation and engagement levels
 * - Provide datapoint completion analytics
 * - Generate real-time progress indicators for management
 *
 * Dashboard Metrics:
 * - Datapoint Status: Overall completion rates for materiality assessments
 * - Stakeholder Progress: Individual stakeholder participation tracking
 * - Topic Coverage: ESRS topic evaluation completion rates
 * - Timeline Analytics: Progress against project milestones
 *
 * Use Cases:
 * - Project management oversight and monitoring
 * - Stakeholder engagement tracking and follow-up
 * - Progress reporting for management and compliance
 * - Identification of bottlenecks and completion gaps
 *
 * Integration Points:
 * - Stakeholder management (participation tracking)
 * - ESRS topic selection (topic completion rates)
 * - IRO evaluation (assessment progress)
 * - Project management (overall project status)
 *
 * API Endpoints:
 * - GET /api/dashboard/datapoints-status: Get overall datapoint completion status
 * - GET /api/dashboard/stakeholders-progress: Get stakeholder participation progress
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("/api/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    @GetMapping("/datapoints-status")
    public ResponseEntity<DatapointStatus> getDatapointStatus(@RequestParam Long companyId) {
        System.out.println(companyId);
        DatapointStatus status = dashboardService.getDatapointStatus(companyId);
        return ResponseEntity.ok(status);
    }

    // DashboardController.java

    @GetMapping("/stakeholders-progress")
    public ResponseEntity<List<StakeholderProgress>> getStakeholdersProgress(@RequestParam Long companyId) {
        List<StakeholderProgress> progressList = dashboardService.getStakeholdersProgress(companyId);
        return ResponseEntity.ok(progressList);
    }

}
