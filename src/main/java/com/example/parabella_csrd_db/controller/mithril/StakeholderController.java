package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopicSelection;
import com.example.parabella_csrd_db.dto.mithril.EsrsTopicSelectionDTO;
import com.example.parabella_csrd_db.dto.mithril.IroDto;
import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
import com.example.parabella_csrd_db.service.mithril.EsrsTopicSelectionService;
import com.example.parabella_csrd_db.service.mithril.IroService;
import com.example.parabella_csrd_db.service.mithril.StakeholderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Stakeholder Management Controller for Double Materiality Analysis
 *
 * This controller manages stakeholder operations within the Parabella CSRD application's
 * Double Materiality Analysis (DMA) module. Stakeholders are key participants in the
 * materiality assessment process who provide input on environmental, social, and governance
 * (ESG) topics relevant to the company's operations.
 *
 * Key Functionalities:
 * - Create and manage stakeholder profiles
 * - Associate stakeholders with companies and projects
 * - Handle stakeholder authentication via unique tokens
 * - Send email invitations to stakeholders for participation
 * - Track stakeholder progress and completion status
 *
 * Stakeholder Types:
 * - Internal stakeholders (employees, management)
 * - External stakeholders (customers, suppliers, investors, NGOs)
 * - Regulatory stakeholders (government agencies, compliance bodies)
 *
 * Security Features:
 * - CORS enabled for specific origins (Google Cloud Storage and localhost)
 * - Token-based stakeholder authentication for secure access
 * - Email verification system for stakeholder participation
 *
 * Integration Points:
 * - Company management system
 * - Project management system
 * - Email notification service
 * - ESRS topic selection process
 * - Value chain object associations
 *
 * API Endpoints:
 * - POST /api/stakeholders: Create new stakeholder
 * - PUT /api/stakeholders/{id}: Update existing stakeholder
 * - GET /api/stakeholders/company/{companyId}: Get stakeholders by company
 * - GET /api/stakeholders/token/{token}: Authenticate stakeholder by token
 * - POST /api/stakeholders/send-stakeholder-emails: Send invitation emails
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024
 */
@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
@RestController
@RequestMapping("/api/stakeholders")
public class StakeholderController {

    @Autowired
    private StakeholderService stakeholderService;

    @Autowired
    private IroService iroService;

    @Autowired // Inject the service that knows how to convert the object
    private EsrsTopicSelectionService esrsTopicSelectionService;
    /**
     * Creates a new stakeholder in the system
     *
     * This endpoint creates a new stakeholder record with the provided information.
     * The stakeholder will be associated with a specific company and project for
     * participation in the double materiality analysis process.
     *
     * Stakeholder Creation Process:
     * 1. Validates stakeholder data (name, email, role, type)
     * 2. Associates stakeholder with specified company and project
     * 3. Generates unique token for stakeholder authentication
     * 4. Sets initial status (typically INVITED)
     * 5. Initializes progress tracking fields
     *
     * Required Fields:
     * - name: Stakeholder's full name
     * - email: Valid email address for communication
     * - role: Stakeholder's role/position
     * - stakeholderType: Type of stakeholder (internal/external/regulatory)
     * - companyId: ID of associated company
     * - projectId: ID of associated project
     *
     * @param stakeholderDTO StakeholderDTO containing stakeholder information
     * @return ResponseEntity with HTTP 201 (CREATED) and the created StakeholderDTO
     *
     * @throws IllegalArgumentException if required fields are missing or invalid
     *
     * @apiNote POST /api/stakeholders
     * @since 1.0
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PostMapping
    public ResponseEntity<StakeholderDTO> createStakeholder(@RequestBody StakeholderDTO stakeholderDTO) {

        System.out.println(stakeholderDTO.getProjectId() + " dfggg" + stakeholderDTO.getName());
        StakeholderDTO createdStakeholder = stakeholderService.createStakeholder(stakeholderDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdStakeholder);
    }

    /**
     * Updates an existing stakeholder's information
     *
     * This endpoint allows modification of stakeholder details while preserving
     * critical system-generated fields like tokens and progress tracking.
     * Updates can include contact information, role changes, and association updates.
     *
     * Updatable Fields:
     * - name: Stakeholder's full name
     * - email: Email address (triggers re-verification if changed)
     * - role: Stakeholder's role/position
     * - stakeholderType: Type classification
     * - companyId: Associated company (with validation)
     * - valueChainObjects: Associated value chain elements
     * - esrsTopics: Associated ESRS topics for evaluation
     *
     * Protected Fields (not updatable via this endpoint):
     * - token: Unique authentication token
     * - status: Participation status (managed by workflow)
     * - completedDatapoints: Progress tracking
     * - totalDatapoints: Progress tracking
     *
     * @param id The unique identifier of the stakeholder to update
     * @param stakeholderDTO StakeholderDTO containing updated information
     * @return ResponseEntity with HTTP 200 (OK) and the updated StakeholderDTO
     *
     * @apiNote PUT /api/stakeholders/{id}
     * @since 1.0
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PutMapping("/{id}")

    public ResponseEntity<StakeholderDTO> updateStakeholder(@PathVariable Long id, @RequestBody StakeholderDTO stakeholderDTO) {
        System.out.println(stakeholderDTO.getProjectId() + " dfggg" + stakeholderDTO.getName());
        StakeholderDTO updatedStakeholder = stakeholderService.updateStakeholder(id, stakeholderDTO);
        return ResponseEntity.ok(updatedStakeholder);
    }

    /**
     * Retrieves all stakeholders associated with a specific company
     *
     * This endpoint returns a list of all stakeholders who are associated with
     * the specified company for double materiality analysis participation.
     * The results include stakeholder details, progress status, and associations.
     *
     * Use Cases:
     * - Display company's stakeholder list in management interface
     * - Generate stakeholder participation reports
     * - Monitor overall stakeholder engagement for a company
     * - Prepare stakeholder communication campaigns
     *
     * Returned Information:
     * - Basic stakeholder details (name, email, role, type)
     * - Participation status (INVITED, IN_PROGRESS, COMPLETED)
     * - Progress metrics (completed vs total datapoints)
     * - Associated value chain objects and ESRS topics
     * - Authentication tokens (for system use)
     *
     * @param companyId The unique identifier of the company
     * @return ResponseEntity with HTTP 200 (OK) and List of StakeholderDTOs

     *
     * @apiNote GET /api/stakeholders/company/{companyId}
     * @since 1.0
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/company/{companyId}")

    public ResponseEntity<List<StakeholderDTO>> getStakeholdersByCompanyId(@PathVariable Long companyId) {
        List<StakeholderDTO> stakeholders = stakeholderService.getStakeholdersByCompanyId(companyId);
        return ResponseEntity.ok(stakeholders);
    }

    /**
     * Authenticates and retrieves stakeholder information using a unique token
     *
     * This endpoint provides token-based authentication for stakeholders to access
     * their personalized materiality assessment interface. Each stakeholder receives
     * a unique token via email invitation that grants access to their specific
     * assessment tasks without requiring traditional user account creation.
     *
     * Token-Based Authentication Flow:
     * 1. Stakeholder receives email invitation with unique token link
     * 2. Clicking the link calls this endpoint with the token
     * 3. System validates token and returns stakeholder information
     * 4. Frontend uses this data to personalize the assessment interface
     *
     * Security Features:
     * - Tokens are unique and non-guessable (UUID-based)
     * - Tokens are tied to specific stakeholder records
     * - No password required for stakeholder access
     * - Tokens can be regenerated if compromised
     *
     * Use Cases:
     * - Stakeholder login via email invitation link
     * - Validate stakeholder identity for assessment participation
     * - Retrieve personalized assessment configuration
     * - Track stakeholder engagement and progress
     *
     * @param token The unique authentication token for the stakeholder
     * @return ResponseEntity with HTTP 200 (OK) and the authenticated StakeholderDTO
     *

     * @throws SecurityException if token is invalid or expired
     *
     * @apiNote GET /api/stakeholders/token/{token}
     * @since 1.0
     */
    @GetMapping("/token/{token}")
    @PreAuthorize("permitAll()")
    public ResponseEntity<StakeholderDTO> getStakeholderByToken(@PathVariable String token) {
        StakeholderDTO stakeholderDTO = stakeholderService.getStakeholderByToken(token);
        return ResponseEntity.ok(stakeholderDTO);
    }

    /**
     * Sends email invitations to multiple stakeholders for materiality assessment participation
     *
     * This endpoint processes a batch of stakeholders and sends personalized email invitations
     * containing unique access tokens for the double materiality analysis process.
     * Each stakeholder receives a customized email with their specific assessment link.
     *
     * Email Invitation Process:
     * 1. Validates stakeholder data and ensures unique tokens exist
     * 2. Generates personalized email content for each stakeholder
     * 3. Creates secure access links with embedded tokens
     * 4. Sends emails via configured email service (SMTP/SendGrid/etc.)
     * 5. Updates stakeholder status to INVITED
     * 6. Logs email delivery status for tracking
     *
     * Email Content Includes:
     * - Personalized greeting with stakeholder name
     * - Company and project context information
     * - Explanation of double materiality analysis importance
     * - Unique access link with embedded token
     * - Instructions for completing the assessment
     * - Contact information for support
     *
     * Security Considerations:
     * - Tokens are unique and non-transferable
     * - Email addresses are validated before sending
     * - Delivery failures are logged for follow-up
     * - Rate limiting prevents email spam
     *
     * Batch Processing Features:
     * - Handles multiple stakeholders in single request
     * - Continues processing if individual emails fail
     * - Returns overall success status
     * - Provides detailed logging for troubleshooting
     *
     * @param stakeholders List of StakeholderDTOs to send invitations to
     * @return ResponseEntity with HTTP 200 (OK) indicating batch processing completion
     *
     *
     * @apiNote POST /api/stakeholders/send-stakeholder-emails
     * @since 1.0
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PostMapping("/send-stakeholder-emails")

    public ResponseEntity<Void> sendStakeholderEmails(@RequestBody List<StakeholderDTO> stakeholders) {
        System.out.println(stakeholders);
        stakeholderService.sendStakeholderEmails(stakeholders);
        return ResponseEntity.ok().build();
    }


    @DeleteMapping("/{id}")

    public ResponseEntity<Void> deleteStakeholder(@PathVariable Long id) {
        stakeholderService.deleteStakeholder(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Retrieves all IROs (Impact, Risk, Opportunity) assigned to a specific stakeholder.
     *
     * This endpoint fetches IROs directly linked to the stakeholder. These are the
     * specific items the stakeholder is expected to evaluate or provide input on
     * as part of the double materiality analysis.
     *
     * Use Cases:
     * - Populate the stakeholder's personalized assessment interface with their assigned IROs.
     * - Allow stakeholders to view the scope of their evaluation tasks.
     * - Used by the frontend to fetch data for the stakeholder's IRO evaluation forms.
     *
     * @param stakeholderId The unique identifier of the stakeholder.
     * @return ResponseEntity with HTTP 200 (OK) and a List of IroDto objects.
     *         Returns an empty list if no IROs are assigned or the stakeholder is not found.
     *         (Consider 404 if stakeholder not found, but service layer currently throws RuntimeException)
     *
     * @apiNote GET /api/stakeholders/{stakeholderId}/iros
     * @since 1.1
     */
    @GetMapping("/{stakeholderId}/iros")

    public ResponseEntity<List<IroDto>> getIrosByStakeholderId(@PathVariable Long stakeholderId) {
        List<IroDto> iros = iroService.getIrosByStakeholderId(stakeholderId);
        return ResponseEntity.ok(iros);
    }

    /**
     * Retrieves all unique ESRS Topic Selections relevant to a specific stakeholder.
     *
     * The relevance is determined by the ESRS Topic Selections linked to the IROs
     * assigned to that stakeholder. This helps understand which broader ESRS topics
     * the stakeholder's input will contribute to.
     *
     * Use Cases:
     * - Provide context to the stakeholder about the ESRS topics their IRO evaluations relate to.
     * - For administrative views, to understand which ESRS topics a stakeholder is covering via IROs.
     * - Can be used by the frontend to structure or filter the display of IROs.
     *
     * @param stakeholderId The unique identifier of the stakeholder.
     * @return ResponseEntity with HTTP 200 (OK) and a List of EsrsTopicSelection entities.
     *         (Consider creating an EsrsTopicSelectionDTO for better API contract).
     *         Returns an empty list if no relevant selections are found or stakeholder not found.
     *
     * @apiNote GET /api/stakeholders/{stakeholderId}/topic-selections
     * @since 1.1
     */
    @GetMapping("/{stakeholderId}/topic-selections")

    public ResponseEntity<List<EsrsTopicSelectionDTO>> getEsrsTopicSelectionsByStakeholderId(@PathVariable Long stakeholderId) {
        // 1. Fetch the raw entity list from the service
        List<EsrsTopicSelection> selections = iroService.getEsrsTopicSelectionsByStakeholderId(stakeholderId);

        // 2. Use the centralized service to map the list of entities to a list of DTOs
        List<EsrsTopicSelectionDTO> selectionDtos = selections.stream()
                .map(esrsTopicSelectionService::convertToDto) // Use the centralized converter
                .collect(Collectors.toList());

        // 3. Return the list of DTOs
        return ResponseEntity.ok(selectionDtos);
    }


}