package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.ProjectDTO;
import com.example.parabella_csrd_db.service.mithril.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * Project Management Controller for Double Materiality Analysis
 *
 * This controller manages project entities within the Parabella CSRD application's
 * Double Materiality Analysis (DMA) module. Projects serve as the primary organizational
 * unit for conducting materiality assessments, encompassing all related companies,
 * stakeholders, and analysis activities.
 *
 * Core Functionalities:
 * - Create and manage DMA projects
 * - Associate projects with users (project owners)
 * - Link projects to companies or company groups
 * - Handle project lifecycle management (create, read, update, delete)
 * - Support project duplication for template-based workflows
 *
 * Project Types:
 * - Company Projects: Focus on individual company materiality analysis
 * - Company Group Projects: Multi-entity materiality analysis for corporate groups
 * - Template Projects: Reusable project configurations
 *
 * Project Data Management:
 * - Basic project information (name, description, type)
 * - User ownership and access control
 * - Company/company group associations
 * - Creation and modification timestamps
 * - Project status and progress tracking
 *
 * Integration Points:
 * - User management system (project ownership)
 * - Company management (single company projects)
 * - Company group management (multi-entity projects)
 * - Stakeholder management (project participants)
 * - ESRS topic selection (project-specific topics)
 * - Audit trail system (project change tracking)
 *
 * Business Context:
 * - Supports CSRD compliance through structured materiality analysis
 * - Enables project-based organization of ESG assessments
 * - Facilitates stakeholder collaboration within project scope
 * - Provides template functionality for standardized assessments
 *
 * API Endpoints:
 * - GET /api/projects/user/{userId}: Get projects by user
 * - GET /api/projects/{projectId}: Get specific project
 * - POST /api/projects/createProject: Create new project
 * - PUT /api/projects/{projectId}: Update existing project
 * - DELETE /api/projects/{projectId}: Delete project
 * - POST /api/projects/copy/{projectId}: Duplicate project
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("/api/projects")
public class ProjectController {

    @Autowired
    private ProjectService projectService;


    /**
     * Retrieves all projects associated with a specific user
     *
     * This endpoint returns a list of all projects where the specified user
     * is the owner or has access rights. This is typically used to populate
     * the user's project dashboard and enable project selection.
     *
     * Use Cases:
     * - Display user's project list in the main dashboard
     * - Enable project switching in the navigation interface
     * - Generate user-specific project reports
     * - Validate user access to project resources
     *
     * Returned Information:
     * - Project basic details (ID, name, description, type)
     * - Project creation and modification timestamps
     * - Associated company or company group information
     * - Project status and progress indicators
     *
     * @param userId The unique identifier of the user
     * @return ResponseEntity with HTTP 200 (OK) and List of ProjectDTOs owned by the user
     *
     * @throws EntityNotFoundException if user with given ID doesn't exist
     *
     * @apiNote GET /api/projects/user/{userId}
     * @since 1.0
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('dma.read')")
    public ResponseEntity<List<ProjectDTO>> getProjectsByUser(@PathVariable Long userId) {
        List<ProjectDTO> projects = projectService.findProjectsByUserId(userId);
        return ResponseEntity.ok(projects);
    }


    /**
     * Retrieves a specific project by its unique identifier
     *
     * This endpoint returns detailed information about a single project,
     * including all associated entities and current status. This is typically
     * used when a user selects a project to work on or view its details.
     *
     * Use Cases:
     * - Load project details for the main project interface
     * - Validate project existence before performing operations
     * - Retrieve project configuration for stakeholder invitations
     * - Display project information in reports and exports
     *
     * Returned Information:
     * - Complete project details (name, description, type, dates)
     * - Associated user (project owner) information
     * - Linked company or company group details
     * - Project status and progress metrics
     * - Creation and modification audit information
     *
     * @param projectId The unique identifier of the project
     * @return ResponseEntity with HTTP 200 (OK) and ProjectDTO if found,
     *         HTTP 404 (NOT_FOUND) if project doesn't exist
     *
     * @apiNote GET /api/projects/{projectId}
     * @since 1.0
     */
    @GetMapping("/{projectId}")
    @PreAuthorize("hasAuthority('dma.read')")
    public ResponseEntity<ProjectDTO> getProjectById(@PathVariable Long projectId) {
        Optional<ProjectDTO> project = projectService.findProjectDTOById(projectId);
        return project.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Creates a new double materiality analysis project
     *
     * This endpoint creates a new project with the provided configuration.
     * The project serves as the organizational container for all materiality
     * analysis activities, including stakeholder management, topic selection,
     * and impact/risk/opportunity evaluation.
     *
     * Project Creation Process:
     * 1. Validates project data (name, description, type)
     * 2. Associates project with the specified user (owner)
     * 3. Links project to company or company group if specified
     * 4. Sets creation timestamp and initial status
     * 5. Initializes project-specific configurations
     * 6. Creates audit trail entry for project creation
     *
     * Required Fields:
     * - projectName: Descriptive name for the project
     * - projectDescription: Detailed description of project scope
     * - projectType: Type of project (COMPANY or COMPANY_GROUP)
     * - userId: ID of the user who will own the project
     *
     * Optional Fields:
     * - companyId: ID of associated company (for single-company projects)
     * - companyGroupId: ID of associated company group (for multi-entity projects)
     *
     * @param projectDTO ProjectDTO containing project configuration data
     * @return ResponseEntity with HTTP 201 (CREATED) and the created ProjectDTO
     *
     * @throws IllegalArgumentException if required fields are missing or invalid
     * @throws EntityNotFoundException if referenced user, company, or company group doesn't exist
     * @throws ConstraintViolationException if project name already exists for the user
     *
     * @apiNote POST /api/projects/createProject
     * @since 1.0
     */
    @PostMapping("/createProject")
    @PreAuthorize("hasAuthority('dma.create')")
    public ResponseEntity<ProjectDTO> createProject(@RequestBody ProjectDTO projectDTO) {
        ProjectDTO savedProject = projectService.createProject(projectDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedProject);
    }

    /**
     * Updates an existing project's information
     *
     * This endpoint allows modification of project details while preserving
     * critical system-generated fields and maintaining data integrity.
     * Updates can include basic information, associations, and configuration changes.
     *
     * Updatable Fields:
     * - projectName: Project name (must remain unique for the user)
     * - projectDescription: Detailed description of project scope
     * - projectType: Type classification (with validation)
     * - companyId: Associated company (for single-company projects)
     * - companyGroupId: Associated company group (for multi-entity projects)
     *
     * Protected Fields (not updatable via this endpoint):
     * - id: Project unique identifier
     * - userId: Project owner (managed separately)
     * - createdAt: Creation timestamp
     * - Audit trail information
     *
     * Update Process:
     * 1. Validates project existence and user permissions
     * 2. Validates updated data for consistency and constraints
     * 3. Preserves system-generated and protected fields
     * 4. Updates modifiable fields with new values
     * 5. Creates audit trail entry for the update
     * 6. Returns updated project information
     *
     * @param projectId The unique identifier of the project to update
     * @param projectDTO ProjectDTO containing updated project information
     * @return ResponseEntity with HTTP 200 (OK) and the updated ProjectDTO
     *
     * @throws EntityNotFoundException if project with given ID doesn't exist
     * @throws IllegalArgumentException if update data is invalid
     * @throws ConstraintViolationException if updated name conflicts with existing projects
     * @throws SecurityException if user doesn't have permission to update the project
     *
     * @apiNote PUT /api/projects/{projectId}
     * @since 1.0
     */
    @PutMapping("/{projectId}")
    @PreAuthorize("hasAuthority('dma.edit')")
    public ResponseEntity<ProjectDTO> updateProject(
            @PathVariable Long projectId,
            @RequestBody ProjectDTO projectDTO) {

        projectDTO.setId(projectId);
        ProjectDTO updatedProject = projectService.updateProject(projectDTO);
        return ResponseEntity.ok(updatedProject);
    }

    /**
     * Deletes a project and all associated data
     *
     * This endpoint permanently removes a project from the system along with
     * all related entities and data. This is a destructive operation that
     * cannot be undone, so it should be used with caution.
     *
     * Deletion Process:
     * 1. Validates project existence and user permissions
     * 2. Checks for dependent entities and handles cascading deletes
     * 3. Removes associated stakeholders and their data
     * 4. Deletes ESRS topic selections and evaluations
     * 5. Removes project-specific audit trail entries
     * 6. Finally deletes the project entity itself
     *
     * Cascading Deletions Include:
     * - All project stakeholders and their tokens
     * - ESRS topic selections for the project
     * - IRO (Impact/Risk/Opportunity) evaluations
     * - Project-specific value chain associations
     * - Related audit trail entries
     *
     * Security Considerations:
     * - Only project owners can delete their projects
     * - Confirmation should be required in the frontend
     * - Consider soft delete for audit purposes
     * - Backup critical data before deletion
     *
     * @param projectId The unique identifier of the project to delete
     * @return ResponseEntity with HTTP 204 (NO_CONTENT) indicating successful deletion
     *
     * @throws EntityNotFoundException if project with given ID doesn't exist
     * @throws SecurityException if user doesn't have permission to delete the project
     * @throws DataIntegrityViolationException if deletion would violate data constraints
     *
     * @apiNote DELETE /api/projects/{projectId}
     * @since 1.0
     */
    @DeleteMapping("/{projectId}")
    @PreAuthorize("hasAuthority('dma.delete')")
    public ResponseEntity<Void> deleteProject(@PathVariable Long projectId) {
        projectService.deleteProject(projectId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Creates a copy of an existing project with all its configurations
     *
     * This endpoint duplicates an existing project, creating a new project
     * with the same configuration but as a separate entity. This is useful
     * for creating template-based projects or reusing successful project setups.
     *
     * Copying Process:
     * 1. Validates source project existence and user permissions
     * 2. Creates new project with copied basic information
     * 3. Duplicates project-specific configurations
     * 4. Copies ESRS topic selections (without evaluations)
     * 5. Replicates value chain object associations
     * 6. Generates new unique identifiers for all copied entities
     *
     * What Gets Copied:
     * - Project basic information (name with "Copy" suffix, description, type)
     * - Company or company group associations
     * - ESRS topic selections (reset to initial state)
     * - Value chain object associations
     * - Project configuration settings
     *
     * What Doesn't Get Copied:
     * - Stakeholder data (stakeholders are project-specific)
     * - IRO evaluations (evaluations are instance-specific)
     * - Progress tracking data
     * - Audit trail entries
     * - Creation/modification timestamps (new timestamps are generated)
     *
     * Use Cases:
     * - Create template projects for standardized assessments
     * - Duplicate successful project configurations
     * - Start new projects based on previous setups
     * - Create backup copies before major changes
     *
     * @param projectId The unique identifier of the project to copy
     * @return ResponseEntity with HTTP 201 (CREATED) and the newly created ProjectDTO
     *
     * @throws EntityNotFoundException if source project with given ID doesn't exist
     * @throws SecurityException if user doesn't have permission to access the source project
     * @throws IllegalStateException if project cannot be copied due to its current state
     *
     * @apiNote POST /api/projects/copy/{projectId}
     * @since 1.0
     */
    @PostMapping("/copy/{projectId}")
    @PreAuthorize("hasAuthority('dma.create')")
    public ResponseEntity<ProjectDTO> copyProject(@PathVariable Long projectId) {
        ProjectDTO copiedProject = projectService.copyProject(projectId);
        return ResponseEntity.status(HttpStatus.CREATED).body(copiedProject);
    }

}
