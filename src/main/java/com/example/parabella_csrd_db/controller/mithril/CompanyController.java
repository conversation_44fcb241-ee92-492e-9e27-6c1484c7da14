package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.CompanyDTO;
import com.example.parabella_csrd_db.service.mithril.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Company Management Controller for CSRD Double Materiality Analysis
 *
 * This controller manages company entities within the Parabella CSRD application.
 * Companies are central to the double materiality analysis process, serving as
 * the primary subjects for ESG (Environmental, Social, Governance) assessment
 * and CSRD compliance reporting.
 *
 * Core Functionalities:
 * - Create and manage company profiles
 * - Associate companies with company groups (for multi-entity organizations)
 * - Link companies to materiality analysis projects
 * - Manage company-specific stakeholder relationships
 * - Handle company value chain object associations
 *
 * Company Data Management:
 * - Basic company information (name, address, VAT, industry)
 * - Operational metrics (number of employees, revenues)
 * - Organizational structure (parent/subsidiary relationships)
 * - Regulatory compliance data
 *
 * Integration Points:
 * - Project management system (double materiality projects)
 * - Stakeholder management (company-specific stakeholders)
 * - Value chain analysis (upstream/downstream relationships)
 * - ESRS topic selection (company-specific materiality topics)
 * - Company group management (multi-entity structures)
 *
 * Business Context:
 * - Supports CSRD (Corporate Sustainability Reporting Directive) compliance
 * - Enables double materiality analysis at company level
 * - Facilitates stakeholder engagement per company
 * - Manages ESG data collection and reporting
 *
 * API Endpoints:
 * - POST /api/companies: Create new company
 * - PUT /api/companies/{id}: Update existing company
 * - GET /api/companies/{id}: Retrieve company by ID
 * - DELETE /api/companies/{id}: Delete company
 * - GET /api/companies/by-group/{groupId}: Get companies by group
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("/api/companies")
public class CompanyController {

    @Autowired
    private CompanyService companyService;

    /**
     * Create a new Company.
     *
     * @param companyDTO the CompanyDTO containing company data
     * @return the created CompanyDTO
     */
    @PostMapping

    public CompanyDTO createCompany(@RequestBody CompanyDTO companyDTO) {
        return companyService.createCompany(companyDTO);
    }

    /**
     * Update an existing Company.
     *
     * @param id         the ID of the Company to update
     * @param companyDTO the CompanyDTO containing updated data
     * @return the updated CompanyDTO
     */
    @PutMapping("/{id}")

    public CompanyDTO updateCompany(@PathVariable Long id, @RequestBody CompanyDTO companyDTO) {
        return companyService.updateCompany(id , companyDTO);
    }

    /**
     * Get a Company by its ID.
     *
     * @param id the ID of the Company
     * @return the CompanyDTO
     */
    @GetMapping("/{id}")

    public CompanyDTO getCompany(@PathVariable Long id ) {
        return companyService.getCompany(id);
    }

    /**
     * Delete a Company by its ID.
     *
     * @param id the ID of the Company to delete
     */
    @DeleteMapping("/{id}")

    public void deleteCompany(@PathVariable Long id) {
        companyService.deleteCompany(id);
    }

    /**
     * Get all Companies associated with a Company Group ID.
     *
     * @param groupId the ID of the Company Group
     * @return List of CompanyDTOs
     */
    @GetMapping("/by-group/{groupId}")

    public List<CompanyDTO> getCompaniesByGroupId(@PathVariable Long groupId) {
        return companyService.getCompaniesByGroupId(groupId);
    }
}
