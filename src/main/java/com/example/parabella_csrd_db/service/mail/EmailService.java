package com.example.parabella_csrd_db.service.mail;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;


/**
 * TODO: Refactor the whole email service with an EmailManager
 * Service class responsible for sending emails.
 * It supports sending both plain text and HTML formatted emails.
 * Additionally, it provides functionality for sending emails with personalized links
 * for stakeholder invitations using tokens.

 * The email templates are loaded from classpath resources, and the service
 * utilizes Spring's JavaMailSender for email operations.

 * The frontend URL for generating links is injected from application properties.
 */
@Service
public class EmailService {

    //@Value("${app.frontend.url}")
    private String frontendUrl = "http://localhost:5173";

    @Autowired
    private JavaMailSender emailSender;

    @Value("${app.security.token.invite-expiry-hours:24}") // Make expiry configurable
    private int tokenExpiryHours;

    /**
     * Sends an email with plain text or HTML content.
     *
     * @param to the recipient's email address
     * @param subject the subject of the email
     * @param content the content of the email, which can be plain text or HTML
     * @throws RuntimeException if there's an error sending the email
     */
    public void sendPlainOrHtmlEmail(String to, String subject, String content) {
        try {
            MimeMessage message = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(
                    message,
                    MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                    StandardCharsets.UTF_8.name()
            );

            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, false);

            emailSender.send(message);

        } catch (MessagingException e) {
            // Log error, rethrow as needed
            throw new RuntimeException("Failed to send email", e);
        }
    }

    /**
     * Sends an HTML formatted email with a token link for stakeholder invitation.
     *
     * @param to the recipient's email address
     * @param token a unique token to include in the email link
     * @param stakeholderName the name of the stakeholder, used to personalize the email
     * @throws MessagingException if there's an error creating or sending the email
     */
    public void sendHtmlEmail(String to, String token, String stakeholderName) throws MessagingException {
        MimeMessage message = emailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(
                message,
                MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                StandardCharsets.UTF_8.name()
        );

        String htmlTemplate = loadStakeholderHtmlTemplate();

        String[] nameParts = stakeholderName != null ? stakeholderName.split(" ") : new String[]{""};
        String firstName = nameParts.length > 0 ? nameParts[0] : "";
        String lastName = nameParts.length > 1 ? nameParts[1] : "";

        String htmlContent = htmlTemplate
                .replace("{{FNAME}}", firstName)
                .replace("{{LNAME}}", lastName)
                .replace("{{LINK}}", frontendUrl + "/stakeholder/main/" + token);

        System.out.println(htmlContent);

        helper.setTo(to);
        helper.setSubject("Invitation to Double Materiality Analysis");
        helper.setText(htmlContent, true);

        emailSender.send(message);
    }

    /**
     * Sends a user invitation email with a link to set their password.
     * The link uses the provided token and points to the frontend's reset password page.
     *
     * @param to the recipient's email address
     * @param token the unique invitation token
     * @throws MessagingException if there's an error creating or sending the email
     */
    public void sendUserInvitationEmail(String to, String token) throws MessagingException {
        MimeMessage message = emailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(
                message,
                MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                StandardCharsets.UTF_8.name()
        );

        String htmlTemplate = loadHtmlTemplate("emailTemplates/user_invitation_template.html");

        // This URL must match your frontend route for setting/resetting a password
        String invitationUrl = frontendUrl + "/pages/authentication/setpassword/" + token;

        String htmlContent = htmlTemplate.replace("{{LINK}}", invitationUrl);

        helper.setTo(to);
        helper.setSubject("You're invited to join our platform!");
        helper.setText(htmlContent, true);

        emailSender.send(message);
    }

    // Generic method to load any template
    private String loadHtmlTemplate(String templatePath) {
        try {
            ClassPathResource resource = new ClassPathResource(templatePath);
            Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
            return FileCopyUtils.copyToString(reader);
        } catch (IOException e) {
            throw new RuntimeException("Failed to load email template: " + e.getMessage(), e);
        }
    }

    // You can refactor your old loadHtmlTemplate to use the new one
    private String loadStakeholderHtmlTemplate() {
        return loadHtmlTemplate("emailTemplates/stakeholder_notification.html");
    }
}
