package com.example.parabella_csrd_db.service.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.CsrdProject;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.displayContent.CsrdSubtopic;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.userContent.CsrdDatapointResponse;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.userContent.CsrdSubtopicGeneratedText;
import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdDatapointResponseRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdProjectRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdSubtopicGeneratedTextRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdSubtopicRepository;
import com.example.parabella_csrd_db.database.vectordatabase.model.EsrsDatapoint;
import com.example.parabella_csrd_db.database.vectordatabase.repository.EsrsDatapointRepository;
import com.example.parabella_csrd_db.dto.elessar.userContent.CsrdSubtopicGeneratedTextDTO;
import com.example.parabella_csrd_db.service.ai.PromptingService;
import com.example.parabella_csrd_db.service.chat_bot.OpenAIService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CsrdSubtopicGenerationService {

    private final CsrdSubtopicGeneratedTextRepository generatedTextRepository;
    private final CsrdDatapointResponseRepository datapointResponseRepository;
    private final CsrdProjectRepository csrdProjectRepository;
    private final CsrdSubtopicRepository csrdSubtopicRepository;
    private final EsrsDatapointRepository esrsDatapointRepository;
    private final PromptingService promptingService; // ✅ ADD
    private final OpenAIService openAIService;       // ✅ ADD

    @Autowired
    public CsrdSubtopicGenerationService(
            CsrdSubtopicGeneratedTextRepository generatedTextRepository,
            CsrdDatapointResponseRepository datapointResponseRepository,
            CsrdProjectRepository csrdProjectRepository,
            CsrdSubtopicRepository csrdSubtopicRepository,
            EsrsDatapointRepository esrsDatapointRepository,
            PromptingService promptingService, // ✅ ADD
            OpenAIService openAIService) {       // ✅ ADD
        this.generatedTextRepository = generatedTextRepository;
        this.datapointResponseRepository = datapointResponseRepository;
        this.csrdProjectRepository = csrdProjectRepository;
        this.csrdSubtopicRepository = csrdSubtopicRepository;
        this.esrsDatapointRepository = esrsDatapointRepository;
        this.promptingService = promptingService; // ✅ ADD
        this.openAIService = openAIService;       // ✅ ADD
    }
    /**
     * Triggers the AI-powered generation of a synthesized, audit-aligned text for a specific subtopic.
     * It gathers all relevant datapoint responses, uses the PromptingService to create a prompt,
     * calls the OpenAIService, and saves the final narrative.
     */
    @Transactional
    public CsrdSubtopicGeneratedTextDTO triggerSubtopicTextGeneration(Long projectId, Long subtopicId, Integer reportingYear) {
        CsrdProject project = csrdProjectRepository.findById(projectId)
                .orElseThrow(() -> new EntityNotFoundException("CSRD Project not found with ID: " + projectId));
        CsrdSubtopic subtopic = csrdSubtopicRepository.findById(subtopicId)
                .orElseThrow(() -> new EntityNotFoundException("CSRD Subtopic not found with ID: " + subtopicId));

        Integer year = Optional.ofNullable(reportingYear).orElse(LocalDateTime.now().getYear());

        CsrdSubtopicGeneratedText generatedText = generatedTextRepository
                .findByCsrdProjectIdAndCsrdSubtopicIdAndReportingYear(projectId, subtopicId, year)
                .orElse(CsrdSubtopicGeneratedText.builder()
                        .csrdProject(project)
                        .csrdSubtopic(subtopic)
                        .reportingYear(year)
                        .build());

        generatedText.setGenerationStatus(CsrdSubtopicGeneratedText.GenerationStatus.PENDING);
        generatedTextRepository.save(generatedText);

        try {
            // Step 1: Gather the raw responses. This method is now used to collect input for the AI.
            String rawAggregatedText = aggregateDatapointResponses(projectId, subtopic.getCsrdSubtopicId(), year);

            if (rawAggregatedText.startsWith("No ESRS datapoints found") || rawAggregatedText.startsWith("No responses submitted")) {
                generatedText.setGeneratedText(rawAggregatedText); // Use the info message directly
                generatedText.setGenerationStatus(CsrdSubtopicGeneratedText.GenerationStatus.COMPLETED);
            } else {
                // Step 2: Build the specialized prompt for aggregation.
                // Assuming project has a getCompanyName() method or similar. If not, you can pass a placeholder.
                String companyName = project.getProjectName(); // Or another field representing the company
                String prompt = promptingService.buildSubtopicAggregationPrompt(
                        subtopic.getCsrdSubtopicId(),
                        companyName,
                        rawAggregatedText
                );

                // Step 3: Call the AI service to get the synthesized, audit-aligned text.
                // Note: getCompletionForReason works fine as it just sends a prompt.
                String synthesizedText = openAIService.getCompletionForReason(prompt);

                // Step 4: Save the AI-generated result.
                generatedText.setGeneratedText(synthesizedText);
                generatedText.setGenerationStatus(CsrdSubtopicGeneratedText.GenerationStatus.COMPLETED);
            }

        } catch (Exception e) {
            generatedText.setGeneratedText("Error during AI-powered text generation: " + e.getMessage());
            generatedText.setGenerationStatus(CsrdSubtopicGeneratedText.GenerationStatus.FAILED);
            System.err.println("Error generating AI subtopic text for project " + projectId + ", subtopic " + subtopicId + ": " + e.getMessage());
            e.printStackTrace();
        } finally {
            generatedText = generatedTextRepository.save(generatedText); // Save final status and text
        }

        return mapToDTO(generatedText);
    }

    @Transactional(readOnly = true)
    public CsrdSubtopicGeneratedTextDTO getGeneratedSubtopicText(Long projectId, Long subtopicId, Integer reportingYear) {
        Integer year = Optional.ofNullable(reportingYear).orElse(LocalDateTime.now().getYear());
        CsrdSubtopicGeneratedText generatedText = generatedTextRepository
                .findByCsrdProjectIdAndCsrdSubtopicIdAndReportingYear(projectId, subtopicId, year)
                .orElseThrow(() -> new EntityNotFoundException(
                        "Generated text not found for Project ID: " + projectId +
                                ", Subtopic ID: " + subtopicId + ", Year: " + year));
        return mapToDTO(generatedText);
    }

    /**
     * Gathers and concatenates raw data responses for all EsrsDatapoints associated with a given CSRD Subtopic ID.
     * This method's output now serves as the INPUT for the AI synthesis prompt.
     */
    private String aggregateDatapointResponses(Long projectId, String csrdSubtopicId, Integer reportingYear) {
        // This method's logic remains the same, as it's excellent for gathering the necessary data.
        List<EsrsDatapoint> esrsDatapoints = esrsDatapointRepository.findByDisclosureRequirement(csrdSubtopicId);

        if (esrsDatapoints.isEmpty()) {
            return "No ESRS datapoints found for disclosure requirement: " + csrdSubtopicId;
        }

        List<CsrdDatapointResponse> relevantResponses = esrsDatapoints.stream()
                .map(datapoint -> datapointResponseRepository.findByCsrdProjectIdAndEsrsDatapointIdAndReportingYear(
                        projectId, datapoint.getId(), reportingYear
                ))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .peek(response -> {
                    esrsDatapointRepository.findById(response.getEsrsDatapointId())
                            .ifPresent(response::setEsrsDatapoint);
                })
                .filter(response -> response.getEsrsDatapoint() != null)
                .sorted(Comparator.comparing(resp -> resp.getEsrsDatapoint().getDisclosureRequirement()))
                .toList();

        if (relevantResponses.isEmpty()) {
            return "No responses submitted for " + csrdSubtopicId + " for project " + projectId + " in year " + reportingYear + ".";
        }

        StringBuilder sb = new StringBuilder();
        // This header is less important now as it's for the AI, but still useful for context.
        sb.append("Datapoint responses for: ").append(csrdSubtopicId).append("\n\n");

        for (CsrdDatapointResponse response : relevantResponses) {
            sb.append("Datapoint: ").append(response.getEsrsDatapoint().getDisclosureRequirement()).append("\n");
            sb.append("Response: ").append(response.getDataResponse() != null ? response.getDataResponse() : "[No Response]").append("\n");
            sb.append("Source: ").append(response.getDataResponseSource()).append("\n\n");
        }
        return sb.toString();
    }

    private CsrdSubtopicGeneratedTextDTO mapToDTO(CsrdSubtopicGeneratedText entity) {
        return CsrdSubtopicGeneratedTextDTO.builder()
                .id(entity.getId())
                .csrdSubtopicId(entity.getCsrdSubtopic().getId())
                .csrdProjectId(entity.getCsrdProject().getId())
                .generatedText(entity.getGeneratedText())
                .reportingYear(entity.getReportingYear())
                .generationStatus(entity.getGenerationStatus())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
}