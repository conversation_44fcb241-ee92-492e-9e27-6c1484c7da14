package com.example.parabella_csrd_db.service.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.RefreshToken;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RefreshTokenRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.security.jwt.exception.TokenRefreshException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing refresh tokens.
 * Handles creation, validation, and cleanup of refresh tokens.
 */
@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class RefreshTokenService {
    
    @Value("${parabella_csrd_db.jwtRefreshExpirationMs:604800000}") // 7 days default
    private Long refreshTokenDurationMs;
    
    @Value("${parabella_csrd_db.maxActiveRefreshTokensPerUser:5}")
    private int maxActiveRefreshTokensPerUser;
    
    private final RefreshTokenRepository refreshTokenRepository;
    private final UserRepository userRepository;
    
    // Use SecureRandom for better cryptographic security
    private final SecureRandom secureRandom = new SecureRandom();
    
    /**
     * Creates a new refresh token for the user.
     * Revokes old tokens if the user has too many active tokens.
     */
    public RefreshToken createRefreshToken(Long userId, String deviceInfo, String ipAddress) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        // Check if user has too many active tokens
        long activeTokenCount = refreshTokenRepository.countActiveTokensByUser(user, Instant.now());
        if (activeTokenCount >= maxActiveRefreshTokensPerUser) {
            // Delete expired tokens first
            refreshTokenRepository.deleteExpiredTokens(Instant.now());
            
            // If still too many, revoke the oldest ones
            activeTokenCount = refreshTokenRepository.countActiveTokensByUser(user, Instant.now());
            if (activeTokenCount >= maxActiveRefreshTokensPerUser) {
                // Find and delete the oldest tokens for this user
                refreshTokenRepository.revokeAllUserTokens(user);
            }
        }
        
        RefreshToken refreshToken = new RefreshToken();
        refreshToken.setUser(user);
        refreshToken.setExpiryDate(Instant.now().plusMillis(refreshTokenDurationMs));
        
        // Generate cryptographically secure token
        refreshToken.setToken(generateSecureToken());
        
        refreshToken.setDeviceInfo(deviceInfo != null ? deviceInfo.substring(0, Math.min(deviceInfo.length(), 255)) : null);
        refreshToken.setIpAddress(ipAddress != null ? ipAddress.substring(0, Math.min(ipAddress.length(), 45)) : null);
        refreshToken.setRevoked(false);
        
        return refreshTokenRepository.save(refreshToken);
    }
    
    /**
     * Generates a cryptographically secure token string.
     */
    private String generateSecureToken() {
        // Generate 32 random bytes and encode as base64 for a secure token
        byte[] randomBytes = new byte[32];
        secureRandom.nextBytes(randomBytes);
        return java.util.Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }
    
    /**
     * Finds a refresh token by its token string.
     * Returns empty if token is null, empty, or revoked.
     */
    public Optional<RefreshToken> findByToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("Attempted to find refresh token with null or empty token string");
            return Optional.empty();
        }
        
        Optional<RefreshToken> tokenOptional = refreshTokenRepository.findByToken(token);
        
        // Additional check for revoked tokens
        if (tokenOptional.isPresent() && tokenOptional.get().isRevoked()) {
            log.warn("Attempted to use revoked refresh token");
            return Optional.empty();
        }
        
        return tokenOptional;
    }
    
    /**
     * Verifies that a refresh token is valid and not expired.
     */
    public RefreshToken verifyExpiration(RefreshToken token) {
        if (token.isExpired()) {
            refreshTokenRepository.delete(token);
            throw new TokenRefreshException(token.getToken(), 
                "Refresh token has expired. Please sign in again.");
        }
        
        if (token.isRevoked()) {
            throw new TokenRefreshException(token.getToken(), 
                "Refresh token has been revoked. Please sign in again.");
        }
        
        return token;
    }
    
    /**
     * Revokes all refresh tokens for a user.
     */
    @Transactional
    public int revokeAllUserTokens(Long userId) {
        return userRepository.findById(userId)
                .map(refreshTokenRepository::revokeAllUserTokens)
                .orElse(0);
    }
    
    /**
     * Deletes a specific refresh token.
     */
    @Transactional
    public void deleteByToken(String token) {
        refreshTokenRepository.findByToken(token)
                .ifPresent(refreshTokenRepository::delete);
    }
    
    /**
     * Gets the count of active tokens for a user.
     */
    public long getActiveTokenCount(Long userId) {
        return userRepository.findById(userId)
                .map(user -> refreshTokenRepository.countActiveTokensByUser(user, Instant.now()))
                .orElse(0L);
    }
    
    /**
     * Scheduled task to clean up expired tokens.
     * Runs every day at 2 AM.
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredTokens() {
        int deletedCount = refreshTokenRepository.deleteExpiredTokens(Instant.now());
        if (deletedCount > 0) {
            log.info("Cleaned up {} expired refresh tokens", deletedCount);
        }
    }
}