package com.example.parabella_csrd_db.dto.authentication.response;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * DTO for the JWT response sent to the client after successful authentication.
 * It includes the token, user details, their assigned roles, and their specific permissions.
 */
@Getter
@Setter
public class JwtResponse {
    // Renamed from 'token' to 'accessToken' for clarity and consistency.
    private String accessToken;
    private String refreshToken;
    private String type = "Bearer";
    private Long id;
    private String username;
    private String email;
    private List<String> roles;
    private List<String> permissions; // <-- ADDED THIS FIELD

    /**
     * Constructor for creating a JWT response.
     *
     * @param accessToken The generated JSON Web Token.
     * @param id The user's unique identifier.
     * @param username The user's username.
     * @param email The user's email address.
     * @param roles A list of roles assigned to the user (e.g., "ROLE_ADMIN").
     * @param permissions A list of specific permissions the user has (e.g., "VIEW_DASHBOARD").
     */
    public JwtResponse(String accessToken, Long id, String username, String email, List<String> roles, List<String> permissions) {
        this.accessToken = accessToken;
        this.id = id;
        this.username = username;
        this.email = email;
        this.roles = roles;
        this.permissions = permissions; // <-- ASSIGN THE NEW FIELD
    }
    
    /**
     * Constructor for creating a JWT response with refresh token.
     *
     * @param accessToken The generated JSON Web Token.
     * @param refreshToken The refresh token for obtaining new access tokens.
     * @param id The user's unique identifier.
     * @param username The user's username.
     * @param email The user's email address.
     * @param roles A list of roles assigned to the user.
     * @param permissions A list of specific permissions the user has.
     */
    public JwtResponse(String accessToken, String refreshToken, Long id, String username, String email, List<String> roles, List<String> permissions) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.id = id;
        this.username = username;
        this.email = email;
        this.roles = roles;
        this.permissions = permissions;
    }

    // The old manual getters and setters are no longer needed
    // because the @Getter and @Setter annotations from Lombok
    // will generate them automatically at compile time.
    // This keeps the code clean and free of boilerplate.

}