spring.application.name=parabella_csrd_db

spring.datasource.username=postgres
# --- CSRD Database Configuration (Primary) ---
spring.datasource.csrd.url=${SPRING_DATASOURCE_CSRD_URL}
spring.datasource.csrd.username=${SPRING_DATASOURCE_CSRD_USERNAME}
spring.datasource.csrd.password=${SPRING_DATASOURCE_CSRD_PASSWORD}
# Add other properties specific to this datasource if needed, e.g., driver class name, connection pool settings
spring.datasource.csrd.driver-class-name=org.postgresql.Driver

# --- VectorDB Database Configuration (Secondary) ---
spring.datasource.vectordb.url=${SPRING_DATASOURCE_VECTORDB_URL}
spring.datasource.vectordb.username=${SPRING_DATASOURCE_VECTORDB_USERNAME}
spring.datasource.vectordb.password=${SPRING_DATASOURCE_VECTORDB_PASSWORD}
spring.datasource.vectordb.driver-class-name=org.postgresql.Driver


app.frontend.url=https://gcr-parabella-production-frontend-1091242934000.europe-west4.run.app

server.port=${PORT:8080}

# PRODUCTION SECURITY: Explicitly disable SQL logging
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql=WARN
logging.level.org.hibernate.type=WARN



