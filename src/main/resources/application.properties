# --- Common Application Settings ---
spring.application.name=parabella-csrd-db
server.port=${PORT:8080}
spring.main.allow-bean-definition-overriding=true

# --- Common JPA Settings (Production-Safe Defaults) ---
# SECURITY: SQL logging disabled by default for production safety
# Use environment variables to enable for development/debugging
spring.jpa.show-sql=${SPRING_JPA_SHOW_SQL:false}
spring.jpa.properties.hibernate.format_sql=${HIBERNATE_FORMAT_SQL:false}
logging.level.org.hibernate.SQL=${HIBERNATE_SQL_LOG_LEVEL:WARN}
logging.level.org.hibernate.type.descriptor.sql=${HIBERNATE_PARAM_LOG_LEVEL:WARN}

spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.datasource.driver-class-name=org.postgresql.Driver

spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy

# --- App Specific Properties (Use Env Vars for Secrets!) ---
parabella_csrd_db.jwtSecret=${JWT_SECRET}
parabella_csrd_db.jwtExpirationMs=900000
# 15 minutes for access token
parabella_csrd_db.jwtRefreshExpirationMs=604800000
# 7 days for refresh token
parabella_csrd_db.maxActiveRefreshTokensPerUser=5

spring.jpa.properties.hibernate.envers.store_data_at_delete=true
spring.jpa.properties.hibernate.envers.global_with_modified_flag=true
spring.jpa.properties.hibernate.envers.modified_flag_suffix=_MOD

# --- OpenAI (Use Env Var) ---
OPEN_AI_API_KEY = ${OPEN_AI_API_KEY}
spring.datasource.username=postgres

spring.jpa.properties.hibernate.hbm2ddl.auto=update

# --- Mail (Use Env Vars for Secrets!) ---
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${SPRING_MAIL_USERNAME}
spring.mail.password=${SPRING_MAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# A comma-separated list of role names that are allowed to be assigned as stakeholders.
parabella.security.assignable-roles=Admin,Manager

# --- Default Active Profile if none is set (optional, 'local' is a good default) ---
spring.profiles.default=local

# --- Disable Flyway ---
spring.flyway.enabled=false