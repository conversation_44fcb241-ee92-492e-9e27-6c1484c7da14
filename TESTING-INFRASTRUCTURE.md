# Testing Infrastructure Documentation

## Overview

The Parabella CSRD FAQ Tool project features a comprehensive testing infrastructure designed to ensure code quality, security, and maintainability. The testing strategy covers all layers of the application with a focus on Spring Boot best practices, security testing, and AI-powered test automation.

## Table of Contents

1. [Testing Strategy Overview](#testing-strategy-overview)
2. [Test Infrastructure Setup](#test-infrastructure-setup)
3. [Test Categories and Organization](#test-categories-and-organization)
4. [Testing Best Practices](#testing-best-practices)
5. [Test Configuration](#test-configuration)
6. [Test Execution and Coverage](#test-execution-and-coverage)
7. [Claude Test Automation](#claude-test-automation)
8. [Authentication Testing](#authentication-testing)
9. [Test Data Management](#test-data-management)
10. [Coverage Goals and Metrics](#coverage-goals-and-metrics)

## Testing Strategy Overview

### Goals
- **Quality Assurance**: Ensure all business logic is thoroughly tested
- **Security Validation**: Verify authentication, authorization, and data protection
- **Regression Prevention**: Catch breaking changes early in development
- **Documentation**: Tests serve as living documentation of system behavior
- **Confidence**: Enable safe refactoring and feature development

### Test Categories
1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions and data flow
3. **Controller Tests**: Test REST API endpoints with security
4. **Repository Tests**: Test data access layer with database interactions
5. **Service Tests**: Test business logic with mocked dependencies

### Coverage Goals
- **Minimum Line Coverage**: 80%
- **Critical Path Coverage**: 100% (authentication, data processing, security)
- **Exception Path Coverage**: All exception scenarios tested
- **Security Scenario Coverage**: All authentication and authorization paths

## Test Infrastructure Setup

### Core Configuration Files

#### `/src/test/java/com/example/parabella_csrd_db/config/TestConfig.java`
Central test configuration that provides:
- **Password Encoder**: BCrypt encoder for authentication tests
- **JWT Utils**: Token generation and validation for security tests
- **Mock Beans**: Email service and password reset service mocks
- **Authentication Manager**: Configured for test scenarios

```java
@TestConfiguration
@Profile("test")
public class TestConfig {
    @Bean
    @Primary
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @MockBean
    public JavaMailSender javaMailSender;
    
    @Bean
    @Primary
    public JwtUtils jwtUtils() {
        return new JwtUtils();
    }
}
```

#### `/src/test/java/com/example/parabella_csrd_db/config/TestSecurityConfig.java`
Security configuration for tests:
- **Disabled CSRF**: For easier API testing
- **Stateless Sessions**: JWT-based authentication testing
- **Permissive Security**: Allows test-specific endpoints

```java
@TestConfiguration
@EnableWebSecurity
@Profile("test")
public class TestSecurityConfig {
    @Bean
    @Primary
    public SecurityFilterChain testSecurityFilterChain(HttpSecurity http) {
        // Simplified security for testing
    }
}
```

### Base Test Classes

#### `/src/test/java/com/example/parabella_csrd_db/utils/BaseIntegrationTest.java`
Abstract base class for integration tests providing:
- **Spring Boot Test Configuration**: Full application context
- **Test Profiles**: Isolated test environment
- **MockMvc**: Web layer testing utilities
- **ObjectMapper**: JSON serialization for API tests
- **Transactional Support**: Automatic rollback after tests

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(locations = "classpath:application-test.properties")
@Import({TestConfig.class, TestSecurityConfig.class})
@AutoConfigureWebMvc
@Transactional
@Testcontainers
public abstract class BaseIntegrationTest {
    @Autowired
    protected MockMvc mockMvc;
    
    @Autowired
    protected ObjectMapper objectMapper;
}
```

#### `/src/test/java/com/example/parabella_csrd_db/utils/TestDataBuilder.java`
Comprehensive test data factory with builder patterns:
- **User Builder**: Creates users with different roles and configurations
- **Role Builder**: Creates roles with specific permissions
- **Company Builder**: Creates company entities for business tests
- **Project Builder**: Creates project entities with relationships
- **Request Builders**: Creates DTOs for API testing

```java
public class TestDataBuilder {
    public static UserBuilder aUser() {
        return new UserBuilder();
    }
    
    public static class UserBuilder {
        private String username = "testuser";
        private String email = "<EMAIL>";
        private String totpSecret = "validTotpSecret";
        
        public UserBuilder withTotpSecret(String totpSecret) {
            this.totpSecret = totpSecret;
            return this;
        }
        
        public User build() {
            // Create and configure user entity
        }
    }
}
```

## Test Categories and Organization

### Directory Structure
```
src/test/java/com/example/parabella_csrd_db/
├── config/                    # Test configuration
│   ├── TestConfig.java
│   └── TestSecurityConfig.java
├── controller/               # Controller layer tests
│   ├── authentication/      # Auth endpoint tests
│   ├── chat_bot/           # AI chat endpoint tests
│   └── [business_modules]/  # Business logic endpoints
├── service/                 # Service layer tests
│   ├── authentication/     # Auth service tests
│   ├── chat_bot/          # AI service tests
│   └── [business_modules]/ # Business service tests
├── repository/             # Data access tests
├── model/                  # Entity and domain tests
├── utils/                  # Test utilities and base classes
└── ParabellaCsrdDbApplicationTests.java
```

### Test Naming Conventions
- **Test Classes**: `[ClassName]Test.java`
- **Test Methods**: `when[Action]_[Condition]_then[ExpectedResult]()`
- **Display Names**: Descriptive BDD-style naming
- **Nested Classes**: Group related test scenarios

Example:
```java
@DisplayName("Authentication Service Tests")
class AuthenticationServiceTest {
    @Nested
    @DisplayName("User Authentication Tests")
    class AuthenticationTests {
        @Test
        @DisplayName("Should return JWT response for valid credentials and TOTP")
        void whenAuthenticate_withValidCredentialsAndTotp_thenReturnsJwtResponse() {
            // Test implementation
        }
    }
}
```

## Testing Best Practices

### 1. Test Structure (AAA Pattern)
```java
@Test
void testMethod() {
    // Arrange - Set up test data and mocks
    LoginRequest request = TestDataBuilder.aLoginRequest();
    when(mockService.authenticate(any())).thenReturn(expectedResult);
    
    // Act - Execute the method under test
    Object result = service.authenticate(request);
    
    // Assert - Verify the results
    assertThat(result).isInstanceOf(JwtResponse.class);
    verify(mockService).authenticate(request);
}
```

### 2. Mocking Strategy
- **External Dependencies**: Always mock external APIs, email services, file systems
- **Database Layer**: Use H2 in-memory database for integration tests
- **Static Methods**: Use MockedStatic for utility classes like TotpUtils
- **Security Context**: Mock authentication for controller tests

```java
@Mock
private UserRepository userRepository;

@MockBean  // Spring Boot's mock for beans
private EmailService emailService;

try (MockedStatic<TotpUtils> totpMock = mockStatic(TotpUtils.class)) {
    totpMock.when(() -> TotpUtils.validateCode(secret, code)).thenReturn(true);
    // Test execution
}
```

### 3. Assertion Strategies
- **AssertJ**: Primary assertion library for fluent, readable assertions
- **Custom Matchers**: For complex domain-specific validations
- **Exception Testing**: Verify both exception type and message
- **Mock Verification**: Ensure correct interactions with dependencies

```java
// AssertJ assertions
assertThat(result)
    .isInstanceOf(JwtResponse.class)
    .extracting("username", "accessToken")
    .containsExactly("testuser", "jwt.token.here");

// Exception assertions
assertThatThrownBy(() -> service.authenticate(invalidRequest))
    .isInstanceOf(IllegalArgumentException.class)
    .hasMessage("Error: Invalid or missing TOTP code");

// Mock verification
verify(userRepository).save(userCaptor.capture());
assertThat(userCaptor.getValue().getTotpSecret()).isEqualTo("new-secret");
```

### 4. Parameterized Testing
```java
@ParameterizedTest
@ValueSource(ints = {999999, 000000, 111111})
@DisplayName("Should return unauthorized for invalid TOTP codes")
void whenSignIn_withInvalidTotpCode_thenReturnsUnauthorized(int invalidCode) {
    // Test implementation with different invalid codes
}
```

## Test Configuration

### `/src/test/resources/application-test.properties`
Comprehensive test environment configuration:

```properties
# Test Profile
spring.profiles.active=test

# H2 Database Configuration (PostgreSQL compatible)
spring.datasource.url=jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Multi-database Support
spring.datasource.csrd.url=jdbc:h2:mem:testdb_csrd;MODE=PostgreSQL
spring.datasource.vector.url=jdbc:h2:mem:testdb_vector;MODE=PostgreSQL

# JWT Configuration for Tests
jwt.secret=testSecretKeyForJwtTokenGenerationThatIsLongEnoughForHS512Algorithm
jwt.expiration=86400000

# Mock Email Configuration
spring.mail.host=localhost
spring.mail.port=587
spring.mail.properties.mail.smtp.auth=false

# Debug Logging
logging.level.com.example.parabella_csrd_db=DEBUG
logging.level.org.springframework.security=DEBUG

# Disable Flyway for tests
spring.flyway.enabled=false
```

### Key Configuration Features
- **H2 Database**: In-memory database with PostgreSQL compatibility mode
- **Multi-Database Support**: Separate test databases for different modules
- **JWT Configuration**: Test-specific secrets and expiration times
- **Mock Services**: Disabled external integrations
- **Debug Logging**: Enhanced logging for test debugging

## Test Execution and Coverage

### Running Tests
```bash
# Run all tests
./gradlew test

# Run specific test class
./gradlew test --tests AuthenticationServiceTest

# Run tests with coverage
./gradlew test jacocoTestReport

# Run with specific profile
./gradlew test -Dspring.profiles.active=test
```

### Coverage Analysis
The project uses JaCoCo for comprehensive coverage analysis:

```bash
# Generate coverage report
./gradlew jacocoTestReport

# View HTML report
open build/reports/jacoco/test/html/index.html

# Check coverage thresholds
./gradlew jacocoTestCoverageVerification
```

### Coverage Metrics
- **Line Coverage**: Percentage of executed code lines
- **Branch Coverage**: Percentage of executed decision branches
- **Method Coverage**: Percentage of invoked methods
- **Class Coverage**: Percentage of classes with at least one test

## Claude Test Automation

### AI-Powered Testing Tools

#### 1. Test Coverage Analysis (`/.claude/scripts/test-coverage.sh`)
Automated script that:
- Runs tests with coverage instrumentation
- Analyzes coverage gaps and identifies critical untested areas
- Provides specific recommendations for improvement
- Generates both console and HTML reports
- Fails CI builds if coverage drops below threshold

```bash
# Usage examples
./.claude/scripts/test-coverage.sh
./.claude/scripts/test-coverage.sh --full --threshold 85
./.claude/scripts/test-coverage.sh --service --threshold 90
```

#### 2. Test Generator Agent (`/.claude/agents/test-generator.md`)
AI agent that automatically generates test skeletons:
- Analyzes untested code and dependencies
- Generates appropriate test structure and mocks
- Creates parameterized tests for edge cases
- Follows project conventions and patterns
- Includes TODO comments for complex scenarios

#### 3. Test Coverage Command (`/.claude/commands/test-coverage.md`)
Claude command for coverage analysis:
- Integrates with development workflow
- Provides actionable coverage reports
- Identifies specific methods needing tests
- Suggests test improvement strategies

### Claude Testing Features
1. **Automated Test Generation**: Creates test skeletons for untested code
2. **Coverage Gap Analysis**: Identifies and prioritizes testing gaps
3. **Test Quality Assessment**: Analyzes existing tests for completeness
4. **Recommendation Engine**: Suggests specific test improvements
5. **Integration with CI/CD**: Automated coverage checking in pipelines

## Authentication Testing

### Dual-Token Authentication Testing
The system implements comprehensive testing for the dual-token authentication system:

#### JWT Token Testing
```java
@Test
@DisplayName("Should return JWT response when credentials and TOTP are valid")
void whenSignIn_withValidCredentialsAndTotp_thenReturnsJwtResponse() {
    // Test JWT token generation and validation
    when(jwtUtils.generateJwtToken(mockAuthentication)).thenReturn("dummy.jwt.token");
    
    // Verify JWT response structure
    assertThat(jwtResponse.getAccessToken()).isEqualTo("dummy.jwt.token");
    assertThat(jwtResponse.getUsername()).isEqualTo("testuser");
    assertThat(jwtResponse.getPermissions()).contains("VIEW_DASHBOARD");
}
```

#### 2FA/TOTP Testing
```java
@Test
@DisplayName("Should require 2FA setup for first-time user")
void whenSignIn_forFirstTime_thenRequires2faSetup() {
    User mockUser = TestDataBuilder.aUser().withoutTotpSecret().build();
    
    try (MockedStatic<TotpUtils> totpMock = mockStatic(TotpUtils.class)) {
        totpMock.when(TotpUtils::generateSecret).thenReturn("new-secret");
        totpMock.when(() -> TotpUtils.getQrCodeUrl(anyString(), anyString(), anyString()))
                .thenReturn("http://qr.code/url");
        
        // Verify 2FA setup response
        assertThat(response.get("message")).isEqualTo("2FA setup required");
        assertThat(response.get("qrCodeUrl")).isEqualTo("http://qr.code/url");
    }
}
```

#### Security Integration Testing
```java
@Test
@WithMockUser(username = "testuser", authorities = {"ROLE_USER"})
@DisplayName("Should allow access with valid authentication")
void whenAccessSecuredEndpoint_withValidAuth_thenAllowsAccess() throws Exception {
    mockMvc.perform(get("/api/secure/endpoint"))
           .andExpect(status().isOk());
}

@Test
@DisplayName("Should deny access without authentication")
void whenAccessSecuredEndpoint_withoutAuth_thenDeniesAccess() throws Exception {
    mockMvc.perform(get("/api/secure/endpoint"))
           .andExpect(status().isUnauthorized());
}
```

### Authentication Test Coverage
- **Login Flow**: Username/password + TOTP validation
- **Registration**: User creation with 2FA setup
- **Password Reset**: Token-based password recovery
- **2FA Verification**: TOTP code validation
- **JWT Handling**: Token generation, validation, and expiration
- **Security Context**: Authentication state management
- **Authorization**: Role and permission-based access control

## Test Data Management

### TestDataBuilder Pattern
The project uses a comprehensive builder pattern for test data creation:

```java
// User creation with different configurations
User adminUser = TestDataBuilder.aUser()
    .withUsername("admin")
    .withEmail("<EMAIL>")
    .withRole(TestDataBuilder.createAdminRole())
    .build();

User newUser = TestDataBuilder.aUser()
    .withoutTotpSecret()  // First-time user
    .build();

// Request DTOs for API testing
LoginRequest loginRequest = TestDataBuilder.aLoginRequest("user", "pass", 123456);
SignUpRequest signUpRequest = TestDataBuilder.aSignUpRequest("newuser", "<EMAIL>", "password");
```

### Data Cleanup Strategy
- **Transactional Tests**: Automatic rollback after each test
- **H2 Database**: Fresh database for each test run
- **Isolated Test Data**: No test interference through shared data
- **Mock Reset**: Automatic mock cleanup between tests

### Test Database Configuration
```properties
# H2 with PostgreSQL compatibility
spring.datasource.url=jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE

# Create-drop strategy for clean tests
spring.jpa.hibernate.ddl-auto=create-drop

# Multiple databases for different modules
spring.datasource.csrd.url=jdbc:h2:mem:testdb_csrd
spring.datasource.vector.url=jdbc:h2:mem:testdb_vector
```

## Coverage Goals and Metrics

### Current Test Statistics
- **Total Test Files**: 70+ test classes
- **Test Categories**: 
  - Controller Tests: 15+ classes
  - Service Tests: 20+ classes  
  - Repository Tests: 7+ classes
  - Model Tests: 15+ classes
  - Integration Tests: Multiple classes

### Coverage Targets
| Component | Minimum Coverage | Target Coverage |
|-----------|-----------------|-----------------|
| Controllers | 85% | 95% |
| Services | 90% | 95% |
| Security | 95% | 100% |
| Authentication | 100% | 100% |
| Critical Business Logic | 90% | 95% |
| Overall Project | 80% | 85% |

### Coverage Monitoring
1. **Automated Checks**: Coverage validation in CI/CD
2. **Quality Gates**: Build failure if coverage drops below threshold
3. **Regular Reviews**: Weekly coverage analysis and improvement
4. **Gap Analysis**: Identification of untested critical paths

### Test Quality Metrics
- **Test Execution Time**: < 5 minutes for full suite
- **Test Reliability**: > 99% pass rate in CI
- **Test Maintainability**: Clear naming and structure
- **Documentation Coverage**: All public APIs tested and documented

## Next Steps and Recommendations

### Immediate Improvements
1. **Increase Service Layer Coverage**: Target 95% coverage for all service classes
2. **Add Performance Tests**: Load testing for critical endpoints
3. **Enhance Security Tests**: More comprehensive authorization scenarios
4. **Mutation Testing**: Implement PIT testing for test quality validation

### Future Enhancements
1. **Contract Testing**: API contract validation with Pact
2. **End-to-End Testing**: Full user journey automation
3. **Chaos Testing**: Resilience testing with failure injection
4. **Visual Regression Testing**: UI component testing

### Continuous Improvement
1. **Regular Test Reviews**: Monthly test code reviews
2. **Coverage Trend Analysis**: Track coverage improvements over time
3. **Test Performance Optimization**: Reduce test execution time
4. **Knowledge Sharing**: Test writing best practices documentation

## Conclusion

The Parabella CSRD FAQ Tool's testing infrastructure represents a comprehensive approach to quality assurance, combining traditional testing methodologies with AI-powered automation. The infrastructure supports confident development, ensures security compliance, and maintains high code quality standards through extensive coverage and robust test patterns.

The integration of Claude AI agents for test generation and coverage analysis provides a unique advantage in maintaining and improving test quality while reducing manual effort. This testing strategy positions the project for reliable, scalable development while ensuring critical business logic and security features are thoroughly validated.