-- Migration script to fix RefreshToken table structure

-- Drop the existing refresh_tokens table if it exists with wrong constraints
DROP TABLE IF EXISTS refresh_tokens;

-- Recreate the table with the correct structure
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    device_info VARCHAR(500),
    ip_address VARCHAR(45),
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT fk_refresh_token_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_refresh_token_user ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_token_expiry ON refresh_tokens(expiry_date);
CREATE INDEX idx_refresh_token_active ON refresh_tokens(user_id, is_revoked, expiry_date);

-- Note: This allows multiple refresh tokens per user (ManyToOne relationship)
-- No unique constraint on user_id, allowing multiple active sessions per user