# frontend.gitlab-ci.yml
stages:
  - build_push # Combined build and push
  - deploy_staging
  - deploy_production

variables:

  # --- Pipeline Variables ---
  IMAGE_TAG: $CI_COMMIT_SHORT_SHA
  ARTIFACT_REGISTRY_PATH: $GCP_REGION_FRONTEND-docker.pkg.dev/$GCP_PROJECT_ID/$GCP_FRONTEND_REPO
  FULL_IMAGE_NAME_FRONTEND: $ARTIFACT_REGISTRY_PATH/$CI_COMMIT_BRANCH-$IMAGE_TAG
  DOCKERFILE_PATH: parabella_frontend_nowa/parabella_elessar

# Use anchors for DRY script definitions
.gcloud_auth_frontend: &gcloud_auth_frontend
  - echo "Authenticating with GCP..."
  - echo "$GCP_SERVICE_KEY" > /tmp/gcp-key.json
  - gcloud auth activate-service-account --key-file=/tmp/gcp-key.json --quiet
  - gcloud config set project "$GCP_PROJECT_ID" --quiet
  # Configure Docker for the specific Artifact Registry region
  - gcloud auth configure-docker $GCP_REGION_FRONTEND-docker.pkg.dev --quiet

# Common Docker setup
.docker_setup: &docker_setup
  image: google/cloud-sdk:alpine # Use specific tag instead of latest
  services:
    - name: docker:20.10-dind # Use specific tag
      alias: docker
  variables:
    # Explicitly set DOCKER_HOST if needed, though often works without with dind service alias
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: "" # Required for Docker-in-Docker
    DOCKER_DRIVER: overlay2 # Recommended driver for dind

docker_build_push_frontend:
  stage: build_push
  # Use the original image
  extends: .docker_setup

  # 1. Add a before_script to install the Docker client
  before_script:
    - apk add --no-cache docker-cli

  script:
    - set -x # Echo commands for debugging
    - *gcloud_auth_frontend
    - docker build --platform linux/amd64 -t "$FULL_IMAGE_NAME_FRONTEND" "$DOCKERFILE_PATH"
    - echo "Pushing frontend Docker image..."
    - docker push "$FULL_IMAGE_NAME_FRONTEND"

deploy_staging_frontend:
  stage: deploy_staging
  image: google/cloud-sdk:alpine # Use specific tag
  environment:
    name: staging # Links to GitLab Environment 'staging'
    url: https://parabella.app
  script:
    - set -x
    - *gcloud_auth_frontend # Authenticate

    # Deploy to Cloud Run. GitLab automatically uses the 'staging' scoped FRONTEND_SERVICE_NAME.
    # Assuming the container serves traffic on port 80 internally. Remove --port if it's 8080.
    - |
      gcloud run deploy "$FRONTEND_SERVICE_NAME" \
        --image "$FULL_IMAGE_NAME_FRONTEND" \
        --region "$GCP_REGION_FRONTEND" \
        --platform managed \
        --allow-unauthenticated \
        --port 80 \
        --quiet
  rules:
    # Run only on commits/merges to the 'staging' branch
    - if: '$CI_COMMIT_BRANCH == "staging"'
  needs: [docker_build_push_frontend] # Explicit dependency

deploy_production_frontend:
  stage: deploy_production
  image: google/cloud-sdk:alpine # Use specific tag
  environment:
    name: production # Links to GitLab Environment 'production'
    url: https://$FRONTEND_SERVICE_NAME-xxxxxx-$GCP_REGION_FRONTEND.a.run.app
  script:
    - set -x
    - *gcloud_auth_frontend # Authenticate

    # Deploy to Cloud Run. GitLab uses 'production' scoped FRONTEND_SERVICE_NAME.
    # Keep flags consistent with staging deployment (e.g., --port)
    - |
      gcloud run deploy "$FRONTEND_SERVICE_NAME" \
        --image "$FULL_IMAGE_NAME_FRONTEND" \
        --region "$GCP_REGION_FRONTEND" \
        --platform managed \
        --allow-unauthenticated \
        --port 80 \
        --quiet
  rules:
    # Run only on commits/merges to the 'main' branch, AND requires manual trigger
    - if: '$CI_COMMIT_BRANCH == "production"'
      when: manual # IMPORTANT: Manual trigger for production deployment
      allow_failure: false # Ensure production deploy failure stops the pipeline
  needs: [docker_build_push_frontend] # Explicit dependency