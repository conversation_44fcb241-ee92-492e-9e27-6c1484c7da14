# CLAUDE.md

## Project Overview

**Parabella CSRD FAQ Tool** is a comprehensive enterprise application for Corporate Sustainability Reporting Directive (CSRD) compliance and ESG reporting. The system provides tools for collecting, analyzing, and reporting sustainability data with AI-powered insights.

### System Architecture
- **Frontend**: React 18.3.1 + TypeScript SPA built with Vite
  - Location: `parabella_frontend_nowa/parabella_elessar/`
  - UI Framework: React Bootstrap + Custom SCSS
  - State Management: Redux Toolkit + Context API
  - Authentication: JWT with 2FA support
  
- **Backend**: Java 21 + Spring Boot 3.3.0 REST API
  - Location: `src/`
  - Database: PostgreSQL with Hibernate + Envers audit trail
  - Security: JWT authentication, BCrypt encryption, role-based access
  - External Integrations: OpenAI API, Email services

### Key Features
- CSRD compliance reporting and data collection
- Multi-module architecture (DMA, PCF, CSRD modules)
- Real-time data visualization with multiple chart libraries
- File processing (Excel, CSV, PDF) capabilities
- AI-powered chat and analysis features
- Comprehensive audit trail and user management
- Two-factor authentication with TOTP

### Technology Stack
**Frontend**: React, TypeScript, Vite, Redux Toolkit, <PERSON>act Bootstrap, SCSS, Chart.js, Recharts, Formik, Axios
**Backend**: Spring Boot, Spring Security, Spring Data JPA, PostgreSQL, Hibernate Envers, JWT, Apache POI
**Testing**: JUnit 5, Mockito, Spring Boot Test, H2 Database, JaCoCo Coverage, Claude AI Test Automation

### Project Structure
```
parabella_frontend_nowa/parabella_elessar/src/ui_components/
│   ├── components/           # Feature-based organization
│   │   ├── authentication/   # Auth-related components
│   │   ├── CSRD_Module/      # CSRD business logic
│   │   ├── DmaModule/        # DMA business logic
│   │   └── PCFModule/        # PCF business logic
│   ├── assets/               # Static resources, SCSS, images
│   ├── layout/               # Reusable layout components
│   └── templateLogic/        # Redux store, routing utilities
├── services/                 # API service layer
└── config/                   # Configuration files
```

#### Naming Conventions
- **Components**: PascalCase (`UserDashboard.tsx`)
- **Functions/Variables**: camelCase (`getUserData`, `isLoading`)
- **Constants**: SCREAMING_SNAKE_CASE (`API_BASE_URL`)
- **CSS Classes**: kebab-case (`user-dashboard-container`)
- **Files**: PascalCase for components, camelCase for utilities

#### Component Standards
```typescript
// Use functional components with TypeScript interfaces
interface UserDashboardProps {
  userId: string;
  onUpdate?: (data: UserData) => void;
}

export const UserDashboard: React.FC<UserDashboardProps> = ({ 
  userId, 
  onUpdate 
}) => {
  // Use hooks for state management
  const [loading, setLoading] = useState(false);
  
  // Return JSX with proper typing
  return <div>...</div>;
};
```

#### State Management
- **Global State**: Redux Toolkit for app-wide state
- **Domain State**: Context API for specific business domains (Auth, Projects, Companies)
- **Local State**: `useState` for component-specific data
- **Async Actions**: Redux Thunk for API calls

#### Styling Standards
- Use SCSS modules with Bootstrap utility classes
- Follow BEM methodology for custom CSS classes
- Maintain consistent spacing using Bootstrap's spacing utilities
- Use CSS custom properties for theme variables

### Backend Standards (Java/Spring Boot)

#### Package Organization
```
com.example.parabella_csrd_db/
├── controller/          # REST endpoints (presentation layer)
├── service/            # Business logic layer
├── database/           # Data access layer
│   ├── config/         # Database configurations
│   ├── maindatabase/   # Primary database entities & repositories
│   └── vectordatabase/ # Vector database for AI features
├── dto/                # Data Transfer Objects
├── security/           # Security configurations
└── utilities/          # Helper classes and utilities
```

#### Naming Conventions
- **Packages**: lowercase with underscores (`parabella_csrd_db`)
- **Classes**: PascalCase (`AuthController`, `UserService`)
- **Methods**: camelCase (`authenticateUser`, `findByUsername`)
- **Constants**: SCREAMING_SNAKE_CASE (`JWT_SECRET_KEY`)
- **Database tables**: snake_case plural (`user_accounts`, `csrd_reports`)
- **Database columns**: snake_case (`created_at`, `user_id`)

#### Class Structure Standards
```java
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {
    
    private final UserService userService;
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UserDto> getUser(@PathVariable Long id) {
        // Implementation
    }
}
```

#### Entity Standards
```java
@Entity
@Table(name = "users")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class User extends BaseAuditedEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, unique = true)
    @Email
    private String email;
    
    // Use proper JPA relationships
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<UserRole> roles = new ArrayList<>();
}
```

#### Service Layer Standards
- Use `@Transactional` for data-modifying operations
- Implement proper exception handling with custom exceptions
- Use DTOs for external API communication
- Implement comprehensive logging with SLF4J

#### Security Standards
- All endpoints require authentication unless explicitly marked as public
- Use method-level security with `@PreAuthorize`
- Implement proper JWT validation and refresh token handling
- Use BCrypt for password hashing
- Implement rate limiting for sensitive endpoints

## Documentation Standards

### Code Documentation
- **JavaDoc**: Required for all public methods and classes in backend
- **TSDoc**: Required for complex functions and public APIs in frontend
- **Inline Comments**: Use sparingly, focus on "why" not "what"
- **README**: Update when adding new modules or changing setup procedures

### API Documentation
- Document all REST endpoints with request/response examples
- Include authentication requirements and error codes
- Use OpenAPI/Swagger for backend API documentation
- Document GraphQL schemas if implemented

### Database Documentation
- Document all entity relationships and constraints
- Maintain migration scripts with clear descriptions
- Document any stored procedures or complex queries
- Keep ER diagrams updated for major schema changes

### Architecture Documentation
- Update architecture diagrams when adding new services
- Document integration patterns and external dependencies
- Maintain deployment and environment configuration docs
- Document security policies and access control patterns

### Testing Documentation
- Document test strategies and coverage requirements
- Maintain test data setup and teardown procedures
- Document integration test scenarios
- Keep performance testing benchmarks updated

### Change Management
- **Commit Messages**: Use conventional commit format
  ```
  feat(auth): add 2FA support for user login
  fix(api): resolve database connection timeout
  docs(readme): update installation instructions
  ```
- **Pull Requests**: Include description, testing notes, and breaking changes
- **Release Notes**: Document features, fixes, and breaking changes
- **Configuration Changes**: Document environment variable updates

### Maintenance Standards
- **Code Reviews**: Required for all changes, focus on security and performance
- **Dependency Updates**: Monthly security updates, quarterly major updates
- **Performance Monitoring**: Monitor API response times and database queries
- **Security Audits**: Quarterly security reviews and dependency scanning

### Environment Configuration
- **Development**: Local PostgreSQL, hot reload enabled
- **Staging**: Cloud database, production-like configuration
- **Production**: Cloud deployment with monitoring and logging

### Build and Deployment
- **Frontend Build**: `npm run build` - creates optimized production bundle
- **Backend Build**: `gradle build` - creates executable JAR
- **Testing**: `npm run lint` (frontend), `gradle test` (backend)
- **Database Migrations**: Use Hibernate DDL update mode (development) or manual scripts (production)

### Performance Guidelines
- **Frontend**: Implement code splitting for large modules, lazy load components
- **Backend**: Use connection pooling, implement caching for frequent queries
- **Database**: Use proper indexing, monitor query performance
- **API**: Implement pagination for large datasets, use compression

## Documentation Structure

### Comprehensive Documentation Available
The project maintains extensive documentation across two primary locations:

#### `/docs/` - Architecture & Implementation Focus
- **[Authentication Architecture](docs/AUTHENTICATION_ARCHITECTURE.md)** - Dual-token system and security implementation
- **[Security Model](docs/SECURITY_MODEL_DOCUMENTATION.md)** - Multi-layer security architecture
- **[System Integration](docs/SYSTEM_INTEGRATION_ARCHITECTURE.md)** - Module integration patterns
- **[Migration Guide](docs/AUTHENTICATION_MIGRATION_GUIDE.md)** - Developer migration instructions
- **[Token Management](docs/TOKEN_MANAGEMENT.md)** - Enterprise token management details
- **[System Diagrams](docs/SYSTEM_ARCHITECTURE_DIAGRAM.md)** - Visual architecture references

#### `/documentation/` - API & Development Focus  
- **[API Reference](documentation/api-reference.md)** - Complete API documentation
- **[System Architecture](documentation/system-architecture.md)** - Master architecture document
- **[Database ERDs](documentation/database-erd.md)** - Entity relationship diagrams
- **[Frontend Components](documentation/frontend-components.md)** - Component library documentation
- **[Authentication Security](documentation/authentication-security.md)** - Security implementation details
- **[Testing Infrastructure](TESTING-INFRASTRUCTURE.md)** - Comprehensive testing strategy and infrastructure
- **[Configuration Guide](documentation/configuration-guide.md)** - Environment setup
- **[Deployment Guide](documentation/deployment-guide.md)** - Production deployment

#### Key Authentication Features (2024-2025)
- **Dual-Token System**: 15-minute access + 7-day refresh tokens
- **Automatic Refresh**: Transparent token renewal via interceptors
- **Session Management**: Timeout warnings and conflict resolution
- **Enhanced Security**: Device tracking, session limits, audit trails
- **2FA Integration**: TOTP-based two-factor authentication

### Testing Infrastructure (2024-2025)
The project includes comprehensive testing infrastructure with enterprise-grade coverage:

#### Core Testing Documentation
- **[Testing Infrastructure](TESTING-INFRASTRUCTURE.md)** - Complete testing strategy, patterns, and best practices
- **[Testing Summary](TESTING-INFRASTRUCTURE-SUMMARY.md)** - Executive overview of testing capabilities
- **[Testing Strategy](documentation/testing-strategy.md)** - Detailed testing approach and methodology

#### Testing Technology Stack
- **Testing Frameworks**: JUnit 5, Mockito, Spring Boot Test, TestContainers
- **Coverage Tools**: JaCoCo with 80% minimum coverage requirements
- **Test Database**: H2 in-memory with PostgreSQL compatibility
- **AI Testing**: Claude-powered test generation and coverage analysis
- **Integration Testing**: Full application context with security testing

#### Key Testing Features
- **70+ Test Classes**: Comprehensive coverage across all layers
- **Authentication Testing**: Complete dual-token and 2FA validation
- **AI-Enhanced Testing**: Automated test generation and gap analysis
- **Professional Patterns**: AAA structure, builder patterns, parameterized tests
- **Security Testing**: Comprehensive authorization and authentication coverage
- **Automated Coverage**: JaCoCo integration with CI/CD quality gates

See **[TESTING-INFRASTRUCTURE.md](TESTING-INFRASTRUCTURE.md)** for complete testing documentation.