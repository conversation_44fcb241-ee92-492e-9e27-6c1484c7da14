> Task :compileJava
> Task :processResources UP-TO-DATE
> Task :classes
> Task :resolveMainClassName

> Task :bootRun
Standard Commons Logging discovery in action with spring-jcl: please remove commons-logging.jar from classpath in order to avoid potential conflicts
Standard Commons Logging discovery in action with spring-jcl: please remove commons-logging.jar from classpath in order to avoid potential conflicts
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.3.0)[0;39m

[2m2025-08-01T00:48:00.293+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mc.e.p.ParabellaCsrdDbApplication        [0;39m [2m:[0;39m Starting ParabellaCsrdDbApplication using Java 21.0.3 with PID 66147 (/Users/<USER>/IdeaProjects/ParabellaCSRDFaqTool/build/classes/java/main started by alexanderpavlovski in /Users/<USER>/IdeaProjects/ParabellaCSRDFaqTool)
[2m2025-08-01T00:48:00.294+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mc.e.p.ParabellaCsrdDbApplication        [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "local"
[2m2025-08-01T00:48:00.307+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-01T00:48:00.307+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-01T00:48:00.513+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-01T00:48:00.574+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 58 ms. Found 37 JPA repository interfaces.
[2m2025-08-01T00:48:00.575+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-01T00:48:00.576+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
[2m2025-08-01T00:48:00.882+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8080 (http)
[2m2025-08-01T00:48:00.886+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T00:48:00.886+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.24]
[2m2025-08-01T00:48:00.900+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-01T00:48:00.901+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 593 ms
Standard Commons Logging discovery in action with spring-jcl: please remove commons-logging.jar from classpath in order to avoid potential conflicts
[2m2025-08-01T00:48:00.943+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: csrdPU]
[2m2025-08-01T00:48:00.962+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.5.2.Final
[2m2025-08-01T00:48:00.977+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-01T00:48:01.007+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.boot.internal.EnversServiceImpl   [0;39m [2m:[0;39m Envers integration enabled? : true
[2m2025-08-01T00:48:01.014+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Starting...
[2m2025-08-01T00:48:01.082+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@52356893
[2m2025-08-01T00:48:01.082+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Start completed.
[2m2025-08-01T00:48:01.094+02:00[0;39m [33m WARN[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-01T00:48:01.224+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-01T00:48:01.365+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroupEsrsSelection
[2m2025-08-01T00:48:01.366+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup
[2m2025-08-01T00:48:01.366+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder
[2m2025-08-01T00:48:01.366+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.notification.Notification
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.Industry
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.Iro
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.IroEvaluation
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChainObject
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChain
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopicSelection
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic
[2m2025-08-01T00:48:01.367+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.authentication.User
[2m2025-08-01T00:48:01.368+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.c.i.m.AuditMetadataGenerator      [0;39m [2m:[0;39m Adding properties for entity: com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company
[2m2025-08-01T00:48:01.826+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
Hibernate: 
    alter table if exists company_group_esrs_selection 
       alter column reason_irrelevance set data type TEXT
Hibernate: 
    alter table if exists company_group_esrs_selection_AUD 
       alter column reason_irrelevance set data type TEXT
Hibernate: 
    alter table if exists csrd_company_info 
       alter column industry set data type TEXT
Hibernate: 
    alter table if exists csrd_field 
       alter column label set data type TEXT
Hibernate: 
    alter table if exists csrd_projects 
       alter column project_description set data type TEXT
Hibernate: 
    alter table if exists datapoint_responses 
       alter column data_response set data type text
Hibernate: 
    alter table if exists esrs_topic_selection 
       alter column reason_irrelevance set data type TEXT
Hibernate: 
    alter table if exists esrs_topic_selection_AUD 
       alter column reason_irrelevance set data type TEXT
Hibernate: 
    alter table if exists iro_evaluation 
       alter column description set data type TEXT
Hibernate: 
    alter table if exists iro_evaluation_AUD 
       alter column description set data type TEXT
Hibernate: 
    alter table if exists messages 
       alter column content set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column commonContent set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column level set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column nace21Code set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column nace21Heading set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column nace2Code set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column nace2Heading set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column numberOfCorrespondingNace2 set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column typeOfCorrespondence set data type TEXT
Hibernate: 
    alter table if exists nace_data 
       alter column typeOfMapping set data type TEXT
Hibernate: 
    alter table if exists subtopic_generated_texts 
       alter column generated_text set data type text
[2m2025-08-01T00:48:02.048+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'csrdPU'
[2m2025-08-01T00:48:02.072+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: vectorPU]
[2m2025-08-01T00:48:02.075+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-01T00:48:02.077+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.boot.internal.EnversServiceImpl   [0;39m [2m:[0;39m Envers integration enabled? : true
[2m2025-08-01T00:48:02.078+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-2 - Starting...
[2m2025-08-01T00:48:02.090+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@42be0b0a
[2m2025-08-01T00:48:02.090+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-2 - Start completed.
[2m2025-08-01T00:48:02.091+02:00[0;39m [33m WARN[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-01T00:48:02.113+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-01T00:48:02.144+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
Hibernate: 
    alter table if exists esrs_data 
       alter column data_point_name set data type text
Hibernate: 
    alter table if exists esrs_data 
       alter column data_response set data type text
Hibernate: 
    alter table if exists esrs_data 
       alter column paragraph set data type text
Hibernate: 
    alter table if exists esrs_data 
       alter column related_ar set data type text
Hibernate: 
    alter table if exists processed_document_chunks 
       alter column chunk_summary set data type text
Hibernate: 
    alter table if exists processed_document_chunks 
       alter column chunk_text set data type text
[2m2025-08-01T00:48:02.154+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'vectorPU'
[2m2025-08-01T00:48:02.408+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36meAuthenticationProviderManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
[2m2025-08-01T00:48:02.409+02:00[0;39m [33m WARN[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used for username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider.
[2m2025-08-01T00:48:02.454+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-01T00:48:03.054+02:00[0;39m [33m WARN[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-01T00:48:03.211+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@50916aba, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6bc61e81, org.springframework.security.web.context.SecurityContextHolderFilter@6b9ce1ea, org.springframework.security.web.header.HeaderWriterFilter@65796d81, org.springframework.web.filter.CorsFilter@3760a956, org.springframework.security.web.authentication.logout.LogoutFilter@5c6a545a, com.example.parabella_csrd_db.security.jwt.AuthTokenFilter@3dd82915, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6ed1bdf6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@396beca1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@78d63292, org.springframework.security.web.session.SessionManagementFilter@1dc37c9a, org.springframework.security.web.access.ExceptionTranslationFilter@14369378, org.springframework.security.web.access.intercept.AuthorizationFilter@7539daab]
[2m2025-08-01T00:48:03.390+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-01T00:48:03.402+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8080 (http) with context path '/'
[2m2025-08-01T00:48:03.407+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [  restartedMain][0;39m [2m[0;39m[36mc.e.p.ParabellaCsrdDbApplication        [0;39m [2m:[0;39m Started ParabellaCsrdDbApplication in 3.22 seconds (process running for 3.39)
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        p1_0.id,
        p1_0.category,
        p1_0.functionKey,
        p1_0.functionName 
    from
        permissions p1_0 
    where
        p1_0.functionKey=?
Hibernate: 
    select
        r1_0.id 
    from
        roles r1_0 
    where
        r1_0.name=? 
    fetch
        first ? rows only
Hibernate: 
    select
        r1_0.id 
    from
        roles r1_0 
    where
        r1_0.name=? 
    fetch
        first ? rows only
Hibernate: 
    select
        r1_0.id 
    from
        roles r1_0 
    where
        r1_0.name=? 
    fetch
        first ? rows only
JSON directory not found. Skipping data load...
Hibernate: 
    select
        count(*) 
    from
        industries i1_0
Industries already populated. Skipping data loading.
[2m2025-08-01T00:49:03.934+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [nio-8080-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-01T00:49:03.934+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [nio-8080-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-01T00:49:03.935+02:00[0;39m [32m INFO[0;39m [35m66147[0;39m [2m---[0;39m [2m[parabella-csrd-db] [nio-8080-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 1 ms
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        u1_0.role_id,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
Hibernate: 
    select
        r1_0.id,
        r1_0.name,
        p1_0.role_id,
        p1_1.id,
        p1_1.category,
        p1_1.functionKey,
        p1_1.functionName 
    from
        roles r1_0 
    left join
        role_permissions p1_0 
            on r1_0.id=p1_0.role_id 
    left join
        permissions p1_1 
            on p1_1.id=p1_0.permission_id 
    where
        r1_0.id=?
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        u1_0.role_id,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
Hibernate: 
    select
        r1_0.id,
        r1_0.name,
        p1_0.role_id,
        p1_1.id,
        p1_1.category,
        p1_1.functionKey,
        p1_1.functionName 
    from
        roles r1_0 
    left join
        role_permissions p1_0 
            on r1_0.id=p1_0.role_id 
    left join
        permissions p1_1 
            on p1_1.id=p1_0.permission_id 
    where
        r1_0.id=?
=== LOGIN ATTEMPT ===
Username: test
TOTP Code provided: true
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        u1_0.role_id,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
=== LOGIN ERROR ===
Error during login: Bad credentials
org.springframework.security.authentication.BadCredentialsException: Bad credentials
	at org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider.authenticate(AbstractUserDetailsAuthenticationProvider.java:141)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:182)
	at com.example.parabella_csrd_db.controller.authentication.AuthController.authenticateUser(AuthController.java:173)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.example.parabella_csrd_db.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
===================
=== LOGIN ATTEMPT ===
Username: Admin
TOTP Code provided: true
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        u1_0.role_id,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
Hibernate: 
    select
        r1_0.id,
        r1_0.name,
        p1_0.role_id,
        p1_1.id,
        p1_1.category,
        p1_1.functionKey,
        p1_1.functionName 
    from
        roles r1_0 
    left join
        role_permissions p1_0 
            on r1_0.id=p1_0.role_id 
    left join
        permissions p1_1 
            on p1_1.id=p1_0.permission_id 
    where
        r1_0.id=?
=== LOGIN ERROR ===
Error during login: Bad credentials
org.springframework.security.authentication.BadCredentialsException: Bad credentials
	at org.springframework.security.authentication.dao.DaoAuthenticationProvider.additionalAuthenticationChecks(DaoAuthenticationProvider.java:93)
	at org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider.authenticate(AbstractUserDetailsAuthenticationProvider.java:147)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:182)
	at com.example.parabella_csrd_db.controller.authentication.AuthController.authenticateUser(AuthController.java:173)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.example.parabella_csrd_db.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
===================
=== LOGIN ATTEMPT ===
Username: testuser
TOTP Code provided: true
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        u1_0.role_id,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
Hibernate: 
    select
        r1_0.id,
        r1_0.name,
        p1_0.role_id,
        p1_1.id,
        p1_1.category,
        p1_1.functionKey,
        p1_1.functionName 
    from
        roles r1_0 
    left join
        role_permissions p1_0 
            on r1_0.id=p1_0.role_id 
    left join
        permissions p1_1 
            on p1_1.id=p1_0.permission_id 
    where
        r1_0.id=?
=== LOGIN ERROR ===
Error during login: Bad credentials
org.springframework.security.authentication.BadCredentialsException: Bad credentials
	at org.springframework.security.authentication.dao.DaoAuthenticationProvider.additionalAuthenticationChecks(DaoAuthenticationProvider.java:93)
	at org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider.authenticate(AbstractUserDetailsAuthenticationProvider.java:147)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:182)
	at com.example.parabella_csrd_db.controller.authentication.AuthController.authenticateUser(AuthController.java:173)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.example.parabella_csrd_db.security.jwt.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:52)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:389)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
===================
Hibernate: 
    select
        rt1_0.id,
        rt1_0.created_at,
        rt1_0.device_info,
        rt1_0.expiryDate,
        rt1_0.ip_address,
        rt1_0.is_revoked,
        rt1_0.token,
        rt1_0.user_id 
    from
        refresh_tokens rt1_0 
    where
        rt1_0.token=?
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        r1_0.id,
        r1_0.name,
        p1_0.role_id,
        p1_1.id,
        p1_1.category,
        p1_1.functionKey,
        p1_1.functionName,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    left join
        roles r1_0 
            on r1_0.id=u1_0.role_id 
    left join
        role_permissions p1_0 
            on r1_0.id=p1_0.role_id 
    left join
        permissions p1_1 
            on p1_1.id=p1_0.permission_id 
    where
        u1_0.id=?
Hibernate: 
    select
        count(rt1_0.id) 
    from
        refresh_tokens rt1_0 
    where
        rt1_0.user_id=? 
        and rt1_0.is_revoked=false 
        and rt1_0.expiryDate>?
Hibernate: 
    insert 
    into
        refresh_tokens
        (created_at, device_info, expiryDate, ip_address, is_revoked, token, user_id) 
    values
        (?, ?, ?, ?, ?, ?, ?) 
    returning id
Hibernate: 
    select
        rt1_0.id,
        rt1_0.created_at,
        rt1_0.device_info,
        rt1_0.expiryDate,
        rt1_0.ip_address,
        rt1_0.is_revoked,
        rt1_0.token,
        rt1_0.user_id 
    from
        refresh_tokens rt1_0 
    where
        rt1_0.token=?
Hibernate: 
    delete 
    from
        refresh_tokens 
    where
        id=?
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        u1_0.role_id,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
Hibernate: 
    select
        r1_0.id,
        r1_0.name,
        p1_0.role_id,
        p1_1.id,
        p1_1.category,
        p1_1.functionKey,
        p1_1.functionName 
    from
        roles r1_0 
    left join
        role_permissions p1_0 
            on r1_0.id=p1_0.role_id 
    left join
        permissions p1_1 
            on p1_1.id=p1_0.permission_id 
    where
        r1_0.id=?
Hibernate: 
    select
        u1_0.id,
        u1_0.email,
        u1_0.enabled,
        u1_0.password,
        r1_0.id,
        r1_0.name,
        p1_0.role_id,
        p1_1.id,
        p1_1.category,
        p1_1.functionKey,
        p1_1.functionName,
        u1_0.totpenabled,
        u1_0.totp_secret,
        u1_0.username 
    from
        users u1_0 
    left join
        roles r1_0 
            on r1_0.id=u1_0.role_id 
    left join
        role_permissions p1_0 
            on r1_0.id=p1_0.role_id 
    left join
        permissions p1_1 
            on p1_1.id=p1_0.permission_id 
    where
        u1_0.id=?
Hibernate: 
    update
        refresh_tokens rt1_0 
    set
        is_revoked=true 
    where
        rt1_0.user_id=?

> Task :bootRun FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':bootRun'.
> Process 'command '/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-21.0.3/Contents/Home/bin/java'' finished with non-zero exit value 137

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 5m 3s
4 actionable tasks: 3 executed, 1 up-to-date
