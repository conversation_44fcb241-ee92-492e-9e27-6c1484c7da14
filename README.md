# Parabella CSRD FAQ Tool

A comprehensive enterprise platform for **Corporate Sustainability Reporting Directive (CSRD)** compliance, featuring AI-powered document processing, multi-module ESG reporting, and intelligent data analytics.

## 🚀 Overview

The Parabella CSRD FAQ Tool is a production-ready enterprise application that streamlines CSRD compliance through:

- **🤖 AI-Powered Intelligence**: GPT-4o-mini integration with vector search for document analysis and auto-completion
- **📊 Multi-Module Architecture**: Three specialized modules for complete ESG reporting
- **🔒 Enterprise Security**: JWT authentication with 2FA, role-based access control, and comprehensive audit trails
- **⚡ High Performance**: Dual-database architecture optimized for both business logic and AI operations
- **🌐 Production Ready**: Flyway migrations, OpenAPI documentation, and cloud deployment support

## 📚 Documentation

### Quick Links
- **[📖 Documentation Index](documentation/documentation-index.md)** - Complete navigation guide for all documentation
- **[🏗️ System Architecture](documentation/system-architecture.md)** - Comprehensive system design and data flows
- **[🔌 API Reference](documentation/api-reference.md)** - Complete REST API documentation
- **[🤖 AI Integration Guide](documentation/ai-integration.md)** - AI features and vector database architecture
- **[🚀 Swagger UI](http://localhost:8080/swagger-ui.html)** - Interactive API explorer (when running locally)

### Documentation Structure

#### 🏗️ Foundation Documentation
- **[System Architecture](documentation/system-architecture.md)** - Master architecture document with module overview
- **[Database ERDs](documentation/database-erd.md)** - Complete entity relationship diagrams
- **[Database Structure](documentation/database-structure.md)** - Schema, migrations, and performance optimization
- **[Architecture Overview](documentation/architecture-overview.md)** - Traditional layered architecture view

#### 🔌 API & Integration
- **[API Reference](documentation/api-reference.md)** - Comprehensive endpoint documentation
- **[AI Integration Guide](documentation/ai-integration.md)** - OpenAI integration and vector operations
- **[Authentication and Security](documentation/authentication-security.md)** - JWT, 2FA, and security patterns

#### 🎨 Frontend & UI
- **[Frontend Components](documentation/frontend-components.md)** - React component library documentation
- **[Module Workflows](documentation/module-workflows.md)** - Complete business process workflows

#### ⚙️ Implementation & Deployment
- **[Configuration Guide](documentation/configuration-guide.md)** - Development environment setup
- **[Service Layer](documentation/service-layer.md)** - Business logic patterns
- **[Deployment Guide](documentation/deployment-guide.md)** - Production deployment procedures
- **[Testing Strategy](documentation/testing-strategy.md)** - Testing approaches and frameworks

## 🏛️ Architecture

### Technology Stack

#### Frontend
- **Framework**: React 18.3.1 with TypeScript 5.5.3
- **Build Tool**: Vite 5.4.10
- **State Management**: Redux Toolkit + Context API
- **UI Framework**: React Bootstrap 2.10.5 + Custom SCSS
- **Charts**: Chart.js, Recharts, ApexCharts
- **Forms**: Formik + Yup validation

#### Backend
- **Framework**: Spring Boot 3.3.0 with Java 21
- **Database**: PostgreSQL with Hibernate + pgvector extension
- **Security**: Spring Security with JWT + TOTP 2FA
- **API Documentation**: SpringDoc OpenAPI 3 (Swagger)
- **Database Migrations**: Flyway 10.4.1
- **Audit Trail**: Hibernate Envers

#### AI & Analytics
- **LLM**: OpenAI GPT-4o-mini
- **Vector Database**: PostgreSQL + pgvector
- **Embeddings**: OpenAI text-embedding-ada-002
- **Document Processing**: Apache POI, PDFBox
- **Semantic Search**: HNSW indexing with cosine similarity

### Business Modules

#### 🎯 DMA Module (Mithril) - Double Materiality Assessment
- Stakeholder engagement and surveys
- Impact and financial materiality scoring
- IRO (Impact, Risk, Opportunity) evaluation
- Value chain analysis

#### 📊 CSRD Module (Elessar) - Compliance Reporting
- ESRS-aligned data collection
- AI-powered document processing
- Automated report generation
- Progress tracking and validation

#### 🌱 PCF Module (Vilya) - Product Carbon Footprint
- Emission calculations (Scope 1, 2, 3)
- Product lifecycle assessment
- Carbon intensity metrics
- Supply chain integration

## 🚀 Getting Started

### Prerequisites

- **Java**: JDK 21 or higher
- **Node.js**: 18.x or higher
- **PostgreSQL**: 15.x with pgvector extension
- **OpenAI API Key**: For AI features

### Quick Start

#### Backend Setup
```bash
# Clone the repository
git clone https://github.com/your-org/ParabellaCSRDFaqTool.git
cd ParabellaCSRDFaqTool

# Set up environment variables
cp .env.example .env
# Edit .env with your database and OpenAI credentials

# Build and run the backend
./gradlew bootRun
```

#### Frontend Setup
```bash
# Navigate to frontend directory
cd parabella_frontend_nowa/parabella_elessar

# Install dependencies
npm install

# Start development server
npm run dev
```

#### Database Setup
```bash
# Create databases
createdb parabella_csrd_db
createdb parabella_vector_db

# Enable pgvector extension
psql -d parabella_vector_db -c "CREATE EXTENSION vector;"

# Migrations run automatically on startup via Flyway
```

### Access Points

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8080
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **API Docs**: http://localhost:8080/v3/api-docs

## 🔑 Key Features

### AI-Powered Capabilities
- **Document Intelligence**: Automated processing and analysis of sustainability documents
- **Semantic Search**: Context-aware information retrieval from processed documents
- **Auto-completion**: AI suggestions for CSRD datapoints based on company data
- **Conversational Interface**: Interactive guidance through compliance workflows

### Security & Compliance
- **Authentication**: JWT tokens with refresh mechanism
- **2FA Support**: TOTP-based two-factor authentication
- **Role-Based Access**: Granular permissions system
- **Audit Trail**: Complete history tracking with Hibernate Envers
- **Data Encryption**: BCrypt password hashing, encrypted sensitive data

### Performance & Scalability
- **Dual Database**: Separate databases for business logic and vector operations
- **Connection Pooling**: HikariCP with optimized settings
- **Async Processing**: Document processing in background queues
- **Caching Strategy**: Redis-ready caching layer
- **Cloud Ready**: Containerized deployment with Google Cloud support

## 📋 API Examples

### Authentication
```bash
# Register new user
curl -X POST http://localhost:8080/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john.doe",
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# Login
curl -X POST http://localhost:8080/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john.doe",
    "password": "SecurePass123!"
  }'
```

### AI Auto-completion
```bash
# Get AI suggestion for CSRD datapoint
curl -X POST http://localhost:8080/api/ai/autofill \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "datapointId": "csrd_field_123",
    "query": "Climate change mitigation approach",
    "projectId": "project_456"
  }'
```

## 🛠️ Development

### Project Structure
```
ParabellaCSRDFaqTool/
├── src/                          # Backend source code
│   ├── main/java/               # Java source files
│   │   └── com/example/         # Main package
│   │       └── parabella_csrd_db/
│   │           ├── controller/   # REST endpoints
│   │           ├── service/      # Business logic
│   │           ├── database/     # Data access layer
│   │           └── security/     # Security config
│   └── main/resources/          # Configuration files
├── parabella_frontend_nowa/     # Frontend application
│   └── parabella_elessar/       # React SPA
│       └── src/
│           ├── ui_components/    # React components
│           ├── services/         # API services
│           └── assets/          # Static resources
├── documentation/               # Comprehensive docs
├── build.gradle                 # Backend build config
└── README.md                   # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Code Standards

- **Java**: Follow Spring Boot best practices, use Lombok for boilerplate
- **TypeScript**: Strict mode enabled, ESLint configuration provided
- **Database**: Snake_case for tables/columns, proper indexing for performance
- **API**: RESTful design, comprehensive error handling
- **Documentation**: Keep docs updated with code changes

## 🚀 Deployment

### Production Deployment

The application supports multiple deployment options:

- **Google Cloud Run**: Containerized deployment with Cloud SQL
- **Kubernetes**: Helm charts available for K8s deployment
- **Traditional**: JAR deployment with external PostgreSQL

See [Deployment Guide](documentation/deployment-guide.md) for detailed instructions.

### Environment Variables

Key environment variables for production:

```bash
# Database Configuration
SPRING_DATASOURCE_URL=**************************************************
SPRING_DATASOURCE_USERNAME=your_username
SPRING_DATASOURCE_PASSWORD=your_password

# Vector Database
VECTOR_DB_URL=****************************************************
VECTOR_DB_USERNAME=your_username
VECTOR_DB_PASSWORD=your_password

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=86400

# Application
SPRING_PROFILES_ACTIVE=production
```

## 📊 Monitoring & Maintenance

- **Health Checks**: Available at `/actuator/health`
- **Metrics**: Micrometer integration for performance monitoring
- **Logging**: SLF4J with Logback, structured JSON logging
- **Database Monitoring**: Connection pool metrics, slow query logging

## 🤝 Support & Resources

- **Documentation**: See [Documentation Index](documentation/documentation-index.md) for complete guide
- **API Explorer**: Use Swagger UI for interactive API testing
- **Issue Tracking**: Report bugs via GitHub Issues
- **Security**: For security concerns, contact <EMAIL>

## 📄 License

This project is proprietary software. All rights reserved.

---

Built with ❤️ for sustainable business transformation
