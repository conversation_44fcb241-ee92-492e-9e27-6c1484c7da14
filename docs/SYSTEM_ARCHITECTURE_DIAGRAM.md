# System Architecture Diagrams

## Overview
This document contains comprehensive system architecture diagrams showing the updated authentication and session management system integration with the Parabella CSRD FAQ Tool.

## 1. High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Tier"
        Browser[Web Browser]
        Mobile[Mobile Browser]
    end
    
    subgraph "Presentation Tier"
        LB[Load Balancer]
        CDN[CDN/Static Assets]
        FE[React Frontend SPA]
    end
    
    subgraph "Application Tier"
        API[Spring Boot API]
        Auth[Authentication Service]
        Business[Business Services]
        Background[Background Jobs]
    end
    
    subgraph "Data Tier"
        DB[(PostgreSQL Database)]
        Redis[(Redis Cache)]
        FileStore[(File Storage)]
    end
    
    subgraph "External Services"
        Email[Email Service]
        AI[OpenAI API]
        Monitor[Monitoring Service]
    end
    
    Browser --> LB
    Mobile --> LB
    LB --> CDN
    LB --> FE
    FE --> API
    API --> Auth
    API --> Business
    API --> Background
    Auth --> DB
    Business --> DB
    Business --> Redis
    Business --> FileStore
    Background --> Email
    Business --> AI
    API --> Monitor
```

## 2. Authentication Flow Architecture

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant LoadBalancer
    participant AuthService
    participant Database
    participant EmailService
    
    Note over User,EmailService: User Registration Flow
    User->>Frontend: Register (username, email, password)
    Frontend->>LoadBalancer: POST /api/auth/signup
    LoadBalancer->>AuthService: Forward request
    AuthService->>Database: Create user + TOTP secret
    AuthService->>Frontend: Return QR code for 2FA setup
    Frontend->>User: Display QR code
    User->>Frontend: Scan QR + enter TOTP code
    Frontend->>AuthService: POST /api/auth/verify2fa
    AuthService->>Database: Enable 2FA for user
    
    Note over User,EmailService: Login Flow
    User->>Frontend: Login (username, password, TOTP)
    Frontend->>AuthService: POST /api/auth/signin
    AuthService->>Database: Validate credentials + 2FA
    AuthService->>Database: Create refresh token
    AuthService->>Frontend: Return JWT + refresh token
    Frontend->>Frontend: Store tokens securely
    
    Note over User,EmailService: API Request Flow
    Frontend->>Frontend: Check token expiry
    alt Token expiring soon
        Frontend->>AuthService: POST /api/auth/refresh
        AuthService->>Database: Validate refresh token
        AuthService->>Database: Create new token pair
        AuthService->>Frontend: Return new tokens
    end
    Frontend->>LoadBalancer: API request with JWT
    LoadBalancer->>AuthService: Validate JWT
    AuthService->>LoadBalancer: Token valid + user context
    LoadBalancer->>Frontend: API response
```

## 3. Component Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Architecture"
        subgraph "Core Services"
            AuthContext[AuthContext Provider]
            TokenService[Token Service]
            ApiService[API Service]
        end
        
        subgraph "UI Components"
            LoginComp[Login Component]
            SessionWarning[Session Warning]
            RouteGuards[Route Guards]
            PermissionGuards[Permission Guards]
        end
        
        subgraph "Business Modules"
            CSRDModule[CSRD Module]
            DMAModule[DMA Module]
            PCFModule[PCF Module]
            UserMgmt[User Management]
        end
        
        subgraph "Infrastructure"
            Interceptors[HTTP Interceptors]
            ErrorHandling[Error Boundaries]
            StateManagement[Redux Store]
        end
    end
    
    subgraph "Backend Architecture"
        subgraph "Controllers"
            AuthController[Auth Controller]
            UserController[User Controller]
            ProjectController[Project Controller]
        end
        
        subgraph "Services"
            AuthService[Auth Service]
            RefreshTokenService[Refresh Token Service]
            UserService[User Service]
            ProjectService[Project Service]
        end
        
        subgraph "Security"
            JwtUtils[JWT Utilities]
            SecurityConfig[Security Config]
            PasswordEncoder[Password Encoder]
            TotpUtils[TOTP Utils]
        end
        
        subgraph "Data Layer"
            UserRepo[User Repository]
            TokenRepo[Token Repository]
            ProjectRepo[Project Repository]
            AuditRepo[Audit Repository]
        end
    end
    
    AuthContext --> TokenService
    TokenService --> ApiService
    ApiService --> Interceptors
    LoginComp --> AuthContext
    SessionWarning --> AuthContext
    RouteGuards --> AuthContext
    PermissionGuards --> AuthContext
    
    CSRDModule --> AuthContext
    DMAModule --> AuthContext
    PCFModule --> AuthContext
    UserMgmt --> AuthContext
    
    Interceptors --> AuthController
    AuthController --> AuthService
    AuthController --> RefreshTokenService
    AuthService --> JwtUtils
    AuthService --> TotpUtils
    RefreshTokenService --> TokenRepo
    UserService --> UserRepo
    
    SecurityConfig --> JwtUtils
    SecurityConfig --> PasswordEncoder
```

## 4. Data Flow Architecture

```mermaid
graph LR
    subgraph "User Context Flow"
        Login[User Login] --> AuthToken[JWT Token]
        AuthToken --> UserContext[User Context]
        UserContext --> Permissions[User Permissions]
        Permissions --> DataAccess[Data Access]
    end
    
    subgraph "Request Processing Flow"
        Request[API Request] --> Interceptor[Request Interceptor]
        Interceptor --> TokenCheck{Token Valid?}
        TokenCheck -->|No| RefreshFlow[Token Refresh]
        TokenCheck -->|Yes| AuthHeader[Add Auth Header]
        RefreshFlow --> NewToken[New Token Pair]
        NewToken --> AuthHeader
        AuthHeader --> Backend[Backend Processing]
        Backend --> PermissionCheck{Permission Check}
        PermissionCheck -->|Allowed| DataQuery[Database Query]
        PermissionCheck -->|Denied| AccessDenied[403 Forbidden]
        DataQuery --> Response[API Response]
    end
    
    subgraph "Session Management Flow"
        SessionStart[Session Start] --> ActivityMonitor[Activity Monitor]
        ActivityMonitor --> TimeoutCheck{Near Timeout?}
        TimeoutCheck -->|Yes| WarningModal[Timeout Warning]
        TimeoutCheck -->|No| ContinueSession[Continue Session]
        WarningModal --> UserChoice{User Action}
        UserChoice -->|Extend| RefreshSession[Refresh Session]
        UserChoice -->|Logout| ClearSession[Clear Session]
        RefreshSession --> ContinueSession
        ClearSession --> LoginPage[Login Page]
    end
```

## 5. Security Architecture Layers

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Layer 1: Network Security"
            HTTPS[HTTPS/TLS 1.3]
            CORS[CORS Policy]
            CSP[Content Security Policy]
        end
        
        subgraph "Layer 2: Authentication"
            JWT[JWT Tokens]
            TOTP[2FA/TOTP]
            RefreshTokens[Refresh Tokens]
        end
        
        subgraph "Layer 3: Authorization"
            RBAC[Role-Based Access]
            Permissions[Fine-grained Permissions]
            ResourceOwnership[Resource Ownership]
        end
        
        subgraph "Layer 4: Session Management"
            SessionLimits[Session Limits]
            TokenRotation[Token Rotation]
            DeviceTracking[Device Tracking]
        end
        
        subgraph "Layer 5: Data Protection"
            InputValidation[Input Validation]
            SQLInjectionPrevention[SQL Injection Prevention]
            XSSPrevention[XSS Prevention]
        end
        
        subgraph "Layer 6: Monitoring & Audit"
            SecurityLogging[Security Logging]
            AnomalyDetection[Anomaly Detection]
            AuditTrail[Audit Trail]
        end
    end
    
    HTTPS --> JWT
    CORS --> TOTP
    CSP --> RefreshTokens
    JWT --> RBAC
    TOTP --> Permissions
    RefreshTokens --> ResourceOwnership
    RBAC --> SessionLimits
    Permissions --> TokenRotation
    ResourceOwnership --> DeviceTracking
    SessionLimits --> InputValidation
    TokenRotation --> SQLInjectionPrevention
    DeviceTracking --> XSSPrevention
    InputValidation --> SecurityLogging
    SQLInjectionPrevention --> AnomalyDetection
    XSSPrevention --> AuditTrail
```

## 6. Database Architecture

```mermaid
erDiagram
    users {
        bigserial id PK
        varchar username UK
        varchar email UK
        varchar password_hash
        varchar totp_secret
        boolean totp_enabled
        boolean enabled
        timestamp created_at
        timestamp updated_at
    }
    
    roles {
        bigserial id PK
        varchar name UK
        varchar description
        timestamp created_at
    }
    
    permissions {
        bigserial id PK
        varchar function_key UK
        varchar description
        varchar category
    }
    
    role_permissions {
        bigint role_id PK,FK
        bigint permission_id PK,FK
    }
    
    refresh_tokens {
        bigserial id PK
        bigint user_id FK
        varchar token UK
        timestamp expiry_date
        timestamp created_at
        varchar device_info
        varchar ip_address
        boolean is_revoked
    }
    
    user_roles {
        bigserial id PK
        bigint user_id FK
        bigint role_id FK
        timestamp assigned_at
    }
    
    security_events {
        bigserial id PK
        varchar event_type
        varchar username
        varchar ip_address
        varchar user_agent
        varchar resource_accessed
        varchar outcome
        timestamp timestamp
        text additional_info
    }
    
    projects {
        bigserial id PK
        bigint owner_id FK
        varchar name
        text description
        varchar status
        timestamp created_at
        timestamp updated_at
        varchar created_by
        varchar last_modified_by
    }
    
    project_collaborators {
        bigserial id PK
        bigint project_id FK
        bigint user_id FK
        varchar permission_level
        timestamp added_at
    }
    
    users ||--o{ refresh_tokens : "has"
    users ||--o{ user_roles : "has"
    roles ||--o{ user_roles : "assigned to"
    roles ||--o{ role_permissions : "has"
    permissions ||--o{ role_permissions : "granted to"
    users ||--o{ projects : "owns"
    users ||--o{ project_collaborators : "collaborates on"
    projects ||--o{ project_collaborators : "has"
    users ||--o{ security_events : "generates"
```

## 7. Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        subgraph "Load Balancer Tier"
            ALB[Application Load Balancer]
            SSL[SSL Termination]
        end
        
        subgraph "Application Tier"
            FE1[Frontend Instance 1]
            FE2[Frontend Instance 2]
            API1[API Instance 1]
            API2[API Instance 2]
        end
        
        subgraph "Database Tier"
            PrimaryDB[(Primary PostgreSQL)]
            ReplicaDB[(Read Replica)]
            RedisCluster[(Redis Cluster)]
        end
        
        subgraph "Monitoring & Logging"
            Prometheus[Prometheus]
            Grafana[Grafana]
            ELK[ELK Stack]
        end
        
        subgraph "External Services"
            EmailSVC[Email Service]
            OpenAI[OpenAI API]
            FileStorage[Cloud Storage]
        end
    end
    
    subgraph "CI/CD Pipeline"
        GitHub[GitHub Repository]
        Actions[GitHub Actions]
        Registry[Container Registry]
        Deploy[Deployment Service]
    end
    
    Users[Users] --> ALB
    ALB --> SSL
    SSL --> FE1
    SSL --> FE2
    FE1 --> API1
    FE2 --> API2
    API1 --> PrimaryDB
    API2 --> PrimaryDB
    API1 --> ReplicaDB
    API2 --> ReplicaDB
    API1 --> RedisCluster
    API2 --> RedisCluster
    
    API1 --> EmailSVC
    API2 --> OpenAI
    API1 --> FileStorage
    
    GitHub --> Actions
    Actions --> Registry
    Registry --> Deploy
    Deploy --> FE1
    Deploy --> FE2
    Deploy --> API1
    Deploy --> API2
    
    Prometheus --> API1
    Prometheus --> API2
    Grafana --> Prometheus
    ELK --> API1
    ELK --> API2
```

## 8. Token Lifecycle Management

```mermaid
stateDiagram-v2
    [*] --> UserLogin
    UserLogin --> TokenGeneration : Credentials + 2FA Valid
    TokenGeneration --> ActiveSession : Store Tokens
    
    ActiveSession --> TokenRefresh : Token Expiring
    ActiveSession --> APIRequest : Normal Operation
    ActiveSession --> SessionWarning : 2min Before Expiry
    
    TokenRefresh --> ActiveSession : New Token Pair
    TokenRefresh --> ForceLogout : Refresh Failed
    
    APIRequest --> ActiveSession : Success
    APIRequest --> TokenRefresh : 401 Unauthorized
    
    SessionWarning --> ExtendSession : User Extends
    SessionWarning --> ForceLogout : User Ignores/Logout
    SessionWarning --> AutoLogout : Timeout
    
    ExtendSession --> TokenRefresh : Manual Refresh
    
    ForceLogout --> TokenRevocation : Revoke All Tokens
    AutoLogout --> TokenRevocation : Revoke All Tokens
    
    TokenRevocation --> [*] : Clear Session
```

## 9. Error Handling Architecture

```mermaid
graph TB
    subgraph "Frontend Error Handling"
        APIError[API Error] --> ErrorInterceptor[Response Interceptor]
        ErrorInterceptor --> ErrorType{Error Type}
        ErrorType -->|401| TokenRefresh[Automatic Token Refresh]
        ErrorType -->|403| PermissionError[Permission Denied]
        ErrorType -->|422| ValidationError[Validation Error]
        ErrorType -->|500| ServerError[Server Error]
        
        TokenRefresh --> RetryRequest[Retry Original Request]
        TokenRefresh --> LoginRequired[Force Re-login]
        
        PermissionError --> AccessDenied[Access Denied Page]
        ValidationError --> FormErrors[Show Form Errors]
        ServerError --> ErrorBoundary[Error Boundary]
    end
    
    subgraph "Backend Error Handling"
        Exception[Exception Thrown] --> ExceptionHandler[Global Exception Handler]
        ExceptionHandler --> ExceptionType{Exception Type}
        ExceptionType -->|AuthenticationException| Unauthorized[401 Response]
        ExceptionType -->|AccessDeniedException| Forbidden[403 Response]
        ExceptionType -->|ValidationException| BadRequest[422 Response]
        ExceptionType -->|RuntimeException| InternalError[500 Response]
        
        Unauthorized --> SecurityLog[Log Security Event]
        Forbidden --> SecurityLog
        BadRequest --> ValidationLog[Log Validation Error]
        InternalError --> ErrorLog[Log System Error]
    end
    
    SecurityLog --> AuditTrail[Audit Trail]
    ValidationLog --> Monitoring[Error Monitoring]
    ErrorLog --> AlertSystem[Alert System]
```

This comprehensive set of architecture diagrams provides a complete view of how the authentication and session management system integrates with the overall Parabella CSRD FAQ Tool architecture, showing the flow of data, security layers, and system interactions at various levels.