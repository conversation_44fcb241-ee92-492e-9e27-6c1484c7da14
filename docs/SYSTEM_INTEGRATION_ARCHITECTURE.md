# System Integration Architecture

## Overview
This document describes how the new authentication and session management system integrates with the overall Parabella CSRD FAQ Tool architecture, including the impact on existing modules and integration patterns.

## System Components Integration

### 1. Authentication Layer Integration

```mermaid
graph TB
    subgraph "Frontend Architecture"
        subgraph "Application Layer"
            AuthContext[Auth Context Provider]
            RouteGuard[Route Protection]
            PermissionGuard[Permission Guards]
        end
        
        subgraph "Business Modules"
            CSRD[CSRD Module]
            DMA[DMA Module] 
            PCF[PCF Module]
            UserMgmt[User Management]
        end
        
        subgraph "Infrastructure Layer"
            TokenService[Token Service]
            ApiService[API Service]
            SessionMgmt[Session Management]
        end
    end
    
    subgraph "Backend Architecture"
        subgraph "Presentation Layer"
            AuthController[Auth Controller]
            UserController[User Controller]
            CSRDController[CSRD Controllers]
            DMAController[DMA Controllers]
        end
        
        subgraph "Business Layer"
            AuthService[Auth Service]
            RefreshTokenService[RefreshToken Service]
            UserService[User Service]
            BusinessServices[Business Services]
        end
        
        subgraph "Data Layer"
            AuthRepo[Auth Repositories]
            BusinessRepo[Business Repositories]
            AuditTrail[Audit Trail]
        end
    end
    
    AuthContext --> CSRD
    AuthContext --> DMA
    AuthContext --> PCF
    AuthContext --> UserMgmt
    
    TokenService --> ApiService
    ApiService --> AuthController
    ApiService --> CSRDController
    ApiService --> DMAController
    
    AuthService --> RefreshTokenService
    AuthService --> UserService
    BusinessServices --> AuditTrail
```

### 2. Module-Level Integration Patterns

#### CSRD Module Integration
```typescript
// CSRD components now use authentication context
const CSRDDashboard: React.FC = () => {
    const { user, hasPermission } = useAuth();
    
    // Permission-based feature access
    if (!hasPermission('csrd.read')) {
        return <AccessDenied />;
    }
    
    // User-specific data filtering
    const userProjects = useUserProjects(user?.id);
    
    return <CSRDContent projects={userProjects} />;
};
```

#### DMA Module Integration
```typescript
// DMA module with role-based functionality
const DMAProjectCreation: React.FC = () => {
    const { user, hasPermission } = useAuth();
    
    const canCreateProject = hasPermission('dma.create');
    const canAssignUsers = hasPermission('user.assign');
    
    return (
        <ProjectForm 
            canCreate={canCreateProject}
            canAssign={canAssignUsers}
            currentUser={user}
        />
    );
};
```

#### PCF Module Integration
```typescript
// PCF module with session-aware data persistence
const PCFAnalysis: React.FC = () => {
    const { user, isAuthenticated } = useAuth();
    
    // Auto-save with user context
    const { autoSave } = useAutoSave({
        userId: user?.id,
        isAuthenticated,
        saveInterval: 30000
    });
    
    return <AnalysisForm onDataChange={autoSave} />;
};
```

## Security Integration Patterns

### 1. Role-Based Access Control (RBAC) Integration

```mermaid
graph LR
    User[User Entity] --> Role[Role Entity]
    Role --> Permission[Permission Entity]
    Permission --> Function[Function Keys]
    
    subgraph "Frontend Guards"
        RouteGuard[Route Guard]
        ComponentGuard[Component Guard]
        FeatureGuard[Feature Guard]
    end
    
    subgraph "Backend Security"
        MethodSecurity[Method Security]
        PreAuthorize[PreAuthorize]
        DataFiltering[Data Filtering]
    end
    
    Function --> RouteGuard
    Function --> ComponentGuard
    Function --> FeatureGuard
    Function --> MethodSecurity
    Function --> PreAuthorize
    Function --> DataFiltering
```

#### Frontend Permission Integration
```typescript
// Permission-based routing
const ProtectedRoute: React.FC<{ 
    permission: string; 
    children: React.ReactNode 
}> = ({ permission, children }) => {
    const { hasPermission } = useAuth();
    
    if (!hasPermission(permission)) {
        return <Navigate to="/access-denied" />;
    }
    
    return <>{children}</>;
};

// Component-level permissions
const AdminPanel: React.FC = () => {
    const { hasPermission } = useAuth();
    
    return (
        <div>
            {hasPermission('user.manage') && <UserManagement />}
            {hasPermission('role.manage') && <RoleManagement />}
            {hasPermission('system.config') && <SystemSettings />}
        </div>
    );
};
```

#### Backend Security Integration
```java
// Method-level security
@RestController
@RequestMapping("/api/csrd")
@PreAuthorize("hasRole('USER')")
public class CSRDController {
    
    @GetMapping("/projects")
    @PreAuthorize("hasPermission('csrd.read')")
    public ResponseEntity<List<Project>> getUserProjects(Authentication auth) {
        UserDetailsImpl user = (UserDetailsImpl) auth.getPrincipal();
        return ResponseEntity.ok(projectService.getProjectsByUser(user.getId()));
    }
    
    @PostMapping("/projects")
    @PreAuthorize("hasPermission('csrd.create')")
    public ResponseEntity<Project> createProject(@RequestBody ProjectDto dto, Authentication auth) {
        // Implementation with user context
    }
}
```

### 2. Audit Trail Integration

```java
// Audit configuration
@Entity
@Audited
@EntityListeners(AuditingEntityListener.class)
public class CSRDProject extends BaseAuditedEntity {
    
    @CreatedBy
    private String createdBy;
    
    @LastModifiedBy 
    private String lastModifiedBy;
    
    @CreatedDate
    private Instant createdDate;
    
    @LastModifiedDate
    private Instant lastModifiedDate;
    
    // User context from authentication
    @PrePersist
    protected void onCreate() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.getPrincipal() instanceof UserDetailsImpl) {
            UserDetailsImpl user = (UserDetailsImpl) auth.getPrincipal();
            this.createdBy = user.getUsername();
        }
    }
}
```

## Data Flow Integration

### 1. User-Centric Data Architecture

```mermaid
graph TD
    User[Authenticated User] --> Context[User Context]
    Context --> Projects[User Projects]
    Context --> Companies[User Companies]
    Context --> Reports[User Reports]
    
    Projects --> CSRD_Data[CSRD Data]
    Projects --> DMA_Data[DMA Data]
    Projects --> PCF_Data[PCF Data]
    
    subgraph "Data Filtering"
        OwnedData[Owned Data]
        SharedData[Shared Data]
        PublicData[Public Data]
    end
    
    CSRD_Data --> OwnedData
    DMA_Data --> SharedData
    PCF_Data --> PublicData
```

#### Data Service Integration
```typescript
// User-aware data services
class CSRDDataService {
    constructor(private api: AxiosInstance, private authContext: AuthContextType) {}
    
    async getProjects(): Promise<Project[]> {
        const { user } = this.authContext;
        
        // API automatically includes user context via JWT
        const response = await this.api.get('/api/csrd/projects');
        
        // Additional client-side filtering if needed
        return response.data.filter(project => 
            project.ownerId === user?.id || 
            project.collaborators.includes(user?.id)
        );
    }
    
    async createProject(projectData: CreateProjectDto): Promise<Project> {
        // User context automatically included via authentication
        return (await this.api.post('/api/csrd/projects', projectData)).data;
    }
}
```

### 2. Session-Aware State Management

```typescript
// Redux store integration with authentication
interface AppState {
    auth: AuthState;
    csrd: CSRDState;
    dma: DMAState;
    pcf: PCFState;
}

// Session-aware reducers
const csrdReducer = (state: CSRDState, action: AnyAction): CSRDState => {
    switch (action.type) {
        case 'AUTH/LOGOUT':
            // Clear user-specific data on logout
            return {
                ...initialState,
                projects: [],
                currentProject: null
            };
        case 'AUTH/LOGIN':
            // Initialize user-specific data on login
            return {
                ...state,
                userId: action.payload.user.id
            };
        default:
            return state;
    }
};
```

## API Integration Patterns

### 1. Centralized API Configuration

```typescript
// API service factory with authentication integration
class ApiServiceFactory {
    static createAuthenticatedApi(): AxiosInstance {
        const api = axios.create({
            baseURL: API_BASE_URL,
            timeout: 30000
        });
        
        // Add authentication interceptors
        this.addAuthInterceptors(api);
        
        // Add error handling
        this.addErrorInterceptors(api);
        
        return api;
    }
    
    private static addAuthInterceptors(api: AxiosInstance) {
        // Request interceptor for token attachment
        api.interceptors.request.use(
            async (config) => {
                const token = tokenService.getAccessToken();
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            }
        );
        
        // Response interceptor for token refresh
        api.interceptors.response.use(
            (response) => response,
            async (error) => {
                // Handle 401 errors with token refresh
                if (error.response?.status === 401) {
                    return this.handleTokenRefresh(error);
                }
                return Promise.reject(error);
            }
        );
    }
}
```

### 2. Service Layer Integration

```typescript
// Base service class with authentication
abstract class BaseApiService {
    protected api: AxiosInstance;
    protected authContext: AuthContextType;
    
    constructor(authContext: AuthContextType) {
        this.api = ApiServiceFactory.createAuthenticatedApi();
        this.authContext = authContext;
    }
    
    protected async request<T>(config: AxiosRequestConfig): Promise<T> {
        try {
            const response = await this.api.request<T>(config);
            return response.data;
        } catch (error) {
            this.handleError(error);
            throw error;
        }
    }
    
    private handleError(error: any) {
        if (error.response?.status === 403) {
            // Handle permission denied
            console.error('Access denied:', error.response.data);
        }
    }
}

// Specific service implementations
class CSRDApiService extends BaseApiService {
    async getProjects(): Promise<Project[]> {
        return this.request<Project[]>({
            method: 'GET',
            url: '/api/csrd/projects'
        });
    }
}
```

## Error Handling Integration

### 1. Global Error Boundaries

```typescript
// Authentication-aware error boundary
class AuthAwareErrorBoundary extends React.Component<Props, State> {
    static getDerivedStateFromError(error: Error): State {
        if (error.name === 'AuthenticationError') {
            return { hasError: true, errorType: 'auth' };
        }
        if (error.name === 'PermissionError') {
            return { hasError: true, errorType: 'permission' };
        }
        return { hasError: true, errorType: 'general' };
    }
    
    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        // Log error with user context
        const { user } = this.context as AuthContextType;
        console.error('Application error:', {
            error: error.message,
            user: user?.username,
            timestamp: new Date().toISOString(),
            ...errorInfo
        });
    }
    
    render() {
        if (this.state.hasError) {
            switch (this.state.errorType) {
                case 'auth':
                    return <LoginRequired />;
                case 'permission':
                    return <AccessDenied />;
                default:
                    return <GeneralError />;
            }
        }
        return this.props.children;
    }
}
```

### 2. API Error Handling

```typescript
// Centralized error handling with authentication awareness
class ErrorHandler {
    static handleApiError(error: AxiosError, authContext: AuthContextType) {
        const { logout } = authContext;
        
        switch (error.response?.status) {
            case 401:
                // Authentication expired - handled by interceptor
                break;
            case 403:
                // Permission denied
                this.showPermissionError();
                break;
            case 422:
                // Validation error
                this.showValidationError(error.response.data);
                break;
            default:
                this.showGenericError();
        }
    }
    
    private static showPermissionError() {
        // Show permission denied message
        toast.error('You do not have permission to perform this action');
    }
}
```

## Deployment Integration

### 1. Environment Configuration

```yaml
# Production configuration
spring:
  security:
    jwt:
      secret: ${JWT_SECRET}
      expiration: 900000  # 15 minutes
      refresh-expiration: 604800000  # 7 days
    
    cors:
      allowed-origins: 
        - https://app.parabella.com
        - https://api.parabella.com
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"
      allow-credentials: true

  datasource:
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
```

### 2. Docker Integration

```dockerfile
# Frontend Dockerfile with authentication config
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
# Build with authentication environment variables
ARG VITE_API_BASE_URL
ARG VITE_APP_ENV
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_APP_ENV=$VITE_APP_ENV

RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
```

## Performance Integration

### 1. Caching Strategies

```typescript
// User-aware caching
class CacheService {
    private cache = new Map<string, CacheEntry>();
    
    getCacheKey(endpoint: string, userId?: number): string {
        return userId ? `${endpoint}:user:${userId}` : endpoint;
    }
    
    async get<T>(key: string, fetcher: () => Promise<T>, ttl: number = 300000): Promise<T> {
        const cached = this.cache.get(key);
        if (cached && !this.isExpired(cached)) {
            return cached.data;
        }
        
        const data = await fetcher();
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
        
        return data;
    }
    
    clearUserCache(userId: number) {
        const userKeys = Array.from(this.cache.keys())
            .filter(key => key.includes(`user:${userId}`));
        userKeys.forEach(key => this.cache.delete(key));
    }
}
```

### 2. Lazy Loading Integration

```typescript
// Authentication-aware lazy loading
const LazyCSRDModule = React.lazy(() => 
    import('./modules/CSRD').then(module => ({
        default: withAuthenticationCheck(module.CSRDModule, 'csrd.access')
    }))
);

const LazyDMAModule = React.lazy(() =>
    import('./modules/DMA').then(module => ({
        default: withAuthenticationCheck(module.DMAModule, 'dma.access')
    }))
);

// HOC for authentication checks
function withAuthenticationCheck<P>(
    Component: React.ComponentType<P>, 
    requiredPermission: string
) {
    return (props: P) => {
        const { hasPermission } = useAuth();
        
        if (!hasPermission(requiredPermission)) {
            return <AccessDenied />;
        }
        
        return <Component {...props} />;
    };
}
```

This integration architecture ensures that the new authentication system seamlessly integrates with all existing modules while providing enhanced security, better user experience, and maintainable code structure.