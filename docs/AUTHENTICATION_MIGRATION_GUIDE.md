# Authentication System Migration Guide

## Overview
This guide provides step-by-step instructions for developers to migrate existing code to work with the new dual-token authentication system and understand the architectural changes.

## Quick Reference

### Key File Locations
- **Backend Authentication**: `/src/main/java/com/example/parabella_csrd_db/`
  - `controller/authentication/AuthController.java`
  - `service/authentication/RefreshTokenService.java`
  - `database/maindatabase/model/authentication/RefreshToken.java`
  - `security/jwt/JwtUtils.java`

- **Frontend Authentication**: `/parabella_frontend_nowa/parabella_elessar/src/`
  - `services/authService.ts`
  - `services/tokenService.ts`
  - `services/api.ts`
  - `services/AuthContext.tsx`

### Key Changes Summary
1. **Dual-token system**: Access token (15min) + Refresh token (7 days)
2. **Automatic token refresh**: Handled by Axios interceptors
3. **Session management**: Visual warnings and extensions
4. **Secure token storage**: New tokenService with encryption consideration
5. **Invitation conflict resolution**: Special handling for invitation links

## Migration Steps

### 1. Backend Migration

#### Step 1.1: Database Migration
Run the following SQL to create the refresh tokens table:

```sql
-- Create refresh_tokens table
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL,
    device_info VARCHAR(500),
    ip_address VARCHAR(45),
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT fk_refresh_token_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_refresh_token_user ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_token_expiry ON refresh_tokens(expiry_date);
CREATE INDEX idx_refresh_token_active ON refresh_tokens(is_revoked, expiry_date);
```

#### Step 1.2: Update Application Properties
Add the following configuration:

```properties
# JWT Configuration (add to application.properties)
parabella_csrd_db.jwtExpirationMs=900000
parabella_csrd_db.jwtRefreshExpirationMs=604800000
parabella_csrd_db.maxActiveRefreshTokensPerUser=5

# Enable scheduled tasks (if not already enabled)
spring.task.scheduling.enabled=true
```

#### Step 1.3: Update Existing Controllers
If you have existing controllers that need authentication, update them:

```java
// Before: Direct token handling
@GetMapping("/user-data")
public ResponseEntity<?> getUserData(HttpServletRequest request) {
    String token = request.getHeader("Authorization");
    // Manual token validation...
}

// After: Use Authentication object
@GetMapping("/user-data")
public ResponseEntity<?> getUserData(Authentication authentication) {
    UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
    // User details automatically available
    return ResponseEntity.ok(userService.getUserData(user.getId()));
}
```

### 2. Frontend Migration

#### Step 2.1: Update API Calls
Replace direct axios usage with the configured api instance:

```typescript
// Before: Direct axios usage
import axios from 'axios';

const fetchUserData = async () => {
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/user/data', {
        headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
};

// After: Use configured api instance
import api from '../services/api';

const fetchUserData = async () => {
    // Token automatically attached by interceptor
    const response = await api.get('/api/user/data');
    return response.data;
};
```

#### Step 2.2: Update Authentication State Management
Replace old authentication patterns:

```typescript
// Before: Manual token management
const [user, setUser] = useState(null);
const [token, setToken] = useState(localStorage.getItem('token'));

useEffect(() => {
    if (token) {
        // Manual token validation
        validateToken(token).then(setUser);
    }
}, [token]);

// After: Use AuthContext
import { useAuth } from '../services/AuthContext';

const MyComponent = () => {
    const { user, isAuthenticated, hasPermission } = useAuth();
    
    if (!isAuthenticated) {
        return <LoginRequired />;
    }
    
    return <ComponentContent user={user} />;
};
```

#### Step 2.3: Update Login Components
Modernize login handling:

```typescript
// Before: Manual login handling
const handleLogin = async (credentials) => {
    try {
        const response = await axios.post('/api/auth/signin', credentials);
        localStorage.setItem('token', response.data.token);
        setUser(response.data.user);
        navigate('/dashboard');
    } catch (error) {
        setError(error.message);
    }
};

// After: Use authService with 2FA support
import { apiLogin, verify2FASetup } from '../services/authService';

const handleLogin = async (credentials) => {
    try {
        const result = await apiLogin(credentials.username, credentials.password, credentials.totpCode);
        
        if (result.success) {
            login(result.user); // AuthContext method
        } else if (result.stage === '2fa_setup') {
            setShow2FASetup(true);
            setQrCodeUrl(result.qrCodeUrl);
        } else if (result.stage === '2fa_verify') {
            setShow2FAInput(true);
        } else {
            setError(result.error);
        }
    } catch (error) {
        setError('Login failed');
    }
};
```

#### Step 2.4: Add Session Management
Include the session timeout warning in your main layout:

```typescript
// In your main App component or layout
import { SessionTimeoutWarning } from './components/authentication/SessionTimeoutWarning';

const App = () => {
    return (
        <AuthProvider>
            <Router>
                <Routes>
                    {/* Your routes */}
                </Routes>
                <SessionTimeoutWarning />
            </Router>
        </AuthProvider>
    );
};
```

### 3. Permission System Migration

#### Step 3.1: Update Permission Checks
Replace role-based checks with permission-based checks:

```typescript
// Before: Role-based checks
const canEditUser = user?.role === 'ADMIN';
const canViewReports = user?.role === 'ADMIN' || user?.role === 'MANAGER';

// After: Permission-based checks
const { hasPermission } = useAuth();
const canEditUser = hasPermission('user.edit');
const canViewReports = hasPermission('reports.view');
```

#### Step 3.2: Update Route Protection
Implement permission-based route guards:

```typescript
// Create a permission guard component
const PermissionGuard: React.FC<{
    permission: string;
    children: React.ReactNode;
    fallback?: React.ReactNode;
}> = ({ permission, children, fallback = <AccessDenied /> }) => {
    const { hasPermission } = useAuth();
    
    return hasPermission(permission) ? <>{children}</> : fallback;
};

// Use in routes
<Route 
    path="/admin/users" 
    element={
        <PermissionGuard permission="user.manage">
            <UserManagement />
        </PermissionGuard>
    } 
/>
```

### 4. Data Fetching Migration

#### Step 4.1: Update Data Services
Create typed services with authentication:

```typescript
// Before: Mixed data fetching
const fetchProjects = async () => {
    const token = getToken();
    const response = await axios.get('/api/projects', {
        headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
};

// After: Service class approach
class ProjectService {
    async getProjects(): Promise<Project[]> {
        const response = await api.get<Project[]>('/api/projects');
        return response.data;
    }
    
    async createProject(project: CreateProjectDto): Promise<Project> {
        const response = await api.post<Project>('/api/projects', project);
        return response.data;
    }
    
    async updateProject(id: number, project: UpdateProjectDto): Promise<Project> {
        const response = await api.put<Project>(`/api/projects/${id}`, project);
        return response.data;
    }
}

export const projectService = new ProjectService();
```

#### Step 4.2: Implement User-Aware Data Fetching
Add user context to data operations:

```typescript
// Custom hook for user-aware data fetching
const useUserProjects = () => {
    const { user } = useAuth();
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
        if (user) {
            projectService.getUserProjects(user.id)
                .then(setProjects)
                .catch(console.error)
                .finally(() => setLoading(false));
        }
    }, [user]);
    
    return { projects, loading };
};
```

### 5. Error Handling Migration

#### Step 5.1: Update Error Boundaries
Create authentication-aware error boundaries:

```typescript
class AuthErrorBoundary extends React.Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = { hasError: false, error: null };
    }
    
    static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error };
    }
    
    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        // Log with user context if available
        console.error('Application error:', {
            error: error.message,
            stack: error.stack,
            ...errorInfo
        });
    }
    
    render() {
        if (this.state.hasError) {
            const error = this.state.error;
            
            if (error?.name === 'AuthenticationError') {
                return <LoginRequired />;
            }
            
            if (error?.name === 'PermissionError') {
                return <AccessDenied />;
            }
            
            return <GenericError error={error} />;
        }
        
        return this.props.children;
    }
}
```

#### Step 5.2: Implement Global Error Handling
Set up centralized error handling:

```typescript
// Global error handler
const GlobalErrorHandler = {
    handleApiError: (error: AxiosError, context?: any) => {
        switch (error.response?.status) {
            case 401:
                // Handled by interceptor, but log for monitoring
                console.warn('Authentication required');
                break;
            case 403:
                console.warn('Access denied:', error.response.data);
                toast.error('You do not have permission for this action');
                break;
            case 422:
                console.warn('Validation error:', error.response.data);
                // Handle validation errors
                break;
            case 500:
                console.error('Server error:', error.response.data);
                toast.error('A server error occurred. Please try again.');
                break;
            default:
                console.error('Unhandled error:', error);
                toast.error('An unexpected error occurred');
        }
    }
};

// Use in components
try {
    await projectService.createProject(projectData);
} catch (error) {
    GlobalErrorHandler.handleApiError(error as AxiosError);
}
```

### 6. Testing Migration

#### Step 6.1: Update Component Tests
Mock the authentication context:

```typescript
// Test utilities
const mockAuthContext = {
    user: {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['User'],
        permissions: ['project.read', 'project.create'],
        token: 'mock-token'
    },
    isAuthenticated: true,
    isLoading: false,
    login: jest.fn(),
    logout: jest.fn(),
    hasPermission: jest.fn((permission) => 
        mockAuthContext.user.permissions.includes(permission)
    )
};

// In tests
const renderWithAuth = (component: React.ReactElement) => {
    return render(
        <AuthContext.Provider value={mockAuthContext}>
            {component}
        </AuthContext.Provider>
    );
};

test('should render project list for authenticated user', () => {
    renderWithAuth(<ProjectList />);
    expect(screen.getByText('My Projects')).toBeInTheDocument();
});
```

#### Step 6.2: API Testing
Update API tests for new endpoints:

```java
@Test
public void testTokenRefresh() throws Exception {
    // Create user and refresh token
    User user = createTestUser();
    RefreshToken refreshToken = refreshTokenService.createRefreshToken(
        user.getId(), "test-device", "127.0.0.1"
    );
    
    // Test refresh endpoint
    mockMvc.perform(post("/api/auth/refresh")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\"refreshToken\":\"" + refreshToken.getToken() + "\"}"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.accessToken").exists())
            .andExpect(jsonPath("$.refreshToken").exists());
}
```

## Common Migration Issues and Solutions

### Issue 1: Circular Dependencies
**Problem**: Circular import between services
**Solution**: Use dependency injection or refactor shared logic

```typescript
// Before: Direct imports causing cycles
import { authService } from './authService';
import { apiService } from './apiService';

// After: Dependency injection
class ApiService {
    constructor(private tokenService: TokenService) {}
}
```

### Issue 2: Token Storage Conflicts
**Problem**: Old and new token storage conflicting
**Solution**: Clean migration in tokenService

```typescript
// Migration logic in tokenService
const migrateFromOldStorage = () => {
    const oldToken = localStorage.getItem('token');
    const oldUser = localStorage.getItem('user');
    
    if (oldToken && !tokenService.getAccessToken()) {
        // Migrate old data
        try {
            const userData = JSON.parse(oldUser || '{}');
            tokenService.setTokens(oldToken, oldToken); // Temporary
            tokenService.setUser(userData);
        } catch (e) {
            console.warn('Failed to migrate old session data');
        }
        
        // Clean up old storage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
    }
};
```

### Issue 3: Permission Mapping
**Problem**: Old role names don't match new permission keys
**Solution**: Create mapping utility

```typescript
const legacyRolePermissionMap: Record<string, string[]> = {
    'ADMIN': ['user.manage', 'role.manage', 'system.config', 'project.create', 'project.read', 'project.edit'],
    'MANAGER': ['project.create', 'project.read', 'project.edit', 'user.assign'],
    'USER': ['project.read', 'project.create']
};

const mapLegacyRoleToPermissions = (role: string): string[] => {
    return legacyRolePermissionMap[role] || ['project.read'];
};
```

## Performance Considerations

### 1. Token Refresh Optimization
```typescript
// Prevent excessive refresh calls
let refreshPromise: Promise<string | null> | null = null;

export const optimizedRefreshToken = async (): Promise<string | null> => {
    if (refreshPromise) {
        return refreshPromise;
    }
    
    refreshPromise = refreshAccessToken();
    
    try {
        return await refreshPromise;
    } finally {
        refreshPromise = null;
    }
};
```

### 2. Permission Caching
```typescript
// Cache permissions to avoid repeated checks
const permissionCache = new Map<string, boolean>();

const cachedHasPermission = (permission: string, user: User): boolean => {
    const cacheKey = `${user.id}:${permission}`;
    
    if (permissionCache.has(cacheKey)) {
        return permissionCache.get(cacheKey)!;
    }
    
    const hasPermission = user.permissions.includes(permission) || 
                         user.roles.includes('Admin');
    
    permissionCache.set(cacheKey, hasPermission);
    return hasPermission;
};
```

## Validation Checklist

### Backend Migration Complete ✓
- [ ] RefreshToken entity created
- [ ] RefreshTokenService implemented
- [ ] AuthController updated with new endpoints
- [ ] Database migration executed
- [ ] Application properties updated
- [ ] Scheduled tasks configured

### Frontend Migration Complete ✓
- [ ] TokenService integrated
- [ ] API interceptors configured
- [ ] AuthContext implemented
- [ ] Session timeout warning added
- [ ] All API calls use new api instance
- [ ] Permission-based guards implemented
- [ ] Error handling updated

### Testing Complete ✓
- [ ] Login flow tested
- [ ] Token refresh tested
- [ ] Session timeout tested
- [ ] Permission checks tested
- [ ] API error handling tested
- [ ] 2FA flow tested

### Performance Optimized ✓
- [ ] Token refresh optimized
- [ ] Permission caching implemented
- [ ] Concurrent request handling tested
- [ ] Memory leaks checked

This migration guide should help developers successfully transition to the new authentication architecture while maintaining system stability and security.