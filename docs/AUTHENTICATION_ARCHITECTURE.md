# Authentication Architecture Documentation

## Overview
The Parabella CSRD FAQ Tool has implemented a comprehensive dual-token authentication system with advanced session management capabilities. This architecture provides enterprise-level security with enhanced user experience features.

## System Architecture Overview

### High-Level Architecture

```mermaid
graph TB
    User[User/Browser]
    FE[Frontend React App]
    API[Spring Boot API]
    DB[(PostgreSQL Database)]
    
    User --> FE
    FE --> API
    API --> DB
    
    subgraph "Frontend Layer"
        AuthContext[AuthContext Provider]
        TokenService[Token Service]
        ApiService[API Service + Interceptors]
        SessionWarning[Session Timeout Warning]
    end
    
    subgraph "Backend Layer"
        AuthController[Auth Controller]
        RefreshTokenService[Refresh Token Service]
        JwtUtils[JWT Utilities]
        SecurityConfig[Security Configuration]
    end
    
    subgraph "Data Layer"
        UserEntity[User Entity]
        RefreshTokenEntity[RefreshToken Entity]
        RolePermission[Role/Permission System]
    end
```

## Authentication Flow Architecture

### 1. Login Process Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Database
    
    User->>Frontend: Login (username, password, 2FA)
    Frontend->>Backend: POST /api/auth/signin
    Backend->>Database: Validate user credentials
    Backend->>Backend: Validate 2FA code
    Backend->>Backend: Generate JWT access token (15min)
    Backend->>Backend: Create refresh token (7 days)
    Backend->>Database: Store refresh token with metadata
    Backend->>Frontend: Return tokens + user data
    Frontend->>Frontend: Store tokens securely
    Frontend->>Frontend: Set authentication state
```

### 2. Token Refresh Flow

```mermaid
sequenceDiagram
    participant Frontend
    participant API_Interceptor
    participant Backend
    participant Database
    
    Frontend->>API_Interceptor: API Request
    API_Interceptor->>API_Interceptor: Check token expiry
    API_Interceptor->>Backend: POST /api/auth/refresh
    Backend->>Database: Validate refresh token
    Backend->>Backend: Generate new access token
    Backend->>Backend: Create new refresh token
    Backend->>Database: Store new refresh token
    Backend->>Database: Revoke old refresh token
    Backend->>API_Interceptor: Return new token pair
    API_Interceptor->>API_Interceptor: Update stored tokens
    API_Interceptor->>Backend: Retry original request
```

## Security Architecture

### 1. Token Management System

#### Backend Token Architecture
- **Access Tokens**: JWT, 15-minute expiry, contains user identity and permissions
- **Refresh Tokens**: UUID-based, 7-day expiry, stored in database with metadata
- **Token Rotation**: Each refresh generates new token pair, old tokens invalidated
- **Session Limits**: Configurable maximum active sessions per user (default: 5)

#### Frontend Token Storage
- **Secure Storage**: localStorage with encryption consideration for HTTPS
- **Session Fingerprinting**: Device and browser information tracking
- **Expiry Tracking**: Proactive refresh before token expiration
- **Automatic Cleanup**: Tokens cleared on logout or refresh failure

### 2. Session Management Architecture

```mermaid
graph LR
    A[User Activity] --> B{Token Expiring?}
    B -->|Yes < 2min| C[Show Warning Modal]
    B -->|No| D[Continue Normal Operation]
    C --> E{User Action}
    E -->|Extend Session| F[Refresh Token]
    E -->|Logout| G[Clear Session]
    E -->|No Action| H[Auto Logout]
    F --> D
    G --> I[Redirect to Login]
    H --> I
```

### 3. Concurrent Request Handling

The system implements a sophisticated queue mechanism for handling multiple requests during token refresh:

```typescript
// Request queuing during token refresh
let isRefreshing = false;
let failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
}> = [];
```

## Integration Patterns

### 1. Frontend-Backend Integration

#### Axios Interceptor Pattern
```typescript
// Request Interceptor - Proactive token refresh
api.interceptors.request.use(async (config) => {
    if (tokenService.isAccessTokenExpiring() && !isRefreshing) {
        await refreshAccessToken();
    }
    const token = tokenService.getAccessToken();
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
});

// Response Interceptor - Reactive token refresh
api.interceptors.response.use(
    (response) => response,
    async (error) => {
        if (error.response?.status === 401 && !originalRequest._retry) {
            // Queue management and token refresh logic
        }
        return Promise.reject(error);
    }
);
```

### 2. Authentication Context Pattern

The AuthContext provides centralized authentication state management:

```typescript
interface AuthContextType {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    login: (user: User) => void;
    logout: () => void;
    hasPermission: (permissionKey: string) => boolean;
}
```

## Database Schema Changes

### RefreshToken Entity
```sql
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL,
    device_info VARCHAR(500),
    ip_address VARCHAR(45),
    is_revoked BOOLEAN NOT NULL DEFAULT false
);
```

### Indexes for Performance
```sql
CREATE INDEX idx_refresh_token_user ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_token_expiry ON refresh_tokens(expiry_date);
CREATE INDEX idx_refresh_token_active ON refresh_tokens(is_revoked, expiry_date);
```

## Configuration Parameters

### Backend Configuration
```properties
# JWT Configuration
parabella_csrd_db.jwtSecret=your-secret-key
parabella_csrd_db.jwtExpirationMs=900000          # 15 minutes
parabella_csrd_db.jwtRefreshExpirationMs=604800000 # 7 days
parabella_csrd_db.maxActiveRefreshTokensPerUser=5

# Security Configuration
spring.security.cors.allowed-origins=https://storage.googleapis.com,http://localhost:5173
```

### Frontend Configuration
```typescript
// Token Service Configuration
const ACCESS_TOKEN_KEY = 'parabella_access_token';
const REFRESH_TOKEN_KEY = 'parabella_refresh_token';
const USER_DATA_KEY = 'parabella_user_data';
const TOKEN_EXPIRY_KEY = 'parabella_token_expiry';

// Session Warning Configuration
const WARNING_THRESHOLD = 2 * 60 * 1000; // 2 minutes
const CHECK_INTERVAL = 30 * 1000;        // 30 seconds
```

## Security Considerations

### 1. Token Security
- **HTTPS Only**: All token transmission over secure channels
- **Short-lived Access Tokens**: 15-minute expiry reduces exposure window
- **Token Rotation**: Refresh tokens are single-use and rotated on each refresh
- **Device Tracking**: Session metadata includes device and IP information

### 2. Session Security
- **Maximum Sessions**: Prevents unlimited concurrent sessions
- **Automatic Cleanup**: Scheduled cleanup of expired tokens
- **Forced Logout**: On security events or refresh failure
- **CORS Protection**: Restricted to allowed origins

### 3. Attack Mitigation
- **Token Replay**: Short expiry and rotation prevent replay attacks
- **Session Hijacking**: Device fingerprinting helps detect anomalies
- **XSS Protection**: Tokens stored securely with consideration for XSS risks
- **CSRF Protection**: JWT in Authorization header, not cookies

## Migration Guide

### Database Migration
1. **Create RefreshToken Table**: Run the schema creation scripts
2. **Update User Entity**: Ensure relationships are properly configured
3. **Data Migration**: Handle existing sessions (may require re-login)

### Frontend Migration
1. **Update API Calls**: Replace direct axios with the configured `api` instance
2. **Token Storage**: Migrate from old token storage to `tokenService`
3. **Add Components**: Include `SessionTimeoutWarning` in main layout
4. **Context Integration**: Ensure all auth-dependent components use `useAuth`

### Backend Migration
1. **Add Dependencies**: Ensure all required Spring Security dependencies
2. **Configuration**: Update application properties
3. **Service Integration**: Wire RefreshTokenService into AuthController
4. **Cleanup Jobs**: Configure scheduled tasks for token cleanup

## Performance Considerations

### 1. Token Refresh Optimization
- **Proactive Refresh**: Refresh tokens before they expire to avoid 401 errors
- **Request Queuing**: Queue concurrent requests during refresh to avoid duplicate refresh calls
- **Background Refresh**: Refresh happens in interceptors, transparent to user

### 2. Database Performance
- **Proper Indexing**: Indexes on frequently queried columns
- **Scheduled Cleanup**: Automatic removal of expired tokens
- **Connection Pooling**: Efficient database connection management

### 3. Frontend Performance
- **Lazy Loading**: Authentication components loaded on demand
- **State Management**: Efficient Redux/Context state updates
- **Memory Management**: Proper cleanup of timers and intervals

## Monitoring and Observability

### Key Metrics to Monitor
1. **Token Refresh Rate**: Track how often tokens are refreshed
2. **Failed Refresh Attempts**: Monitor authentication failures
3. **Session Duration**: Average session length per user
4. **Concurrent Sessions**: Number of active sessions per user
5. **Token Cleanup**: Expired tokens cleaned up per cycle

### Logging Strategy
```java
// Backend Logging
@Slf4j
public class RefreshTokenService {
    // Log token creation, refresh, and cleanup events
}

// Frontend Logging
console.log("Token refresh successful");
console.warn("Token expiring soon, refreshing...");
console.error("Token refresh failed:", error);
```

## Testing Strategy

### Unit Tests
- **Token Generation**: Verify JWT creation and validation
- **Token Refresh**: Test refresh flow and token rotation
- **Session Management**: Test session timeout and extension
- **Error Handling**: Test various failure scenarios

### Integration Tests
- **End-to-End Login**: Full authentication flow testing
- **Concurrent Requests**: Multiple simultaneous API calls during refresh
- **Session Expiry**: Automated testing of timeout scenarios
- **Security Testing**: Token manipulation and security breach attempts

### Load Testing
- **High Concurrency**: Multiple users refreshing tokens simultaneously
- **Token Storage**: Database performance under high token volume
- **Memory Usage**: Frontend memory consumption with long sessions

## Future Enhancements

### Planned Improvements
1. **Biometric Authentication**: Integration with WebAuthn for passwordless login
2. **Advanced Session Analytics**: More detailed session tracking and analysis
3. **Token Introspection**: OAuth 2.0 token introspection endpoint
4. **Distributed Sessions**: Redis-based session storage for horizontal scaling
5. **Progressive Security**: Adaptive authentication based on risk assessment

### Technical Debt
1. **Service Layer Decoupling**: Extract authentication logic from controllers
2. **Type Safety**: Improve TypeScript typing for authentication interfaces
3. **Error Handling**: Standardize error responses and user messaging
4. **Configuration Management**: Externalize more configuration parameters