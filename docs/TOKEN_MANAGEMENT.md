# Enterprise Token Management Implementation

## Overview
This document describes the comprehensive token management system implemented for secure session handling between the frontend and backend.

## Architecture

### Backend Components

#### 1. **RefreshToken Entity**
- Stores refresh tokens with metadata (user, device info, IP address)
- Tracks token expiry and revocation status
- Supports multiple active sessions per user (configurable limit)

#### 2. **Token Endpoints**
- `POST /api/auth/signin` - Returns both access token (15min) and refresh token (7 days)
- `POST /api/auth/refresh` - Exchanges refresh token for new token pair
- `POST /api/auth/logout` - Revokes all user refresh tokens
- `GET /api/auth/me` - Validates current session

#### 3. **Security Features**
- Token rotation on refresh (old tokens invalidated)
- Automatic cleanup of expired tokens (scheduled daily)
- Device tracking for session management
- Configurable max sessions per user

### Frontend Components

#### 1. **Token Storage Service**
- Encrypted token storage in localStorage
- Session fingerprinting for additional security
- Automatic token expiry tracking
- Secure token retrieval with validation

#### 2. **Axios Interceptors**
- **Request Interceptor**: 
  - Attaches access token to all requests
  - Proactive token refresh when expiring soon
- **Response Interceptor**:
  - Automatic retry with token refresh on 401
  - Queue management for concurrent requests
  - Graceful logout on refresh failure

#### 3. **Session Management**
- Visual timeout warnings (2 minutes before expiry)
- One-click session extension
- Countdown timer display
- Automatic logout on expiry

## Configuration

### Backend Properties
```properties
# Access token expiry (15 minutes)
parabella_csrd_db.jwtExpirationMs=900000

# Refresh token expiry (7 days)
parabella_csrd_db.jwtRefreshExpirationMs=604800000

# Max active sessions per user
parabella_csrd_db.maxActiveRefreshTokensPerUser=5
```

### Token Flow

1. **Login**: User provides credentials + 2FA → Receives access & refresh tokens
2. **API Calls**: Access token sent in Authorization header
3. **Token Expiry**: 
   - Proactive refresh before expiry
   - Automatic refresh on 401 response
4. **Session Extension**: User can manually extend via warning modal
5. **Logout**: All refresh tokens revoked on backend

## Security Considerations

1. **Token Storage**: Tokens encrypted in localStorage with session fingerprint
2. **Token Rotation**: New tokens issued on each refresh
3. **Session Limits**: Prevents unlimited active sessions
4. **HTTPS Required**: All token transmission over secure channels
5. **CORS Configuration**: Restricted to allowed origins

## Migration Notes

### Database Changes
Run the following migration to create the refresh_tokens table:

```sql
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL,
    device_info VARCHAR(500),
    ip_address VARCHAR(45),
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT fk_refresh_token_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_refresh_token_user ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_token_expiry ON refresh_tokens(expiry_date);
```

### Frontend Updates
- Update all API calls to use the new `api` instance from `services/api.ts`
- Replace direct localStorage access with `tokenService` methods
- Add `<SessionTimeoutWarning />` component to main layout

## Testing

1. **Login Flow**: Verify access and refresh tokens received
2. **Token Refresh**: Wait 15 minutes, verify automatic refresh
3. **Session Warning**: Wait 13 minutes, verify warning appears
4. **Manual Extension**: Click extend in warning modal
5. **Concurrent Requests**: Make multiple API calls during refresh
6. **Logout**: Verify all tokens cleared and backend revocation

## Monitoring

- Track refresh token usage patterns
- Monitor failed refresh attempts
- Alert on unusual session counts per user
- Log token rotation events for audit