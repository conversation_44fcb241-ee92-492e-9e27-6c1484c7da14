# Developer Guide: SQL Logging Configuration

## Quick Reference

### Enable SQL Logging for Local Development

**Method 1: Use Local Profile (Recommended)**
```bash
# Run with local profile (SQL logging already enabled in application-local.properties)
./gradlew bootRun --args='--spring.profiles.active=local'

# Or when running JAR
java -jar build/libs/parabella-csrd-db.jar --spring.profiles.active=local
```

**Method 2: Environment Variables**
```bash
# Set environment variables
export SPRING_JPA_SHOW_SQL=true
export HIBERNATE_FORMAT_SQL=true
export HIBERNATE_SQL_LOG_LEVEL=DEBUG

# Run application
./gradlew bootRun
```

**Method 3: IDE Configuration**
In your IDE, set these environment variables or VM options:
```
-DSPRING_JPA_SHOW_SQL=true
-DHIBERNATE_FORMAT_SQL=true
-DHIBERNATE_SQL_LOG_LEVEL=DEBUG
```

### View Parameter Values (Use with Caution)

To see actual parameter values in SQL queries (contains sensitive data):

**In application-local.properties, uncomment:**
```properties
logging.level.org.hibernate.type.descriptor.sql=TRACE
```

**Or via environment variable:**
```bash
export HIBERNATE_PARAM_LOG_LEVEL=TRACE
```

### Different Logging Levels

| Level | What You See |
|-------|-------------|
| `WARN` | Errors and warnings only |
| `INFO` | Basic SQL execution info |
| `DEBUG` | Full SQL queries (no parameters) |
| `TRACE` | SQL queries + parameter values |

### Security Reminders

- ✅ **DO** use SQL logging in local development
- ✅ **DO** use `DEBUG` level for query structure analysis
- ⚠️ **CAUTION** when using `TRACE` level (shows parameter values)
- ❌ **NEVER** enable SQL logging in production
- ❌ **NEVER** commit enabled SQL logging to main branch

### Current Configuration Status

| Environment | SQL Logging | Parameter Values | Override Method |
|-------------|-------------|------------------|-----------------|
| Production | ❌ Disabled | ❌ Disabled | Environment variables only |
| Staging | ❌ Disabled | ❌ Disabled | Environment variables only |
| Local | ✅ Enabled | ❌ Disabled | application-local.properties |
| Test | ❌ Disabled | ✅ Enabled (for test debugging) | application-test.properties |

### Troubleshooting

**Problem**: Not seeing SQL queries in development
- **Solution**: Make sure you're using `--spring.profiles.active=local`

**Problem**: Seeing too much SQL logging
- **Solution**: Adjust log levels: `HIBERNATE_SQL_LOG_LEVEL=INFO`

**Problem**: Need to debug a specific query
- **Solution**: Temporarily enable TRACE level for parameter values

**Problem**: Accidentally enabled SQL logging in production
- **Solution**: Remove environment variables and restart application immediately