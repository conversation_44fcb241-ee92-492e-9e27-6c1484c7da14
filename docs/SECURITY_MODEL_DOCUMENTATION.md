# Security Model Documentation

## Overview
This document describes the comprehensive security model implemented in the Parabella CSRD FAQ Tool, covering authentication, authorization, session management, and data protection mechanisms.

## Security Architecture

### 1. Multi-Layer Security Model

```mermaid
graph TB
    subgraph "Application Security Layers"
        L1[Network Security Layer]
        L2[Application Gateway Layer]  
        L3[Authentication Layer]
        L4[Authorization Layer]
        L5[Session Management Layer]
        L6[Data Protection Layer]
        L7[Audit & Monitoring Layer]
    end
    
    L1 --> L2
    L2 --> L3
    L3 --> L4
    L4 --> L5
    L5 --> L6
    L6 --> L7
    
    subgraph "Security Controls"
        HTTPS[HTTPS/TLS]
        CORS[CORS Policy]
        JWT[JWT Tokens]
        RBAC[Role-Based Access]
        SessionMgmt[Session Management]
        Encryption[Data Encryption]
        Logging[Security Logging]
    end
    
    L1 -.-> HTTPS
    L2 -.-> CORS
    L3 -.-> JWT
    L4 -.-> RBAC
    L5 -.-> SessionMgmt
    L6 -.-> Encryption
    L7 -.-> Logging
```

## Authentication Security

### 1. Multi-Factor Authentication (MFA)

#### TOTP-Based 2FA Implementation
```java
// Backend 2FA validation
public class TotpUtils {
    private static final int TIME_STEP = 30; // 30 seconds
    private static final int WINDOW = 1;     // ±30 seconds tolerance
    
    public static boolean validateCode(String secret, int userCode) {
        long timeSlot = System.currentTimeMillis() / 1000 / TIME_STEP;
        
        // Check current time slot and adjacent slots for clock drift
        for (int i = -WINDOW; i <= WINDOW; i++) {
            int expectedCode = generateTOTP(secret, timeSlot + i);
            if (expectedCode == userCode) {
                return true;
            }
        }
        return false;
    }
    
    private static int generateTOTP(String secret, long timeSlot) {
        // HMAC-SHA1 based TOTP implementation
        byte[] secretBytes = Base32.decode(secret);
        byte[] timeBytes = ByteBuffer.allocate(8).putLong(timeSlot).array();
        
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(new SecretKeySpec(secretBytes, "HmacSHA1"));
        byte[] hash = mac.doFinal(timeBytes);
        
        int offset = hash[hash.length - 1] & 0xF;
        int binary = ((hash[offset] & 0x7F) << 24) |
                    ((hash[offset + 1] & 0xFF) << 16) |
                    ((hash[offset + 2] & 0xFF) << 8) |
                    (hash[offset + 3] & 0xFF);
        
        return binary % 1000000; // 6-digit code
    }
}
```

#### Security Features
- **Secret Generation**: Cryptographically secure random 160-bit secrets
- **Time-based Codes**: 30-second validity window with drift tolerance
- **Mandatory Enforcement**: All users must complete 2FA setup
- **Recovery Options**: Admin-assisted recovery for lost devices

### 2. JWT Token Security

#### Token Structure and Claims
```java
// JWT token generation with security claims
public String generateJwtToken(Authentication authentication) {
    UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
    
    return Jwts.builder()
            .setSubject(userPrincipal.getUsername())
            .claim("id", userPrincipal.getId())
            .claim("email", userPrincipal.getEmail())
            .claim("roles", userPrincipal.getRoles())
            .claim("permissions", userPrincipal.getPermissions())
            .claim("iat", System.currentTimeMillis() / 1000) // Issued at
            .claim("jti", UUID.randomUUID().toString())      // JWT ID for revocation
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + jwtExpirationMs))
            .signWith(getSigningKey(), SignatureAlgorithm.HS256)
            .compact();
}
```

#### Token Security Measures
- **Short Lifespan**: 15-minute access token expiry
- **Strong Signing**: HMAC-SHA256 with 256-bit secret
- **Unique Identifiers**: JWT ID (jti) for token tracking and revocation
- **Audience Validation**: Tokens bound to specific application
- **Issuer Validation**: Verified token source

### 3. Refresh Token Security

#### Secure Token Storage and Rotation
```java
@Entity
@Table(name = "refresh_tokens")
public class RefreshToken {
    @Column(nullable = false, unique = true)
    private String token; // UUID v4 - 128-bit entropy
    
    @Column(nullable = false)
    private Instant expiryDate; // 7-day maximum
    
    @Column(name = "device_info")
    private String deviceInfo; // Device fingerprinting
    
    @Column(name = "ip_address") 
    private String ipAddress; // IP binding for anomaly detection
    
    @Column(name = "is_revoked", nullable = false)
    private boolean isRevoked = false; // Explicit revocation
    
    // Security methods
    public boolean isExpired() {
        return Instant.now().isAfter(expiryDate);
    }
    
    public boolean isSuspicious(String currentIp, String currentDevice) {
        // Implement device/IP change detection
        return !Objects.equals(this.ipAddress, currentIp) ||
               !Objects.equals(this.deviceInfo, currentDevice);
    }
}
```

#### Rotation Security
- **Single Use**: Each refresh invalidates the previous token
- **Device Binding**: Tokens tied to device fingerprints
- **IP Validation**: Anomaly detection for IP changes
- **Concurrent Session Limits**: Maximum 5 active sessions per user
- **Automatic Cleanup**: Daily cleanup of expired tokens

## Authorization Security

### 1. Role-Based Access Control (RBAC)

#### Permission Matrix
```java
// Fine-grained permission system
public enum Permission {
    // User Management
    USER_READ("user.read"),
    USER_CREATE("user.create"), 
    USER_EDIT("user.edit"),
    USER_DELETE("user.delete"),
    USER_ASSIGN("user.assign"),
    
    // Role Management
    ROLE_READ("role.read"),
    ROLE_MANAGE("role.manage"),
    
    // Project Management  
    PROJECT_READ("project.read"),
    PROJECT_CREATE("project.create"),
    PROJECT_EDIT("project.edit"),
    PROJECT_DELETE("project.delete"),
    PROJECT_SHARE("project.share"),
    
    // Module Access
    CSRD_ACCESS("csrd.access"),
    DMA_ACCESS("dma.access"),
    PCF_ACCESS("pcf.access"),
    
    // System Administration
    SYSTEM_CONFIG("system.config"),
    AUDIT_READ("audit.read");
    
    private final String key;
}
```

#### Method-Level Security
```java
@RestController
@PreAuthorize("hasRole('USER')")
public class ProjectController {
    
    @GetMapping("/projects")
    @PreAuthorize("hasPermission('project.read')")
    public ResponseEntity<List<Project>> getProjects(Authentication auth) {
        UserDetailsImpl user = (UserDetailsImpl) auth.getPrincipal();
        return ResponseEntity.ok(projectService.getProjectsByUser(user.getId()));
    }
    
    @PostMapping("/projects")
    @PreAuthorize("hasPermission('project.create')")
    public ResponseEntity<Project> createProject(@RequestBody ProjectDto dto, Authentication auth) {
        // Automatic user context injection
        UserDetailsImpl user = (UserDetailsImpl) auth.getPrincipal();
        return ResponseEntity.ok(projectService.createProject(dto, user.getId()));
    }
    
    @DeleteMapping("/projects/{id}")
    @PreAuthorize("hasPermission('project.delete') and @projectService.isOwner(#id, authentication.principal.id)")
    public ResponseEntity<Void> deleteProject(@PathVariable Long id) {
        projectService.deleteProject(id);
        return ResponseEntity.noContent().build();
    }
}
```

### 2. Resource-Level Security

#### Data Ownership Validation
```java
@Service
public class ProjectSecurityService {
    
    public boolean isOwner(Long projectId, Long userId) {
        return projectRepository.findById(projectId)
                .map(project -> project.getOwnerId().equals(userId))
                .orElse(false);
    }
    
    public boolean canAccess(Long projectId, Long userId) {
        return projectRepository.findById(projectId)
                .map(project -> project.getOwnerId().equals(userId) || 
                               project.getCollaborators().contains(userId))
                .orElse(false);
    }
    
    @PreAuthorize("hasPermission('project.share')")
    public boolean canShare(Long projectId, Long userId) {
        return isOwner(projectId, userId);
    }
}
```

## Session Security

### 1. Session Management

#### Session Fingerprinting
```typescript
// Frontend session fingerprinting
class SessionFingerprint {
    static generate(): string {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx!.textBaseline = 'top';
        ctx!.font = '14px Arial';
        ctx!.fillText('Session fingerprint', 2, 2);
        
        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL()
        ].join('|');
        
        return btoa(fingerprint).substring(0, 32);
    }
    
    static validate(stored: string): boolean {
        const current = this.generate();
        return stored === current;
    }
}
```

#### Session Timeout Management
```typescript
// Proactive session management
class SessionManager {
    private warningThreshold = 2 * 60 * 1000; // 2 minutes
    private checkInterval = 30 * 1000;        // 30 seconds
    
    startSessionMonitoring() {
        setInterval(() => {
            const remaining = tokenService.getRemainingSessionTime();
            
            if (remaining > 0 && remaining <= this.warningThreshold) {
                this.showSessionWarning(remaining);
            } else if (remaining <= 0) {
                this.forceLogout();
            }
        }, this.checkInterval);
    }
    
    private showSessionWarning(remaining: number) {
        // Display modal with extend/logout options
        const modal = new SessionTimeoutModal({
            remainingTime: remaining,
            onExtend: () => this.extendSession(),
            onLogout: () => this.forceLogout()
        });
        modal.show();
    }
}
```

### 2. Concurrent Session Handling

#### Session Limit Enforcement
```java
@Service
public class SessionLimitService {
    
    @Value("${parabella_csrd_db.maxActiveRefreshTokensPerUser:5}")
    private int maxActiveRefreshTokensPerUser;
    
    public void enforceSessionLimit(User user) {
        long activeCount = refreshTokenRepository.countActiveTokensByUser(user, Instant.now());
        
        if (activeCount >= maxActiveRefreshTokensPerUser) {
            // Clean up expired tokens first
            refreshTokenRepository.deleteExpiredTokens(Instant.now());
            
            // If still over limit, revoke oldest sessions
            activeCount = refreshTokenRepository.countActiveTokensByUser(user, Instant.now());
            if (activeCount >= maxActiveRefreshTokensPerUser) {
                List<RefreshToken> oldestTokens = refreshTokenRepository
                    .findOldestActiveTokensByUser(user, activeCount - maxActiveRefreshTokensPerUser + 1);
                
                oldestTokens.forEach(token -> {
                    token.setRevoked(true);
                    refreshTokenRepository.save(token);
                });
            }
        }
    }
}
```

## Data Protection Security

### 1. Input Validation and Sanitization

#### Server-Side Validation
```java
// Comprehensive input validation
@RestController
@Validated
public class UserController {
    
    @PostMapping("/users")
    public ResponseEntity<User> createUser(
            @Valid @RequestBody CreateUserRequest request) {
        
        // Additional security validation
        if (!SecurityUtils.isValidEmail(request.getEmail())) {
            throw new ValidationException("Invalid email format");
        }
        
        if (!SecurityUtils.isStrongPassword(request.getPassword())) {
            throw new ValidationException("Password does not meet security requirements");
        }
        
        return ResponseEntity.ok(userService.createUser(request));
    }
}

// Security validation utilities
public class SecurityUtils {
    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");
    
    private static final Pattern STRONG_PASSWORD_PATTERN = 
        Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$");
    
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }
    
    public static boolean isStrongPassword(String password) {
        return password != null && STRONG_PASSWORD_PATTERN.matcher(password).matches();
    }
}
```

#### Client-Side Sanitization
```typescript
// Input sanitization utilities
class InputSanitizer {
    static sanitizeString(input: string): string {
        return input
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .replace(/javascript:/gi, '') // Remove javascript: protocols
            .replace(/on\w+=/gi, '') // Remove event handlers
            .trim();
    }
    
    static sanitizeEmail(email: string): string {
        return email.toLowerCase().trim().replace(/[^\w@.-]/g, '');
    }
    
    static sanitizeNumeric(input: string): string {
        return input.replace(/[^\d.-]/g, '');
    }
}
```

### 2. SQL Injection Prevention

#### Parameterized Queries
```java
// Safe repository patterns
@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    
    // Safe: Using method queries
    List<Project> findByOwnerIdAndNameContaining(Long ownerId, String name);
    
    // Safe: Parameterized native queries
    @Query(value = "SELECT * FROM projects WHERE owner_id = ?1 AND status = ?2", nativeQuery = true)
    List<Project> findByOwnerAndStatus(Long ownerId, String status);
    
    // Safe: JPQL with parameters
    @Query("SELECT p FROM Project p WHERE p.owner.id = :ownerId AND p.createdDate >= :fromDate")
    List<Project> findRecentProjectsByOwner(@Param("ownerId") Long ownerId, @Param("fromDate") LocalDateTime fromDate);
}
```

### 3. XSS Prevention

#### Content Security Policy
```java
// CSP configuration
@Configuration
public class SecurityHeadersConfig {
    
    @Bean
    public FilterRegistrationBean<CspHeaderFilter> cspFilter() {
        FilterRegistrationBean<CspHeaderFilter> filter = new FilterRegistrationBean<>();
        filter.setFilter(new CspHeaderFilter());
        filter.addUrlPatterns("/*");
        return filter;
    }
}

class CspHeaderFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        httpResponse.setHeader("Content-Security-Policy", 
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' https://trusted-scripts.com; " +
            "style-src 'self' 'unsafe-inline' https://trusted-styles.com; " +
            "img-src 'self' data: https:; " +
            "connect-src 'self' https://api.parabella.com"
        );
        
        chain.doFilter(request, response);
    }
}
```

## Network Security

### 1. HTTPS/TLS Configuration

#### TLS Configuration
```yaml
# Spring Boot TLS configuration
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: parabella
  port: 8443

# Redirect HTTP to HTTPS
security:
  require-ssl: true
```

### 2. CORS Configuration

#### Secure CORS Policy
```java
@Configuration
@EnableWebSecurity
public class CorsConfig {
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Specific allowed origins (no wildcards in production)
        configuration.setAllowedOrigins(Arrays.asList(
            "https://app.parabella.com",
            "https://staging.parabella.com"
        ));
        
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS"
        ));
        
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "X-Requested-With"
        ));
        
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}
```

## Audit and Monitoring

### 1. Security Event Logging

#### Comprehensive Audit Trail
```java
@Entity
@Audited
public class SecurityEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String eventType; // LOGIN, LOGOUT, ACCESS_DENIED, etc.
    
    @Column(nullable = false)
    private String username;
    
    @Column(nullable = false)
    private String ipAddress;
    
    private String userAgent;
    private String resourceAccessed;
    private String outcome; // SUCCESS, FAILURE, BLOCKED
    
    @Column(nullable = false)
    private Instant timestamp;
    
    private String additionalInfo; // JSON format for extra details
}

@Service
public class SecurityAuditService {
    
    public void logSecurityEvent(SecurityEventType type, String username, 
                                 HttpServletRequest request, String outcome) {
        SecurityEvent event = new SecurityEvent();
        event.setEventType(type.name());
        event.setUsername(username);
        event.setIpAddress(getClientIpAddress(request));
        event.setUserAgent(request.getHeader("User-Agent"));
        event.setOutcome(outcome);
        event.setTimestamp(Instant.now());
        
        securityEventRepository.save(event);
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
}
```

### 2. Anomaly Detection

#### Suspicious Activity Detection
```java
@Service
public class AnomalyDetectionService {
    
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void detectAnomalies() {
        detectMultipleFailedLogins();
        detectUnusualAccessPatterns();
        detectTokenAnomalies();
    }
    
    private void detectMultipleFailedLogins() {
        Instant fiveMinutesAgo = Instant.now().minus(Duration.ofMinutes(5));
        
        List<String> suspiciousIps = securityEventRepository
            .findFailedLoginsByTimeframe(fiveMinutesAgo, Instant.now())
            .stream()
            .collect(Collectors.groupingBy(SecurityEvent::getIpAddress, Collectors.counting()))
            .entrySet()
            .stream()
            .filter(entry -> entry.getValue() >= 5) // 5 failed attempts
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
        
        suspiciousIps.forEach(this::blockSuspiciousIp);
    }
    
    private void detectTokenAnomalies() {
        // Detect tokens accessed from multiple IPs
        List<RefreshToken> suspiciousTokens = refreshTokenRepository
            .findTokensWithMultipleIpAccess();
        
        suspiciousTokens.forEach(token -> {
            token.setRevoked(true);
            refreshTokenRepository.save(token);
            notifySecurityTeam("Suspicious token activity detected", token);
        });
    }
}
```

## Security Testing

### 1. Automated Security Testing

#### Security Test Cases
```java
@SpringBootTest
@AutoConfigureTestDatabase
class SecurityTests {
    
    @Test
    void shouldRejectWeakPasswords() {
        CreateUserRequest request = new CreateUserRequest();
        request.setPassword("123456"); // Weak password
        
        assertThrows(ValidationException.class, () -> {
            userController.createUser(request);
        });
    }
    
    @Test
    void shouldEnforceSessionLimits() {
        User user = createTestUser();
        
        // Create maximum allowed sessions
        for (int i = 0; i < 5; i++) {
            refreshTokenService.createRefreshToken(user.getId(), "device" + i, "127.0.0.1");
        }
        
        // Attempt to create one more - should revoke oldest
        RefreshToken newToken = refreshTokenService.createRefreshToken(
            user.getId(), "device6", "127.0.0.1"
        );
        
        assertNotNull(newToken);
        assertEquals(5, refreshTokenService.getActiveTokenCount(user.getId()));
    }
    
    @Test
    void shouldDetectSqlInjection() {
        String maliciousInput = "'; DROP TABLE users; --";
        
        assertThrows(DataAccessException.class, () -> {
            projectService.searchProjects(maliciousInput);
        });
    }
}
```

### 2. Penetration Testing Guidelines

#### Security Testing Checklist
- [ ] **Authentication Bypass**: Attempt to access protected resources without tokens
- [ ] **Token Manipulation**: Try to modify JWT claims and signatures  
- [ ] **Session Fixation**: Attempt to reuse tokens across different users
- [ ] **CSRF Attacks**: Test cross-site request forgery protection
- [ ] **XSS Injection**: Test input sanitization in all forms
- [ ] **SQL Injection**: Test parameterized query protection
- [ ] **Privilege Escalation**: Attempt to access higher-privilege functions
- [ ] **Brute Force**: Test rate limiting on login endpoints
- [ ] **Session Hijacking**: Test session token security
- [ ] **Data Exposure**: Verify sensitive data is not leaked in responses

## Compliance and Standards

### 1. GDPR Compliance

#### Data Protection Measures
- **Right to be Forgotten**: Automated user data deletion
- **Data Portability**: Export user data in structured format
- **Consent Management**: Explicit consent tracking
- **Data Minimization**: Only collect necessary user data
- **Purpose Limitation**: Use data only for stated purposes

### 2. Security Standards Adherence

#### Standards Compliance
- **OWASP Top 10**: Protection against common web vulnerabilities
- **NIST Cybersecurity Framework**: Comprehensive security controls
- **ISO 27001**: Information security management
- **SOC 2 Type II**: Service organization controls

This comprehensive security model ensures that the Parabella CSRD FAQ Tool maintains enterprise-level security while providing a seamless user experience.