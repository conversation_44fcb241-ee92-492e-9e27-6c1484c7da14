# Parabella CSRD FAQ Tool - Architecture Documentation

## Overview
This directory contains comprehensive documentation for the Parabella CSRD FAQ Tool's architecture, focusing on the enhanced authentication and session management system implemented in 2024-2025.

## 📁 Documentation Index

### Core Architecture Documents

#### 🔐 Authentication & Security
- **[Authentication Architecture](./AUTHENTICATION_ARCHITECTURE.md)** - Complete overview of the dual-token authentication system, session management, and security features
- **[Security Model Documentation](./SECURITY_MODEL_DOCUMENTATION.md)** - Comprehensive security model covering all layers from network to data protection
- **[Authentication Migration Guide](./AUTHENTICATION_MIGRATION_GUIDE.md)** - Step-by-step guide for developers to migrate to the new authentication system

#### 🏗️ System Architecture
- **[System Architecture Diagrams](./SYSTEM_ARCHITECTURE_DIAGRAM.md)** - Visual diagrams showing system components, data flows, and integration patterns
- **[System Integration Architecture](./SYSTEM_INTEGRATION_ARCHITECTURE.md)** - How authentication integrates with existing modules and business logic

#### 📋 Implementation Documentation
- **[Token Management](./TOKEN_MANAGEMENT.md)** - Enterprise token management implementation details
- **[Invitation Link Fix](./INVITATION_LINK_FIX.md)** - Invitation conflict resolution system documentation

## 🚀 Quick Start for Developers

### Understanding the New Architecture
1. **Start Here**: Read [Authentication Architecture](./AUTHENTICATION_ARCHITECTURE.md) for system overview
2. **Security Context**: Review [Security Model Documentation](./SECURITY_MODEL_DOCUMENTATION.md) for security implementation
3. **Integration Patterns**: Study [System Integration Architecture](./SYSTEM_INTEGRATION_ARCHITECTURE.md) for module integration
4. **Migration Path**: Follow [Authentication Migration Guide](./AUTHENTICATION_MIGRATION_GUIDE.md) for code updates

### Key Architectural Changes

#### What's New in 2024-2025
- ✅ **Dual-Token System**: 15-minute access tokens + 7-day refresh tokens
- ✅ **Automatic Token Refresh**: Transparent token renewal via Axios interceptors
- ✅ **Session Management**: User-friendly timeout warnings and session extension
- ✅ **Enhanced Security**: Device tracking, session limits, and audit trails
- ✅ **Permission System**: Fine-grained role-based access control
- ✅ **2FA Integration**: Mandatory TOTP-based two-factor authentication

#### Migration Impact
- **Frontend**: New authentication context, token service, and API interceptors
- **Backend**: Refresh token service, enhanced JWT utilities, and session management
- **Database**: New refresh_tokens table and audit trail enhancements
- **Security**: Multi-layer security model with comprehensive monitoring

## 🔄 System Integration Overview

### Frontend Architecture
```
React SPA (18.3.1 + TypeScript)
├── Authentication Layer
│   ├── AuthContext Provider
│   ├── Token Service
│   └── Session Management
├── Business Modules
│   ├── CSRD Module
│   ├── DMA Module
│   └── PCF Module
└── Infrastructure
    ├── API Service + Interceptors
    ├── Permission Guards
    └── Error Handling
```

### Backend Architecture
```
Spring Boot API (3.3.0 + Java 21)
├── Security Layer
│   ├── JWT Authentication
│   ├── Refresh Token Service
│   └── 2FA/TOTP Integration
├── Business Services
│   ├── User Management
│   ├── Project Services
│   └── Audit Services
└── Data Layer
    ├── PostgreSQL Database
    ├── JPA Repositories
    └── Audit Trail (Envers)
```

## 🛡️ Security Highlights

### Multi-Layer Security Model
1. **Network Security**: HTTPS/TLS, CORS, CSP
2. **Authentication**: JWT + 2FA, Token rotation
3. **Authorization**: RBAC, Fine-grained permissions
4. **Session Management**: Limits, Device tracking, Timeouts
5. **Data Protection**: Input validation, SQL injection prevention
6. **Monitoring**: Security logging, Anomaly detection

### Key Security Features
- **Zero-Trust Architecture**: Every request validated
- **Token Security**: Short-lived access tokens with secure refresh
- **Session Security**: Device fingerprinting and concurrent session limits
- **Audit Trail**: Comprehensive logging of all security events
- **Input Validation**: Multi-layer validation and sanitization

## 📊 Performance & Scalability

### Optimization Features
- **Proactive Token Refresh**: Prevents API disruptions
- **Request Queuing**: Handles concurrent requests during token refresh
- **Permission Caching**: Optimized authorization checks
- **Database Indexing**: Efficient query performance
- **Connection Pooling**: Scalable database connections

### Monitoring & Observability
- **Token Metrics**: Refresh rates, failure rates
- **Session Analytics**: Duration, concurrent sessions
- **Security Events**: Login attempts, access denials
- **Performance Metrics**: Response times, error rates

## 🔧 Development Guidelines

### Code Standards
- **Frontend**: TypeScript, React functional components, Context API
- **Backend**: Java 21, Spring Boot, JPA, Method-level security
- **Database**: PostgreSQL, Hibernate, Proper indexing
- **Testing**: Unit tests, Integration tests, Security tests

### Best Practices
- **Authentication**: Always use AuthContext for user state
- **API Calls**: Use configured api instance with interceptors
- **Permissions**: Implement permission-based access control
- **Error Handling**: Use centralized error handling patterns
- **Security**: Follow principle of least privilege

## 📈 Deployment & Operations

### Environment Configuration
```yaml
Production:
  - HTTPS Only
  - Restricted CORS
  - Token rotation enabled
  - Session monitoring active
  - Audit logging comprehensive

Staging:
  - Production-like configuration
  - Extended logging
  - Performance monitoring

Development:
  - Local PostgreSQL
  - Hot reload enabled
  - Debug logging
```

### Monitoring Setup
- **Application Metrics**: Prometheus + Grafana
- **Security Events**: ELK Stack
- **Performance**: APM tools
- **Alerts**: Failed logins, Token anomalies, System errors

## 🎯 Future Roadmap

### Planned Enhancements
- **WebAuthn Integration**: Passwordless authentication
- **Advanced Analytics**: User behavior analysis
- **Distributed Sessions**: Redis-based session storage
- **Progressive Security**: Risk-based authentication
- **API Rate Limiting**: Advanced throttling mechanisms

### Technical Debt
- **Service Layer Refactoring**: Extract business logic from controllers
- **Type Safety Improvements**: Enhanced TypeScript interfaces
- **Configuration Externalization**: Environment-based configuration
- **Test Coverage**: Increase automated test coverage

## 📞 Support & Contact

### For Developers
- **Architecture Questions**: Refer to system integration documentation
- **Security Concerns**: Review security model documentation
- **Implementation Help**: Follow migration guide step-by-step
- **Performance Issues**: Check monitoring and optimization guides

### Documentation Maintenance
This documentation is maintained alongside the codebase. When making architectural changes:
1. Update relevant documentation files
2. Update system diagrams if component relationships change
3. Update migration guides for breaking changes
4. Update security documentation for security-related changes

---

**Last Updated**: January 2025  
**Version**: 2.0 (Authentication System Overhaul)  
**Maintainer**: Parabella Development Team