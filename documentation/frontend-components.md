# Frontend Component Documentation

This document provides comprehensive documentation for the React components in the Parabella CSRD FAQ Tool frontend application.

## Architecture Overview

The frontend is organized using a feature-based architecture with three main business modules:

```
src/ui_components/
├── components/
│   ├── authentication/          # Authentication flows
│   ├── DmaModule/              # Double Materiality Assessment (Mithril)
│   ├── CSRD_Module/            # CSRD Compliance Reporting (Elessar)
│   ├── PCFModule/              # Product Carbon Footprint (Vilya)
│   ├── analytics/              # Cross-module analytics
│   ├── chat_bot/               # AI chatbot functionality
│   ├── general_pages/          # Settings, profile, contact
│   └── dashboard/              # Main dashboards
├── layout/
│   └── layoutcomponent/        # Shared layout components
└── templateLogic/              # Redux store, routing, utilities
```

## Core Layout Components

### Header Component (`layout/layoutcomponent/header.tsx`)

**Purpose**: Main application header with navigation and user controls

**Props Interface**:
```typescript
interface HeaderProps {
  // Inherits from theme context - no direct props
}
```

**Key Features**:
- **Authentication Status**: Shows login/logout controls based on auth state
- **Theme Switching**: Light/dark mode toggle
- **User Profile**: Dropdown with profile options
- **Notifications**: Bell icon with notification count
- **Responsive Design**: Collapses on mobile devices

**Usage Example**:
```tsx
import Header from '../layout/layoutcomponent/header';

function App() {
  return (
    <div className="app">
      <Header />
      {/* Main content */}
    </div>
  );
}
```

**State Dependencies**:
- `AuthContext`: User authentication state
- `Redux Theme Store`: Theme preferences
- `NotificationContext`: Notification count

### Sidebar Component (`layout/layoutcomponent/sidebar.tsx`)

**Purpose**: Main navigation sidebar with module access

**Props Interface**:
```typescript
interface SidebarProps {
  // Theme-driven component, no direct props
}
```

**Key Features**:
- **Module Navigation**: Links to DMA, CSRD, PCF modules
- **Collapsible Menu**: Expandable sections with sub-items
- **Role-based Visibility**: Menu items filtered by user permissions
- **Active State**: Highlights current page/module

**Navigation Structure**:
```typescript
const navigationStructure = {
  dashboard: "/dashboard",
  modules: {
    dma: "/mithril/*",
    csrd: "/csrd/*", 
    pcf: "/vilya/*"
  },
  analytics: "/analytics",
  settings: "/settings"
};
```

## Authentication Components

### Login Component (`authentication/Login.tsx`)

**Purpose**: User authentication with 2FA support

**Props Interface**:
```typescript
interface LoginProps {
  onSuccess?: (user: User) => void;
  redirectTo?: string;
}
```

**Key Features**:
- **Form Validation**: Email/username and password validation
- **2FA Integration**: TOTP verification after initial login
- **Remember Me**: Optional session persistence
- **Error Handling**: User-friendly error messages

**API Integration**:
```typescript
const loginUser = async (credentials: LoginRequest) => {
  const response = await axios.post('/api/auth/signin', credentials);
  if (response.data.requires2FA) {
    // Show 2FA verification component
  }
  return response.data;
};
```

**Usage Example**:
```tsx
<Login 
  onSuccess={(user) => navigate('/dashboard')}
  redirectTo="/dashboard"
/>
```

### Register Component (`authentication/Register.tsx`)

**Purpose**: User registration with 2FA setup

**Props Interface**:
```typescript
interface RegisterProps {
  onSuccess?: () => void;
  inviteToken?: string;
}
```

**Key Features**:
- **Multi-step Flow**: Registration → Email verification → 2FA setup
- **QR Code Generation**: TOTP secret as QR code
- **Validation**: Real-time form validation
- **Invite System**: Support for invitation-based registration

**Registration Flow**:
```mermaid
graph LR
    A[Enter Details] --> B[Email Verification]
    B --> C[2FA Setup]  
    C --> D[QR Code Scan]
    D --> E[Verify TOTP]
    E --> F[Registration Complete]
```

## DMA Module Components (Mithril)

### Main Container (`DmaModule/00_Mithril.tsx`)

**Purpose**: Main container for Double Materiality Assessment workflow

**Props Interface**:
```typescript
interface MithrilProps {
  projectId?: string;
}
```

**Key Features**:
- **Tab Navigation**: Step-by-step workflow tabs
- **Progress Tracking**: Visual progress indicator
- **Data Persistence**: Auto-save functionality
- **Context Management**: Provides project and company context

**Tab Structure**:
```typescript
const dmaTabs = [
  { id: 'company', label: 'Company Information', component: GeneralCompanyInformation },
  { id: 'stakeholders', label: 'Stakeholder Analysis', component: StakeholderAnalysis },
  { id: 'valuechain', label: 'Value Chain Mapping', component: ValueChainMapping },
  { id: 'topics', label: 'Topics Analysis', component: TopicsAnalysisSelection },
  { id: 'impact', label: 'Impact & Financial Analysis', component: ImpactFinancialAnalysis },
  { id: 'results', label: 'Analysis Results', component: AnalysisResult }
];
```

### Stakeholder Analysis (`DmaModule/StakeholderAnalysis.tsx`)

**Purpose**: Stakeholder management and engagement

**Props Interface**:
```typescript
interface StakeholderAnalysisProps {
  projectId: string;
  onDataUpdate?: (stakeholders: Stakeholder[]) => void;
}
```

**Key Features**:
- **Stakeholder CRUD**: Add, edit, delete stakeholders
- **Email Invitations**: Send survey invitations to stakeholders
- **Progress Tracking**: Monitor stakeholder completion rates
- **Role Assignment**: Assign stakeholder types and responsibilities

**Stakeholder Types**:
- **Internal**: Employees, management, board members
- **External**: Customers, suppliers, regulators, communities
- **Financial**: Investors, lenders, analysts

**Usage Example**:
```tsx
<StakeholderAnalysis 
  projectId={currentProject.id}
  onDataUpdate={(stakeholders) => updateProjectStakeholders(stakeholders)}
/>
```

### Topic Analysis (`DmaModule/TopicsAnalysisSelection.tsx`)

**Purpose**: ESRS topic selection and relevance assessment

**Props Interface**:
```typescript
interface TopicsAnalysisProps {
  projectId: string;
  stakeholders: Stakeholder[];
  onTopicUpdate?: (selections: EsrsTopicSelection[]) => void;
}
```

**Key Features**:
- **ESRS Topic Grid**: Interactive topic selection matrix
- **Relevance Scoring**: Impact and financial materiality scoring
- **Stakeholder Input**: Collect stakeholder perspectives on topics
- **IRO Identification**: Identify Impacts, Risks, and Opportunities

**Topic Categories**:
- **Environmental (E1-E5)**: Climate, pollution, water, biodiversity, circular economy
- **Social (S1-S4)**: Workforce, value chain workers, communities, consumers
- **Governance (G1)**: Business conduct, corporate governance

## CSRD Module Components (Elessar)

### CSRD Layout (`CSRD_Module/CsrdLayout.tsx`)

**Purpose**: Main container for CSRD compliance workflow

**Props Interface**:
```typescript
interface CsrdLayoutProps {
  projectId?: string;
  step?: number;
}
```

**Key Features**:
- **Three-step Workflow**: Project setup → Data sources → Content management
- **Progress Tracking**: Visual step indicator
- **Data Synchronization**: Real-time data sync across steps
- **Export Functionality**: Report generation and export

**Workflow Steps**:
1. **Project Setup**: Company information and project configuration
2. **Data Sources**: Document upload and AI processing
3. **Content Management**: CSRD datapoint completion and report generation

### AI Upload Page (`CSRD_Module/ai-upload-page.tsx`)

**Purpose**: Document upload and AI-powered processing

**Props Interface**:
```typescript
interface AIUploadPageProps {
  projectId: string;
  onProcessingComplete?: (documents: ProcessedDocument[]) => void;
}
```

**Key Features**:
- **Drag & Drop Upload**: Multi-file upload with progress tracking
- **AI Processing**: Automatic document chunking and embedding
- **Format Support**: PDF, Word, Excel, text files
- **Processing Status**: Real-time processing progress
- **Vector Search**: Semantic search across uploaded documents

**Supported File Types**:
```typescript
const supportedFormats = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
  'text/csv'
];
```

**Usage Example**:
```tsx
<AIUploadPage 
  projectId={project.id}
  onProcessingComplete={(docs) => {
    console.log(`Processed ${docs.length} documents`);
    navigateToNextStep();
  }}
/>
```

### CSRD Dashboard (`CSRD_Module/CsrdDashboard.tsx`)

**Purpose**: Content management and datapoint completion

**Props Interface**:
```typescript
interface CsrdDashboardProps {
  projectId: string;
  selectedTopic?: string;
  onDatapointUpdate?: (response: CsrdDatapointResponse) => void;
}
```

**Key Features**:
- **Topic Navigation**: Hierarchical topic/subtopic navigation
- **Dynamic Forms**: Context-aware form generation
- **AI Assistance**: Auto-completion suggestions from processed documents
- **Progress Tracking**: Completion status across all datapoints
- **Draft Saving**: Auto-save functionality

**Form Field Types**:
- **Text Input**: Short and long text responses
- **Multiple Choice**: Radio buttons and checkboxes
- **Numerical**: Numerical inputs with validation
- **Date/Time**: Date pickers and time selectors
- **File Upload**: Document attachments
- **Conditional Fields**: Fields shown based on other responses

## PCF Module Components (Vilya)

### PCF Dashboard (`PCFModule/PCFDashboard.tsx`)

**Purpose**: Product Carbon Footprint calculation and visualization

**Props Interface**:
```typescript
interface PCFDashboardProps {
  productId?: string;
  onCalculationUpdate?: (footprint: CarbonFootprint) => void;
}
```

**Key Features**:
- **Lifecycle Visualization**: Interactive lifecycle phase charts
- **Emission Calculations**: Real-time carbon footprint calculations
- **Comparison Tools**: Compare products and scenarios
- **Export Reports**: Carbon footprint reports and certificates

**Lifecycle Phases**:
```typescript
const lifecyclePhases = [
  { id: 'rawMaterials', label: 'Raw Materials', color: '#FF6B6B' },
  { id: 'production', label: 'Production', color: '#4ECDC4' },
  { id: 'distribution', label: 'Distribution', color: '#45B7D1' },
  { id: 'usePhase', label: 'Use Phase', color: '#96CEB4' },
  { id: 'endOfLife', label: 'End of Life', color: '#FECA57' }
];
```

### Carbon Footprint Calculator (`PCFModule/CarbonFootprint.tsx`)

**Purpose**: Detailed carbon footprint calculation interface

**Props Interface**:
```typescript
interface CarbonFootprintProps {
  productId: string;
  phase: LifecyclePhase;
  onDataUpdate?: (data: EmissionData) => void;
}
```

**Key Features**:
- **Phase-specific Forms**: Tailored inputs for each lifecycle phase
- **Emission Factor Database**: Integrated emission factor lookup
- **Unit Conversion**: Automatic unit conversions
- **Uncertainty Analysis**: Confidence intervals for calculations

**Calculation Example**:
```typescript
const calculateEmissions = (data: EmissionInput): EmissionResult => {
  const { quantity, unit, emissionFactor } = data;
  const normalizedQuantity = convertToBaseUnit(quantity, unit);
  const emissions = normalizedQuantity * emissionFactor.value;
  
  return {
    emissions,
    unit: 'kgCO2e',
    uncertainty: emissionFactor.uncertainty,
    source: emissionFactor.source
  };
};
```

## Shared Components

### Chart Components

**Purpose**: Reusable data visualization components

**Available Charts**:
- **DonutChart**: Progress and completion visualization
- **BarChart**: Comparative data analysis
- **LineChart**: Trend analysis over time
- **HeatMap**: Materiality matrix visualization
- **TreeMap**: Hierarchical data representation

**Usage Example**:
```tsx
import { DonutChart } from '../analytics/DonutScoreCard';

<DonutChart 
  data={completionData}
  centerText="75% Complete"
  colors={['#28a745', '#dc3545', '#ffc107']}
/>
```

### Modal Components

**Purpose**: Reusable modal dialogs for various interactions

**Modal Types**:
- **ConfirmationModal**: Yes/no confirmations
- **FormModal**: Form submissions in modal
- **DetailModal**: Display detailed information
- **ImageModal**: Image preview and zoom

**Props Interface**:
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnOverlayClick?: boolean;
}
```

## State Management

### Context Providers

**AuthContext** (`services/AuthContext.tsx`):
```typescript
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (user: User) => void;
  logout: () => void;
  hasPermission: (permissionKey: string) => boolean;
}
```

**ProjectContext** (DMA module):
```typescript
interface ProjectContextType {
  currentProject: Project | null;
  companies: Company[];
  stakeholders: Stakeholder[];
  updateProject: (project: Partial<Project>) => void;
  addStakeholder: (stakeholder: Stakeholder) => void;
}
```

### Redux Store Structure

**Theme Management**:
```typescript
interface ThemeState {
  lang: string;
  dir: string;
  datanavlayout: string;
  datathememode: 'light' | 'dark';
  datacolormode: string;
  databgcolor: string;
}
```

## Component Development Guidelines

### Naming Conventions
- **Components**: PascalCase (e.g., `UserDashboard.tsx`)
- **Props Interfaces**: `ComponentNameProps`
- **Hook Functions**: `useComponentName`
- **CSS Classes**: kebab-case (`user-dashboard-container`)

### TypeScript Standards
```typescript
// Always define props interface
interface ComponentProps {
  required: string;
  optional?: number;
  callback?: (data: any) => void;
}

// Use React.FC with props interface
export const Component: React.FC<ComponentProps> = ({ 
  required, 
  optional = 0, 
  callback 
}) => {
  // Component implementation
};
```

### Error Handling
```typescript
const [error, setError] = useState<string | null>(null);
const [loading, setLoading] = useState(false);

const handleAsyncOperation = async () => {
  try {
    setLoading(true);
    setError(null);
    await apiCall();
  } catch (err) {
    setError(err instanceof Error ? err.message : 'An error occurred');
  } finally {
    setLoading(false);
  }
};
```

### Performance Optimization
- **React.memo**: Wrap components that receive stable props
- **useMemo**: Memoize expensive calculations
- **useCallback**: Memoize event handlers passed as props
- **Lazy Loading**: Use React.lazy for route-based code splitting

This component documentation provides a comprehensive guide for understanding, using, and maintaining the React components in the Parabella CSRD FAQ Tool frontend application.