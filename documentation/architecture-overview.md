# Architecture Overview

## System Architecture

The Parabella CSRD backend follows a multi-layered architecture with clear separation of concerns. The application is built using Spring Boot and follows modern best practices for enterprise Java applications.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Applications                       │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                           API Layer                              │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
│  │    CSRD     │  │   Mithril   │  │   Elessar   │  │   Auth   ││
│  │ Controllers │  │ Controllers │  │ Controllers │  │Controllers││
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘│
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                         Service Layer                            │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
│  │    CSRD     │  │   Mithril   │  │   Elessar   │  │   Auth   ││
│  │  Services   │  │  Services   │  │  Services   │  │ Services ││
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘│
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                       Repository Layer                           │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
│  │    CSRD     │  │   Mithril   │  │   Elessar   │  │   Auth   ││
│  │Repositories │  │Repositories │  │Repositories │  │Repositories│
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────────┘│
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Database Layer                              │
│                                                                 │
│  ┌─────────────────────┐      ┌─────────────────────┐          │
│  │   Main Database     │      │   Vector Database   │          │
│  │    (PostgreSQL)     │      │    (PostgreSQL)     │          │
│  └─────────────────────┘      └─────────────────────┘          │
└─────────────────────────────────────────────────────────────────┘
```

## Key Components

### 1. API Layer (Controllers)

The API layer consists of REST controllers that handle HTTP requests and responses. The controllers are organized by domain:

- **CSRD Controllers**: Handle CSRD data and reporting endpoints
- **Mithril Controllers**: Handle double materiality analysis endpoints
- **Elessar Controllers**: Handle project management and company information endpoints
- **Auth Controllers**: Handle authentication and user management endpoints

### 2. Service Layer

The service layer contains the business logic of the application. Services are organized by domain and implement the core functionality:

- **CSRD Services**: Implement CSRD data processing and reporting logic
- **Mithril Services**: Implement double materiality analysis logic
- **Elessar Services**: Implement project management and company information logic
- **Auth Services**: Implement authentication and user management logic

### 3. Repository Layer

The repository layer provides data access to the underlying databases. Repositories are implemented using Spring Data JPA:

- **CSRD Repositories**: Access CSRD data entities
- **Mithril Repositories**: Access double materiality analysis entities
- **Elessar Repositories**: Access project management and company information entities
- **Auth Repositories**: Access user and authentication entities

### 4. Database Layer

The application uses a multi-database configuration with two PostgreSQL databases:

- **Main Database**: Stores the core application data (CSRD data, projects, users, etc.)
- **Vector Database**: Stores vector embeddings for AI-powered search and analysis

## Cross-Cutting Concerns

### Security

Security is implemented using Spring Security with JWT authentication. The security configuration is defined in `WebSecurityConfig.java`.

### Exception Handling

Global exception handling is implemented to provide consistent error responses across the API.

### Validation

Input validation is performed using Bean Validation (JSR-380) annotations on DTOs.

### Logging

Logging is implemented using SLF4J and Logback.

### Auditing

Entity auditing is implemented using Hibernate Envers to track changes to entities.

## Module Structure

The application is organized into three main business modules:

### 1. DMA Module (Mithril)

The Double Materiality Assessment (DMA) module implements stakeholder-driven materiality analysis, including:

- Stakeholder management and engagement
- ESRS topic selection and evaluation
- Impact, Risk, and Opportunity (IRO) assessment
- Value chain mapping and analysis
- Materiality matrix generation

### 2. CSRD Module (Elessar)

The CSRD compliance module implements comprehensive sustainability reporting functionality, including:

- CSRD project creation and management
- AI-powered document upload and processing
- Dynamic form generation for CSRD datapoints
- Company information management
- Report generation and export

### 3. PCF Module (Vilya)

The Product Carbon Footprint (PCF) module implements lifecycle carbon assessment, including:

- Carbon footprint calculation across lifecycle phases
- Emission factor management
- Phase-specific data collection (Raw Materials, Production, Distribution, Use Phase, End of Life)
- Carbon analytics and reporting

## Technology Stack

- **Framework**: Spring Boot 3.3.0
- **Language**: Java 21
- **Database**: PostgreSQL 13+ (Dual database: Main + Vector with pgvector)
- **ORM**: Hibernate/JPA with Envers auditing
- **Security**: Spring Security with JWT + 2FA (TOTP)
- **Build Tool**: Gradle
- **Migrations**: Flyway
- **Testing**: JUnit 5, Testcontainers, Mockito
- **Documentation**: SpringDoc OpenAPI 3 (Swagger UI available at `/swagger-ui.html`)
- **AI Integration**: OpenAI GPT-4o-mini, Vector embeddings
