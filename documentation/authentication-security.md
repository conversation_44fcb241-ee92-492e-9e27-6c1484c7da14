# Authentication and Security

This document describes the authentication and security mechanisms implemented in the Parabella CSRD backend.

## Authentication

The application uses JWT (JSON Web Token) based authentication. This stateless authentication mechanism is well-suited for RESTful APIs and provides a secure way to authenticate users.

### Authentication Flow

1. User submits credentials (username/password)
2. Server validates credentials against the database
3. If valid, server generates a JWT token and returns it to the client
4. Client includes the JWT token in the Authorization header for subsequent requests
5. Server validates the token for each protected request

### JWT Configuration

The JWT configuration is defined in the application properties:

```properties
parabella_csrd_db.jwtSecret=${JWT_SECRET}
parabella_csrd_db.jwtExpirationMs=86400000
```

The JWT secret is stored as an environment variable for security.

### JWT Token Structure

The JWT token consists of three parts:

1. **Header**: Contains the token type and signing algorithm
2. **Payload**: Contains claims about the user (subject, issued at, expiration, roles, permissions)
3. **Signature**: Ensures the token hasn't been tampered with

#### JWT Payload Claims

The JWT payload includes the following custom claims for RBAC:

```json
{
  "sub": "username",
  "id": 123,
  "email": "<EMAIL>",
  "roles": ["Admin"],
  "permissions": ["user.view", "user.edit", "role.view", "dma.read", "dma.edit"],
  "iat": 1640995200,
  "exp": 1640996100
}
```

**Critical Fix**: The JWT creation now correctly includes:
- `roles` array with role names (e.g., `["Admin"]`)
- `permissions` array with permission keys (e.g., `["user.view", "dma.read"]`)

This ensures frontend permission checking works correctly with `hasPermission(permissionKey)`.

### Authentication Controller

The `AuthController` class handles authentication requests:

```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder encoder;

    @Autowired
    private JwtUtils jwtUtils;

    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        // Authenticate user and generate JWT token
    }

    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequest signupRequest) {
        // Register new user
    }

    @PostMapping("/refreshtoken")
    public ResponseEntity<?> refreshToken(@Valid @RequestBody TokenRefreshRequest request) {
        // Refresh JWT token
    }
}
```

### JWT Utilities

The `JwtUtils` class provides utilities for working with JWT tokens:

```java
@Component
public class JwtUtils {

    private String jwtSecret;
    private int jwtExpirationMs;

    public String generateJwtToken(Authentication authentication) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
        
        // Get role name (e.g., "Admin")
        String roleName = userPrincipal.getRole();
        
        // Get permissions (e.g., ["user.view", "dma.read"])
        List<String> permissions = userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        return Jwts.builder()
                .setSubject(userPrincipal.getUsername())
                .claim("id", userPrincipal.getId())
                .claim("email", userPrincipal.getEmail())
                .claim("roles", Collections.singletonList(roleName))
                .claim("permissions", permissions)
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationMs))
                .signWith(key(), SignatureAlgorithm.HS256)
                .compact();
    }

    public String getUserNameFromJwtToken(String token) {
        // Extract username from JWT token
    }

    public boolean validateJwtToken(String authToken) {
        // Validate JWT token
    }
}
```

### Authentication Endpoints Bug Fix

**Critical Issue Fixed**: The `/api/auth/me` endpoint was returning incorrect permission data.

#### Problem
The endpoint was using `Permission::getFunctionName` (human-readable names) instead of `Permission::getFunctionKey` (permission keys), causing frontend permission checks to fail.

#### Solution
Updated both `/signin` and `/me` endpoints to correctly map permissions:

```java
// BEFORE (incorrect)
permissions = userRole.getPermissions().stream()
    .map(Permission::getFunctionName)  // Returns "View Users"
    .collect(Collectors.toList());

// AFTER (correct)  
permissions = userRole.getPermissions().stream()
    .map(Permission::getFunctionKey)   // Returns "user.view"
    .collect(Collectors.toList());
```

This fix ensures frontend `hasPermission('user.view')` calls work correctly.

### JWT Authentication Filter

The `AuthTokenFilter` class intercepts requests and validates JWT tokens:

```java
public class AuthTokenFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // Extract and validate JWT token
        // Set authentication in security context if valid
    }
}
```

## Frontend Permission Integration

### AuthContext Permission Checking

The frontend `AuthContext` provides centralized permission checking:

```typescript
interface AuthContextType {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    login: (user: User) => void;
    logout: () => void;
    hasPermission: (permissionKey: string) => boolean;
}

const hasPermission = useCallback((permissionKey: string): boolean => {
    if (!user || !user.permissions) {
        return false;
    }
    // Admin role has all permissions
    if (user.roles?.includes("Admin")) {
        return true;
    }
    return user.permissions.includes(permissionKey);
}, [user]);
```

### Component-Level Permission Checks

Components now properly check permissions before rendering:

```typescript
// UserManagement.tsx
const { hasPermission } = useAuth();
const canViewUsers = hasPermission('user.view');
const canInviteUsers = hasPermission('user.invite');
const canEditUsers = hasPermission('user.edit');
const canDeleteUsers = hasPermission('user.delete');

if (!canViewUsers) {
    return (
        <Container className="text-center mt-5">
            <h1>Access Denied</h1>
            <p>You do not have permission to view users.</p>
        </Container>
    );
}
```

### Permission-Based UI Rendering

UI elements are conditionally rendered based on permissions:

```typescript
{canInviteUsers && (
    <Button variant="primary" onClick={() => setShowInviteModal(true)}>
        <PersonPlusFill className="me-2" />Invite User
    </Button>
)}

{canEditUsers && (
    <Button variant="link" onClick={() => openEditModal(user)}>
        <Pencil />
    </Button>
)}
```

## Backend Controller Security

### Method-Level Security

Controllers use Spring Security's `@PreAuthorize` annotation for endpoint protection:

```java
@RestController
@RequestMapping("/api/users")
public class UserManagementController {

    @GetMapping
    @PreAuthorize("hasAuthority('user.view')")
    public ResponseEntity<List<UserDto>> getUsers() {
        return ResponseEntity.ok(userService.findAllUsers());
    }

    @PostMapping("/invite")
    @PreAuthorize("hasAuthority('user.invite')")
    public ResponseEntity<UserDto> inviteUser(@Valid @RequestBody UserInviteRequest request) {
        return new ResponseEntity<>(userService.inviteUser(request), HttpStatus.CREATED);
    }

    @PutMapping("/{userId}/role")
    @PreAuthorize("hasAuthority('user.edit')")
    public ResponseEntity<UserDto> updateUserRole(@PathVariable Long userId, 
                                                  @Valid @RequestBody UserUpdateRoleRequest request) {
        return ResponseEntity.ok(userService.updateUserRole(userId, request));
    }

    @DeleteMapping("/{userId}")
    @PreAuthorize("hasAuthority('user.delete')")
    public void deleteUser(@PathVariable Long userId) {
        userService.deleteUser(userId);
    }
}
```

### Role Management Security

```java
@RestController
@RequestMapping("/api/role-management")
public class RoleManagementController {

    @GetMapping("/roles")
    @PreAuthorize("hasAuthority('role.view')")
    public ResponseEntity<List<RoleDto>> getRoles() {
        return ResponseEntity.ok(roleService.findAllRoles());
    }

    @PostMapping("/roles")
    @PreAuthorize("hasAuthority('role.create')")
    public ResponseEntity<RoleDto> createRole(@Valid @RequestBody RoleCreateDto createDto) {
        return new ResponseEntity<>(roleService.createRole(createDto), HttpStatus.CREATED);
    }

    @PutMapping("/roles/{roleId}")
    @PreAuthorize("hasAuthority('role.edit')")
    public ResponseEntity<RoleDto> updateRolePermissions(@PathVariable Integer roleId,
                                                         @Valid @RequestBody RolePermissionsUpdateDto updateDto) {
        return ResponseEntity.ok(roleService.updateRolePermissions(roleId, updateDto));
    }

    @DeleteMapping("/roles/{roleId}")
    @PreAuthorize("hasAuthority('role.delete')")
    public void deleteRole(@PathVariable Integer roleId) {
        roleService.deleteRole(roleId);
    }
}
```

## Security Configuration

The security configuration is defined in the `WebSecurityConfig` class:

```java
@Configuration
@EnableWebSecurity
public class WebSecurityConfig {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private AuthEntryPointJwt unauthorizedHandler;

    @Bean
    public AuthTokenFilter authenticationJwtTokenFilter() {
        return new AuthTokenFilter();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth ->
                        auth.requestMatchers("/api/auth/**").permitAll()
                                .requestMatchers("/api/test/**").permitAll()
                                .requestMatchers("/api/csrd/**").permitAll()
                                .requestMatchers("/api/companies/**").authenticated()
                                .requestMatchers("/api/excel/data/**").authenticated()
                                .requestMatchers("/api/progress/**").authenticated()
                                .requestMatchers("/api/projects/**").authenticated()
                                .requestMatchers("/api/stakeholder/**").permitAll()
                                .anyRequest().authenticated()
                );

        http.authenticationProvider(authenticationProvider());
        http.addFilterBefore(authenticationJwtTokenFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
```

## CORS Configuration

Cross-Origin Resource Sharing (CORS) is configured to allow requests from specific origins:

```java
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("https://storage.googleapis.com", "http://localhost:5173",
                        "https://parabella-elessar-1091242934000.europe-west3.run.app",
                        "https://gcr-parabella-staging-frontend-1091242934000.europe-west10.run.app",
                        "https://gcr-parabella-staging-frontend-1091242934000.europe-west4.run.app",
                        "https://parabella.app")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
```

## Role-Based Access Control (RBAC) System

The application implements a comprehensive Role-Based Access Control system with granular permissions.

### Permission System Architecture

The RBAC system consists of three main entities:
- **Users**: Individual system users
- **Roles**: Collections of permissions (e.g., "Admin", "User", "Moderator")  
- **Permissions**: Granular access rights (e.g., "user.view", "role.edit", "dma.read")

### User Entity

The `User` entity represents a user in the system with a **one-to-one** relationship to roles:

```java
@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 50)
    private String username;

    @NotBlank
    @Size(max = 100)
    @Email
    private String email;

    @NotBlank
    @Size(max = 120)
    private String password;

    // One-to-one relationship with Role
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id")
    private Role role;

    private String totpSecret;
    private boolean totpEnabled;

    // Constructors, getters, and setters
}
```

### Role Entity

The `Role` entity contains permissions and represents user roles:

```java
@Entity
@Table(name = "roles")
public class Role {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(nullable = false, unique = true)
    private String name; // e.g., "Admin", "User", "Moderator"

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();

    // Constructors, getters, and setters
}
```

### Permission Entity

The `Permission` entity defines granular access rights:

```java
@Entity
@Table(name = "permissions")
public class Permission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    // Unique key for the permission (e.g., "user.view", "dma.read")
    @Column(nullable = false, unique = true)
    private String functionKey;

    // Human-readable name (e.g., "View Users", "Read DMA")
    @Column(nullable = false)
    private String functionName;

    // Category for grouping (e.g., "User Management", "DMA Module")
    @Column(nullable = false)
    private String category;

    // Constructors, getters, and setters
}
```

### Standard Permission Keys

The system uses the following permission key format:

- **User Management**: `user.view`, `user.invite`, `user.edit`, `user.delete`
- **Role Management**: `role.view`, `role.create`, `role.edit`, `role.delete`  
- **DMA Module**: `dma.read`, `dma.edit`
- **Project Management**: `project.create`, `project.view`, `project.edit`, `project.delete`

### User Details Service

The `UserDetailsServiceImpl` class implements the Spring Security `UserDetailsService` interface:

```java
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: " + username));

        return UserDetailsImpl.build(user);
    }
}
```

## Password Management

### Password Encoding

Passwords are encoded using BCrypt:

```java
@Bean
public PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
}
```

### Password Reset

The application supports password reset functionality:

1. User requests a password reset by providing their email
2. A password reset token is generated and stored in the database
3. An email with a reset link is sent to the user
4. User clicks the link and provides a new password
5. The token is validated and the password is updated

## Two-Factor Authentication (2FA)

The application supports Time-based One-Time Password (TOTP) for two-factor authentication:

1. User enables 2FA in their account settings
2. A TOTP secret is generated and stored in the user's record
3. User configures their authenticator app with the secret
4. For subsequent logins, user must provide both password and TOTP code

## Troubleshooting Permission Issues

### Common Issues and Solutions

#### 1. "Failed to load user data" Error
**Symptom**: User sees generic error message instead of access denied
**Cause**: Frontend making API calls without permission checks
**Solution**: Ensure components check permissions before making API calls

```typescript
// Before API call
if (!canViewUsers) {
    setIsLoading(false);
    return; // Don't make API call
}
```

#### 2. Permission Keys vs Function Names Mismatch  
**Symptom**: Admin users can't access features they should have access to
**Cause**: Backend returning `functionName` instead of `functionKey`
**Solution**: Ensure endpoints use `Permission::getFunctionKey`

```java
// Correct usage
permissions = userRole.getPermissions().stream()
    .map(Permission::getFunctionKey)  // "user.view"
    .collect(Collectors.toList());
```

#### 3. JWT Token Missing Permissions
**Symptom**: `hasPermission()` always returns false
**Cause**: JWT not including permissions claim or using wrong field
**Solution**: Verify JWT includes both `roles` and `permissions` arrays

#### 4. Frontend Permission Check Failing
**Symptom**: Admin role not getting permissions  
**Cause**: Admin fallback logic not working
**Solution**: Ensure `hasPermission` includes Admin check:

```typescript
if (user.roles?.includes("Admin")) {
    return true; // Admin has all permissions
}
```

### Debug Commands

#### Verify JWT Token Contents
```javascript
// In browser console
const token = localStorage.getItem('parabella_access_token');
const payload = JSON.parse(atob(token.split('.')[1]));
console.log('Roles:', payload.roles);
console.log('Permissions:', payload.permissions);
```

#### Check User Context
```typescript
// Add to component for debugging
console.log('User:', user);
console.log('Has user.view:', hasPermission('user.view'));
console.log('User roles:', user?.roles);
console.log('User permissions:', user?.permissions);
```

#### Backend Permission Verification
```bash
# Check database permissions for a role
SELECT r.name, p.function_key, p.function_name 
FROM roles r 
JOIN role_permissions rp ON r.id = rp.role_id 
JOIN permissions p ON rp.permission_id = p.id 
WHERE r.name = 'Admin';
```

## Advanced Security Features

### 1. Multi-Layer Security Architecture

The application implements a comprehensive multi-layer security model:

```mermaid
graph TB
    subgraph "Application Security Layers"
        L1[Network Security Layer]
        L2[Application Gateway Layer]  
        L3[Authentication Layer]
        L4[Authorization Layer]
        L5[Session Management Layer]
        L6[Data Protection Layer]
        L7[Audit & Monitoring Layer]
    end
    
    L1 --> L2
    L2 --> L3
    L3 --> L4
    L4 --> L5
    L5 --> L6
    L6 --> L7
    
    subgraph "Security Controls"
        HTTPS[HTTPS/TLS]
        CORS[CORS Policy]
        JWT[JWT Tokens]
        RBAC[Role-Based Access]
        SessionMgmt[Session Management]
        Encryption[Data Encryption]
        Logging[Security Logging]
    end
    
    L1 -.-> HTTPS
    L2 -.-> CORS
    L3 -.-> JWT
    L4 -.-> RBAC
    L5 -.-> SessionMgmt
    L6 -.-> Encryption
    L7 -.-> Logging
```

### 2. Advanced Token Security

#### Refresh Token Security Features
- **Single Use**: Each refresh invalidates the previous token
- **Device Binding**: Tokens tied to device fingerprints  
- **IP Validation**: Anomaly detection for IP changes
- **Concurrent Session Limits**: Maximum 5 active sessions per user
- **Automatic Cleanup**: Daily cleanup of expired tokens

#### Enhanced JWT Security
```java
// JWT with security claims and revocation support
public String generateJwtToken(Authentication authentication) {
    UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
    
    return Jwts.builder()
            .setSubject(userPrincipal.getUsername())
            .claim("id", userPrincipal.getId())
            .claim("email", userPrincipal.getEmail())
            .claim("roles", Collections.singletonList(userPrincipal.getRole()))
            .claim("permissions", userPrincipal.getPermissions())
            .claim("jti", UUID.randomUUID().toString())      // JWT ID for revocation
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + jwtExpirationMs))
            .signWith(getSigningKey(), SignatureAlgorithm.HS256)
            .compact();
}
```

### 3. Session Security

#### Session Fingerprinting
```typescript
// Frontend session fingerprinting for anomaly detection
class SessionFingerprint {
    static generate(): string {
        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset()
        ].join('|');
        
        return btoa(fingerprint).substring(0, 32);
    }
}
```

#### Session Limit Enforcement
```java
@Service
public class SessionLimitService {
    
    @Value("${parabella_csrd_db.maxActiveRefreshTokensPerUser:5}")
    private int maxActiveRefreshTokensPerUser;
    
    public void enforceSessionLimit(User user) {
        long activeCount = refreshTokenRepository.countActiveTokensByUser(user, Instant.now());
        
        if (activeCount >= maxActiveRefreshTokensPerUser) {
            // Revoke oldest sessions
            List<RefreshToken> oldestTokens = refreshTokenRepository
                .findOldestActiveTokensByUser(user, activeCount - maxActiveRefreshTokensPerUser + 1);
            
            oldestTokens.forEach(token -> {
                token.setRevoked(true);
                refreshTokenRepository.save(token);
            });
        }
    }
}
```

### 4. Input Validation & Sanitization

#### Server-Side Security Validation
```java
public class SecurityUtils {
    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");
    
    private static final Pattern STRONG_PASSWORD_PATTERN = 
        Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$");
    
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }
    
    public static boolean isStrongPassword(String password) {
        return password != null && STRONG_PASSWORD_PATTERN.matcher(password).matches();
    }
}
```

#### Content Security Policy
```java
// CSP header configuration
class CspHeaderFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        httpResponse.setHeader("Content-Security-Policy", 
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "img-src 'self' data: https:; " +
            "connect-src 'self' https://api.parabella.com"
        );
        
        chain.doFilter(request, response);
    }
}
```

### 5. Security Monitoring & Audit

#### Security Event Logging
```java
@Entity
public class SecurityEvent {
    private String eventType; // LOGIN, LOGOUT, ACCESS_DENIED, etc.
    private String username;
    private String ipAddress;
    private String userAgent;
    private String outcome; // SUCCESS, FAILURE, BLOCKED
    private Instant timestamp;
    private String additionalInfo; // JSON format for extra details
}

@Service
public class SecurityAuditService {
    public void logSecurityEvent(SecurityEventType type, String username, 
                                 HttpServletRequest request, String outcome) {
        SecurityEvent event = new SecurityEvent();
        event.setEventType(type.name());
        event.setUsername(username);
        event.setIpAddress(getClientIpAddress(request));
        event.setOutcome(outcome);
        event.setTimestamp(Instant.now());
        
        securityEventRepository.save(event);
    }
}
```

#### Anomaly Detection
```java
@Service
public class AnomalyDetectionService {
    
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void detectAnomalies() {
        detectMultipleFailedLogins();
        detectTokenAnomalies();
    }
    
    private void detectMultipleFailedLogins() {
        // Detect and block IPs with 5+ failed login attempts in 5 minutes
        Instant fiveMinutesAgo = Instant.now().minus(Duration.ofMinutes(5));
        
        List<String> suspiciousIps = securityEventRepository
            .findFailedLoginsByTimeframe(fiveMinutesAgo, Instant.now())
            .stream()
            .collect(Collectors.groupingBy(SecurityEvent::getIpAddress, Collectors.counting()))
            .entrySet()
            .stream()
            .filter(entry -> entry.getValue() >= 5)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
        
        suspiciousIps.forEach(this::blockSuspiciousIp);
    }
}
```

## Security Best Practices

The application implements the following comprehensive security best practices:

1. **Secure Password Storage**: Passwords are hashed using BCrypt with salt
2. **JWT Token Security**: JWT tokens are signed with HMAC-SHA256 and include revocation IDs
3. **HTTPS**: All communication is encrypted using TLS 1.2+
4. **CORS**: Cross-Origin Resource Sharing is configured with specific allowed origins
5. **Input Validation**: All input is validated using Bean Validation and custom security checks
6. **CSRF Protection**: CSRF protection is disabled for the API (stateless authentication)
7. **Granular Permissions**: Access controlled by specific permission keys, not just roles
8. **Method-Level Security**: Backend endpoints protected with `@PreAuthorize`
9. **Frontend Permission Checks**: Components validate permissions before rendering
10. **Content Security Policy**: CSP headers prevent XSS attacks
11. **Session Management**: Proactive session monitoring with timeout warnings
12. **Rate Limiting**: API rate limiting implemented to prevent abuse
13. **Security Logging**: Comprehensive audit trail of all security events
14. **Anomaly Detection**: Automated detection of suspicious activities
15. **SQL Injection Prevention**: Parameterized queries and input sanitization
16. **Session Limits**: Maximum concurrent sessions per user
17. **Device Fingerprinting**: Session binding to device characteristics
18. **Token Rotation**: Refresh tokens are single-use with automatic rotation

## Migration Guide

### Database Migration

#### 1. Create Refresh Tokens Table
Run the following SQL to add refresh token support:

```sql
-- Create refresh_tokens table
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL,
    device_info VARCHAR(500),
    ip_address VARCHAR(45),
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT fk_refresh_token_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_refresh_token_user ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_token_expiry ON refresh_tokens(expiry_date);
CREATE INDEX idx_refresh_token_active ON refresh_tokens(is_revoked, expiry_date);
```

#### 2. Update User Table for Role Relationship
Ensure proper one-to-one role relationship:

```sql
-- Add role_id column if not exists
ALTER TABLE users ADD COLUMN role_id INTEGER;
ALTER TABLE users ADD CONSTRAINT fk_user_role FOREIGN KEY (role_id) REFERENCES roles(id);

-- Create indexes
CREATE INDEX idx_user_role ON users(role_id);
```

### Frontend Migration

#### 1. Update API Service Usage
Replace direct axios usage with the configured api instance:

```typescript
// Before: Direct axios usage
import axios from 'axios';

const getUsers = async () => {
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/users', {
        headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
};

// After: Using configured api service
import api from '../../../../services/api.ts';

const getUsers = async () => {
    const response = await api.get('/api/users');
    return response.data;
};
```

#### 2. Update Token Storage
Replace direct localStorage usage with tokenService:

```typescript
// Before: Direct localStorage
localStorage.setItem('token', token);
const token = localStorage.getItem('token');

// After: Using tokenService
import { tokenService } from './tokenService';

tokenService.setTokens(accessToken, refreshToken);
const token = tokenService.getAccessToken();
```

#### 3. Update Permission Checks
Replace role-based checks with permission-based checks:

```typescript
// Before: Role-based checks
const canEditUser = user?.role === 'ADMIN';
const canViewReports = user?.role === 'ADMIN' || user?.role === 'MANAGER';

// After: Permission-based checks  
const { hasPermission } = useAuth();
const canEditUser = hasPermission('user.edit');
const canViewReports = hasPermission('reports.view');
```

#### 4. Add Session Warning Component
Include session timeout warnings in your main layout:

```typescript
// Add to main App component
import { SessionTimeoutWarning } from './components/SessionTimeoutWarning';

function App() {
    return (
        <AuthProvider>
            <Router>
                <Routes>
                    {/* Your routes */}
                </Routes>
                <SessionTimeoutWarning />
            </Router>
        </AuthProvider>
    );
}
```

### Backend Migration

#### 1. Update Authentication Controller
Ensure proper JWT and permission handling:

```java
// Update AuthController to use correct permission mapping
@PostMapping("/signin")
public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
    // ... authentication logic ...
    
    // IMPORTANT: Use Permission::getFunctionKey for JWT claims
    List<String> permissions = userRole.getPermissions().stream()
        .map(Permission::getFunctionKey)  // Not getFunctionName!
        .collect(Collectors.toList());
    
    JwtResponse response = new JwtResponse(
        jwt, refreshToken.getToken(), userDetails.getId(),
        userDetails.getUsername(), userDetails.getEmail(),
        Collections.singletonList(userRole.getName()), // roles array
        permissions // permission keys array
    );
    
    return ResponseEntity.ok(response);
}
```

#### 2. Add Method-Level Security
Add `@PreAuthorize` annotations to controllers:

```java
@RestController
@RequestMapping("/api/users")
public class UserManagementController {

    @GetMapping
    @PreAuthorize("hasAuthority('user.view')")
    public ResponseEntity<List<UserDto>> getUsers() {
        return ResponseEntity.ok(userService.findAllUsers());
    }

    @PostMapping("/invite")
    @PreAuthorize("hasAuthority('user.invite')")
    public ResponseEntity<UserDto> inviteUser(@Valid @RequestBody UserInviteRequest request) {
        return new ResponseEntity<>(userService.inviteUser(request), HttpStatus.CREATED);
    }
}
```

#### 3. Configure Refresh Token Service
Add refresh token service configuration:

```properties
# Application properties
parabella_csrd_db.jwtExpirationMs=900000          # 15 minutes
parabella_csrd_db.jwtRefreshExpirationMs=604800000 # 7 days  
parabella_csrd_db.maxActiveRefreshTokensPerUser=5
```

### Testing Migration

#### 1. Test Authentication Flow
```bash
# Test complete authentication flow
curl -X POST http://localhost:8080/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password","totpCode":123456}'

# Test token refresh
curl -X POST http://localhost:8080/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{"refreshToken":"your-refresh-token"}'
```

#### 2. Test Permission Endpoints
```bash
# Test user management (requires user.view)
curl -X GET http://localhost:8080/api/users \
  -H "Authorization: Bearer your-jwt-token"

# Test role management (requires role.view)  
curl -X GET http://localhost:8080/api/role-management/roles \
  -H "Authorization: Bearer your-jwt-token"
```

#### 3. Verify Frontend Permission Checks
- Load user management page with "User" role → Should see "Access Denied"
- Load same page with "Admin" role → Should see user list
- Check browser console for permission debugging logs

### Common Issues & Solutions

#### Issue: Permission checks fail for Admin users
**Solution**: Ensure `hasPermission` includes Admin fallback:
```typescript
if (user.roles?.includes("Admin")) {
    return true; // Admin has all permissions
}
```

#### Issue: JWT tokens missing permissions
**Solution**: Verify backend uses `Permission::getFunctionKey`:
```java
// Correct
.map(Permission::getFunctionKey)  // Returns "user.view"
// Incorrect  
.map(Permission::getFunctionName) // Returns "View Users"
```

#### Issue: API calls return 401 for authenticated users
**Solution**: Check if endpoints use relative URLs and api service is configured with base URL.

This migration guide ensures a smooth transition to the enhanced authentication and permission system.
