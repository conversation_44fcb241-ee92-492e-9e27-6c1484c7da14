# Module Workflows and Features

This document provides comprehensive workflow documentation for the three core business modules of the Parabella CSRD FAQ Tool.

## Module Overview

The system implements three specialized modules for comprehensive CSRD compliance:

```mermaid
graph TB
    subgraph "Business Modules"
        DMA[DMA Module<br/>Mithril<br/>Double Materiality<br/>Assessment]
        CSRD[CSRD Module<br/>Elessar<br/>Compliance<br/>Reporting]
        PCF[PCF Module<br/>Vilya<br/>Product Carbon<br/>Footprint]
    end
    
    subgraph "Shared Services"
        AUTH[Authentication<br/>& Authorization]
        AI[AI Services<br/>OpenAI Integration]
        EMAIL[Email Services<br/>Notifications]
        AUDIT[Audit Trail<br/>& Versioning]
    end
    
    DMA --> AUTH
    CSRD --> AUTH
    PCF --> AUTH
    
    DMA --> EMAIL
    CSRD --> AI
    CSRD --> EMAIL
    
    DMA --> AUDIT
    CSRD --> AUDIT
    PCF --> AUDIT
```

---

## DMA Module (Mithril) - Double Materiality Assessment

### Purpose and Scope

The DMA Module implements the **Double Materiality Assessment** required under CSRD, enabling companies to identify and prioritize material sustainability topics through stakeholder engagement and impact analysis.

### Module Architecture

```mermaid
graph TB
    subgraph "DMA Workflow Steps"
        STEP1[1. Company Information<br/>General Setup]
        STEP2[2. Stakeholder Analysis<br/>Identification & Engagement]
        STEP3[3. Value Chain Mapping<br/>Upstream & Downstream]
        STEP4[4. Topics Analysis<br/>ESRS Selection]
        STEP5[5. Impact Assessment<br/>IRO Evaluation]
        STEP6[6. Results Dashboard<br/>Materiality Matrix]
        
        STEP1 --> STEP2
        STEP2 --> STEP3
        STEP3 --> STEP4
        STEP4 --> STEP5
        STEP5 --> STEP6
    end
    
    subgraph "Data Flow"
        DB[(Database)]
        EMAIL[Email Service]
        EXPORT[Export Engine]
        
        STEP2 --> EMAIL
        STEP6 --> EXPORT
        STEP1 --> DB
        STEP2 --> DB
        STEP3 --> DB
        STEP4 --> DB
        STEP5 --> DB
        STEP6 --> DB
    end
```

### Detailed Workflow

#### Step 1: Company Information Setup

**Purpose**: Establish project foundation and company context

**Key Features**:
- **Company Profile**: Basic company information (name, industry, size, revenue)
- **Project Configuration**: Project scope, timeline, and objectives
- **Group Structure**: Support for multi-company group analysis
- **Regulatory Context**: Applicable CSRD requirements and thresholds

**Data Model**:
```typescript
interface CompanyInfo {
  id: string;
  companyName: string;
  industry: string;
  revenue: number;
  employees: string;
  headquarters: string;
  isPublicCompany: boolean;
  csrdApplicable: boolean;
  reportingYear: number;
}
```

**User Journey**:
1. Create new DMA project
2. Enter company basic information
3. Define project scope and timeline
4. Configure group structure (if applicable)
5. Review CSRD applicability criteria

#### Step 2: Stakeholder Analysis and Engagement

**Purpose**: Identify and engage relevant stakeholders for materiality input

**Key Features**:
- **Stakeholder Identification**: Comprehensive stakeholder mapping
- **Email Invitations**: Automated survey distribution
- **Progress Tracking**: Real-time completion monitoring
- **Role Assignment**: Internal vs. external stakeholder classification

**Stakeholder Categories**:
```typescript
enum StakeholderType {
  EMPLOYEES = "employees",
  MANAGEMENT = "management", 
  BOARD = "board_members",
  CUSTOMERS = "customers",
  SUPPLIERS = "suppliers",
  INVESTORS = "investors",
  REGULATORS = "regulators",
  COMMUNITIES = "communities",
  NGOS = "ngos",
  MEDIA = "media"
}
```

**Email Integration**:
```typescript
interface StakeholderInvitation {
  stakeholderId: string;
  email: string;
  token: string; // Unique survey access token
  surveyUrl: string;
  expirationDate: Date;
  remindersSent: number;
}
```

**User Journey**:
1. Define stakeholder categories relevant to business
2. Add stakeholder contacts with roles
3. Configure survey parameters
4. Send email invitations to stakeholders
5. Monitor response rates and send reminders
6. Review collected stakeholder input

#### Step 3: Value Chain Mapping

**Purpose**: Map upstream and downstream value chain relationships

**Key Features**:
- **Value Chain Visualization**: Interactive value chain diagram
- **Relationship Mapping**: Supplier and customer relationships
- **Impact Identification**: Value chain impact points
- **Geographic Analysis**: Regional value chain presence

**Value Chain Structure**:
```typescript
interface ValueChainObject {
  id: string;
  name: string;
  type: 'supplier' | 'customer' | 'partner' | 'subsidiary';
  industry: string;
  country: string;
  relationshipType: 'direct' | 'indirect';
  sustainabilityRisk: 'low' | 'medium' | 'high';
  annualValue: number;
}
```

**User Journey**:
1. Map upstream suppliers and service providers
2. Identify downstream customers and distribution channels
3. Assess sustainability risks across value chain
4. Document geographic presence and local impacts
5. Prioritize high-risk value chain relationships

#### Step 4: ESRS Topics Analysis and Selection

**Purpose**: Evaluate relevance of ESRS sustainability topics

**Key Features**:
- **ESRS Topic Grid**: All 10 ESRS topical standards
- **Relevance Assessment**: Binary relevant/not relevant decisions
- **Reasoning Documentation**: Justification for topic relevance
- **Stakeholder Integration**: Incorporate stakeholder perspectives

**ESRS Topic Structure**:
```typescript
interface EsrsTopicSelection {
  id: string;
  esrsTopicId: string; // E1, E2, E3, E4, E5, S1, S2, S3, S4, G1
  companyId: string;
  stakeholderId?: string;
  isRelevant: boolean;
  relevanceReason?: string;
  irrelevanceReason?: string;
  impactScore?: number; // 1-5 scale
  financialScore?: number; // 1-5 scale
}
```

**ESRS Topics**:
- **Environmental (E1-E5)**: Climate change, pollution, water, biodiversity, circular economy
- **Social (S1-S4)**: Own workforce, value chain workers, affected communities, consumers
- **Governance (G1)**: Business conduct

**User Journey**:
1. Review ESRS topic descriptions and requirements
2. Assess business relevance for each topic
3. Document reasoning for inclusion/exclusion decisions
4. Incorporate stakeholder feedback on topic relevance
5. Validate selections against regulatory requirements

#### Step 5: Impact, Risk, and Opportunity (IRO) Assessment

**Purpose**: Detailed evaluation of material topics for impact and financial materiality

**Key Features**:
- **IRO Identification**: Specific impacts, risks, and opportunities
- **Magnitude Assessment**: Quantitative impact scoring
- **Likelihood Evaluation**: Probability assessment
- **Financial Materiality**: Revenue/cost impact analysis
- **Time Horizon**: Short, medium, long-term considerations

**IRO Data Model**:
```typescript
interface IroEvaluation {
  id: string;
  topicSelectionId: string;
  name: string;
  description: string;
  type: 'impact' | 'risk' | 'opportunity';
  
  // Impact Materiality
  impactMagnitude: number; // 1-5 scale
  impactLikelihood: number; // 1-5 scale
  impactScope: 'local' | 'national' | 'regional' | 'global';
  
  // Financial Materiality  
  financialMagnitude: number; // 1-5 scale
  financialLikelihood: number; // 1-5 scale
  financialImpactType: 'revenue' | 'costs' | 'assets' | 'liabilities';
  
  timeHorizon: 'short' | 'medium' | 'long';
  mitigationMeasures?: string;
  opportunityValue?: number;
}
```

**Assessment Matrix**:
```
Impact Magnitude × Likelihood = Impact Materiality Score
Financial Magnitude × Likelihood = Financial Materiality Score

Materiality Threshold: Topics scoring ≥ 3.0 in either dimension
```

**User Journey**:
1. For each relevant ESRS topic, identify specific IROs
2. Assess impact magnitude and likelihood
3. Evaluate financial materiality implications
4. Document time horizons and scope
5. Define mitigation measures and opportunity capture strategies
6. Calculate overall materiality scores

#### Step 6: Results Dashboard and Materiality Matrix

**Purpose**: Visualize materiality assessment results and generate reports

**Key Features**:
- **Materiality Matrix**: Interactive bubble chart visualization
- **Topic Prioritization**: Ranked list of material topics
- **Stakeholder Summary**: Stakeholder engagement statistics
- **Export Capabilities**: PDF, Excel, and CSV export
- **Audit Trail**: Complete decision audit trail

**Materiality Matrix Visualization**:
```typescript
interface MaterialityDataPoint {
  topicId: string;
  topicName: string;
  impactMaterialityScore: number; // X-axis
  financialMaterialityScore: number; // Y-axis
  bubbleSize: number; // Stakeholder consensus level
  color: string; // Topic category (E/S/G)
  isMaterial: boolean; // Above materiality threshold
}
```

**Export Formats**:
- **Executive Summary**: High-level materiality results
- **Detailed Report**: Complete assessment documentation
- **Stakeholder Report**: Engagement summary and feedback
- **Regulatory Mapping**: CSRD disclosure requirement mapping

---

## CSRD Module (Elessar) - Compliance Reporting

### Purpose and Scope

The CSRD Module implements comprehensive **CSRD compliance reporting** with AI-powered document processing, dynamic form generation, and automated report compilation.

### Module Architecture

```mermaid
graph TB
    subgraph "CSRD Workflow"
        SETUP[1. Project Setup<br/>Company & Scope]
        UPLOAD[2. Data Sources<br/>AI Document Processing]
        CONTENT[3. Content Management<br/>Datapoint Completion]
        REPORTS[4. Report Generation<br/>Export & Validation]
        
        SETUP --> UPLOAD
        UPLOAD --> CONTENT
        CONTENT --> REPORTS
    end
    
    subgraph "AI Processing Pipeline"
        DOC[Document Upload]
        CHUNK[Text Chunking]
        EMBED[Vector Embeddings]
        SEARCH[Semantic Search]
        SUGGEST[Auto-completion]
        
        DOC --> CHUNK
        CHUNK --> EMBED
        EMBED --> SEARCH
        SEARCH --> SUGGEST
    end
    
    subgraph "Data Layer"
        MAIN[(Main Database<br/>Business Data)]
        VECTOR[(Vector Database<br/>AI Embeddings)]
        
        SETUP --> MAIN
        CONTENT --> MAIN
        UPLOAD --> VECTOR
        SUGGEST --> VECTOR
    end
    
    UPLOAD --> DOC
    CONTENT --> SUGGEST
```

### Detailed Workflow

#### Step 1: CSRD Project Setup

**Purpose**: Configure CSRD reporting project and company context

**Key Features**:
- **Company Information**: Enhanced company profile for CSRD requirements
- **Reporting Scope**: Consolidated vs. separate reporting
- **Material Topics**: Import from DMA module or manual selection
- **Disclosure Requirements**: Map topics to ESRS disclosure requirements

**CSRD Project Model**:
```typescript
interface CsrdProject {
  id: string;
  projectName: string;
  companyInfoId: string;
  reportingYear: number;
  reportingType: 'consolidated' | 'separate';
  applicableESRS: string[]; // E1, E2, etc.
  materialTopics: string[];
  status: 'draft' | 'in_progress' | 'review' | 'final';
  dueDate: Date;
  createdBy: string;
  lastModified: Date;
}
```

**User Journey**:
1. Create new CSRD reporting project
2. Configure company information and reporting scope
3. Import material topics from DMA assessment
4. Map topics to applicable ESRS disclosure requirements
5. Set reporting timeline and milestones

#### Step 2: Data Sources and AI Document Processing

**Purpose**: Upload and process sustainability documents using AI

**Key Features**:
- **Multi-format Upload**: PDF, Word, Excel, CSV support
- **AI Document Processing**: Automatic text extraction and chunking
- **Vector Embeddings**: Semantic document indexing
- **Processing Status**: Real-time progress tracking
- **Document Management**: Organize and categorize uploaded files

**AI Processing Pipeline**:
```typescript
interface DocumentProcessingPipeline {
  upload: (files: File[]) => Promise<UploadResult>;
  extractText: (document: UploadedFile) => Promise<string>;
  chunkText: (text: string) => TextChunk[];
  generateEmbeddings: (chunks: TextChunk[]) => Promise<VectorEmbedding[]>;
  storeVectors: (embeddings: VectorEmbedding[]) => Promise<void>;
  indexForSearch: (chunks: ProcessedChunk[]) => Promise<void>;
}
```

**Supported Document Types**:
- **Sustainability Reports**: Annual and sustainability reports
- **Policies**: Environmental, social, governance policies
- **Data Sheets**: Environmental data, social metrics
- **Certifications**: ISO certifications, third-party validations
- **Assessments**: LCA studies, audit reports

**Processing Workflow**:
```mermaid
sequenceDiagram
    participant U as User
    participant API as API Server
    participant AI as AI Service
    participant VDB as Vector DB
    
    U->>API: Upload documents
    API->>AI: Process documents
    AI->>AI: Extract text content
    AI->>AI: Split into chunks
    AI->>AI: Generate embeddings
    AI->>VDB: Store vectors
    VDB-->>API: Confirm storage
    API-->>U: Processing complete
```

**User Journey**:
1. Upload sustainability-related documents
2. Configure processing parameters (chunk size, overlap)
3. Monitor real-time processing progress
4. Review processed document chunks
5. Validate document categorization and metadata

#### Step 3: Content Management and Datapoint Completion

**Purpose**: Complete CSRD datapoints with AI-assisted content generation

**Key Features**:
- **Dynamic Forms**: Context-aware form generation
- **AI Auto-completion**: Suggest responses based on processed documents
- **Progress Tracking**: Completion status across all disclosure requirements
- **Version Control**: Track changes and maintain audit trail
- **Collaboration**: Multi-user editing with conflict resolution

**CSRD Data Structure**:
```typescript
interface CsrdDatapointResponse {
  id: string;
  projectId: string;
  fieldId: string; // Links to CSRD field definition
  userId: string;
  responseValue: string | number | boolean | File;
  responseStatus: 'empty' | 'draft' | 'complete' | 'reviewed';
  aiSuggestion?: string;
  isAiGenerated: boolean;
  sourceDocuments?: string[]; // Document chunk references
  lastModified: Date;
  reviewComments?: string;
}
```

**AI Auto-completion Process**:
```typescript
interface AIAutocompletion {
  datapointId: string;
  query: string; // Natural language query based on field description
  
  searchDocuments: (query: string) => Promise<RelevantChunk[]>;
  generateResponse: (context: RelevantChunk[]) => Promise<string>;
  validateResponse: (response: string, field: CsrdField) => ValidationResult;
  suggestToUser: (suggestion: string, confidence: number) => void;
}
```

**Form Field Types**:
- **Text Fields**: Short and long text responses
- **Numerical Fields**: Metrics with units and validation
- **Multiple Choice**: Dropdown and radio button selections
- **Date Fields**: Reporting periods and milestone dates
- **File Uploads**: Supporting documentation
- **Tables**: Structured data entry (e.g., emissions by scope)
- **Conditional Fields**: Fields displayed based on other responses

**User Journey**:
1. Navigate CSRD topic hierarchy
2. Complete datapoints for each disclosure requirement
3. Review AI-generated suggestions
4. Validate responses against source documents
5. Save drafts and track completion progress
6. Submit sections for review and approval

#### Step 4: Report Generation and Export

**Purpose**: Generate comprehensive CSRD reports for regulatory submission

**Key Features**:
- **Multi-format Export**: PDF, HTML, XML (ESEF format)
- **Template Engine**: Customizable report templates
- **Data Validation**: Completeness and consistency checks
- **Regulatory Mapping**: ESRS disclosure requirement mapping
- **Digital Signatures**: Electronic signature support

**Report Generation Pipeline**:
```typescript
interface ReportGeneration {
  validateCompleteness: (projectId: string) => ValidationResult;
  generateSections: (topics: string[]) => Promise<ReportSection[]>;
  applyTemplate: (sections: ReportSection[], template: ReportTemplate) => Promise<Document>;
  exportToPDF: (document: Document) => Promise<Blob>;
  exportToXML: (document: Document) => Promise<string>; // ESEF format
  generateAuditTrail: (projectId: string) => Promise<AuditReport>;
}
```

**Export Formats**:
- **Executive Summary**: High-level sustainability performance
- **Complete CSRD Report**: Full regulatory compliance report
- **Assurance Package**: Documentation for external assurance
- **Data Export**: Structured data for regulatory systems
- **Public Report**: Stakeholder-facing sustainability report

**Validation Checks**:
- **Completeness**: All mandatory datapoints completed
- **Consistency**: Cross-references and calculations validated
- **Data Quality**: Outlier detection and reasonableness checks
- **Regulatory Compliance**: ESRS requirement coverage
- **Audit Trail**: Complete change history

---

## PCF Module (Vilya) - Product Carbon Footprint

### Purpose and Scope

The PCF Module implements **Product Carbon Footprint** calculation following international standards (ISO 14067, GHG Protocol), enabling lifecycle carbon assessment and environmental impact analysis.

### Module Architecture

```mermaid
graph TB
    subgraph "PCF Workflow"
        PRODUCT[1. Product Definition<br/>Scope & Boundaries]
        RAW[2. Raw Materials<br/>Cradle-to-Gate]
        PROD[3. Production<br/>Manufacturing]
        DIST[4. Distribution<br/>Transport & Storage]
        USE[5. Use Phase<br/>Consumer Usage]
        EOL[6. End of Life<br/>Disposal & Recycling]
        RESULTS[7. Results Analysis<br/>Hotspot Identification]
        
        PRODUCT --> RAW
        RAW --> PROD
        PROD --> DIST
        DIST --> USE
        USE --> EOL
        EOL --> RESULTS
    end
    
    subgraph "Calculation Engine"
        EF[(Emission Factors<br/>Database)]
        CALC[Calculation Engine]
        VALID[Validation Engine]
        
        RAW --> CALC
        PROD --> CALC
        DIST --> CALC
        USE --> CALC
        EOL --> CALC
        CALC --> EF
        CALC --> VALID
    end
    
    subgraph "Reporting"
        CHARTS[Charts & Analytics]
        EXPORT[Export & Certificates]
        COMPARE[Product Comparison]
        
        RESULTS --> CHARTS
        RESULTS --> EXPORT
        RESULTS --> COMPARE
    end
```

### Detailed Workflow

#### Step 1: Product Definition and Scope

**Purpose**: Define product boundaries and functional unit

**Key Features**:
- **Product Specification**: Detailed product description and specifications
- **Functional Unit**: Definition of calculation basis (per unit, per kg, per service)
- **System Boundaries**: Cradle-to-gate, cradle-to-grave, or gate-to-gate
- **Cut-off Criteria**: Materiality thresholds for inclusion
- **Geographic Scope**: Regional considerations for calculations

**Product Model**:
```typescript
interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  functionalUnit: {
    unit: string; // 'piece', 'kg', 'liter', 'service hour'
    description: string;
    referenceAmount: number;
  };
  systemBoundary: 'cradle-to-gate' | 'cradle-to-grave' | 'gate-to-gate';
  geographicScope: string[];
  studyPurpose: string;
  intendedAudience: string[];
  cutOffCriteria: number; // Percentage threshold
}
```

**User Journey**:
1. Create new PCF assessment project
2. Define product specifications and variants
3. Set functional unit and system boundaries
4. Configure geographic and temporal scope
5. Establish data quality requirements

#### Step 2: Raw Materials Assessment

**Purpose**: Calculate emissions from raw material extraction and processing

**Key Features**:
- **Material Inventory**: Comprehensive bill of materials
- **Emission Factors**: Industry-standard emission factor database
- **Supplier Data**: Primary data integration where available
- **Allocation Methods**: Mass, economic, or energy-based allocation
- **Uncertainty Analysis**: Data quality and uncertainty assessment

**Raw Materials Model**:
```typescript
interface RawMaterial {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  emissionFactor: {
    value: number; // kgCO2e per unit
    unit: string;
    source: string; // Database or supplier data
    geography: string;
    year: number;
    uncertainty: number; // Percentage
  };
  supplierData?: {
    supplierId: string;
    primaryData: boolean;
    dataQuality: 'high' | 'medium' | 'low';
  };
  allocation?: {
    method: 'mass' | 'economic' | 'energy';
    factor: number;
  };
}
```

**Calculation Process**:
```typescript
const calculateRawMaterialEmissions = (materials: RawMaterial[]): EmissionResult => {
  return materials.reduce((total, material) => {
    const baseEmissions = material.quantity * material.emissionFactor.value;
    const allocatedEmissions = material.allocation 
      ? baseEmissions * material.allocation.factor 
      : baseEmissions;
    
    return {
      totalEmissions: total.totalEmissions + allocatedEmissions,
      uncertainty: calculateCombinedUncertainty(total.uncertainty, material.emissionFactor.uncertainty),
      contributors: [...total.contributors, {
        material: material.name,
        emissions: allocatedEmissions,
        percentage: (allocatedEmissions / total.totalEmissions) * 100
      }]
    };
  }, { totalEmissions: 0, uncertainty: 0, contributors: [] });
};
```

#### Step 3: Production Phase Assessment

**Purpose**: Calculate emissions from manufacturing processes

**Key Features**:
- **Process Mapping**: Detailed production process breakdown
- **Energy Consumption**: Electricity, heat, and fuel consumption
- **Process Emissions**: Direct emissions from chemical reactions
- **Waste Management**: Waste treatment and disposal emissions
- **Facility Data**: Site-specific energy grid factors

**Production Model**:
```typescript
interface ProductionProcess {
  id: string;
  processName: string;
  processType: 'energy' | 'chemical' | 'mechanical' | 'thermal';
  
  energyInputs: {
    electricity: { consumption: number; gridFactor: number; unit: string };
    naturalGas: { consumption: number; emissionFactor: number; unit: string };
    steam: { consumption: number; emissionFactor: number; unit: string };
  };
  
  processEmissions: {
    directEmissions: number; // Process-specific CO2/CH4/N2O
    refrigerants: { type: string; leakageRate: number; gwp: number }[];
  };
  
  waste: {
    wasteType: string;
    quantity: number;
    treatmentMethod: string;
    emissionFactor: number;
  }[];
  
  throughput: number; // Products per time period
  facilityLocation: string;
}
```

#### Step 4: Distribution and Transport

**Purpose**: Calculate emissions from product distribution

**Key Features**:
- **Transport Modes**: Road, rail, sea, air transport options
- **Distance Calculation**: Route optimization and distance matrices
- **Packaging Assessment**: Primary, secondary, tertiary packaging
- **Storage Emissions**: Warehouse energy consumption
- **Cold Chain**: Refrigerated transport considerations

**Distribution Model**:
```typescript
interface DistributionSegment {
  id: string;
  from: string; // Origin location
  to: string; // Destination location
  transportMode: 'road' | 'rail' | 'sea' | 'air' | 'multimodal';
  vehicle: {
    type: string; // Truck size, vessel type, etc.
    fuelType: 'diesel' | 'gasoline' | 'electric' | 'lng' | 'biofuel';
    loadCapacity: number;
    emissionFactor: number; // gCO2e per tonne-km
  };
  distance: number; // km
  load: {
    productQuantity: number;
    totalWeight: number; // Including packaging
    loadFactor: number; // Percentage of vehicle capacity used
  };
  packagingMaterial: {
    primary: MaterialWeight[];
    secondary: MaterialWeight[];
    tertiary: MaterialWeight[];
  };
}
```

#### Step 5: Use Phase Assessment

**Purpose**: Calculate emissions during product use by consumers

**Key Features**:
- **Usage Patterns**: Consumer behavior modeling
- **Energy Consumption**: Product operational energy requirements
- **Consumables**: Replacement parts, refills, maintenance
- **Service Life**: Product lifespan and usage frequency
- **Regional Variations**: Geographic usage pattern differences

**Use Phase Model**:
```typescript
interface UsePhase {
  id: string;
  productLifespan: {
    duration: number;
    unit: 'years' | 'months' | 'cycles' | 'hours';
    usageFrequency: number; // Times used per day/month
  };
  
  energyConsumption: {
    electricityPerUse: number; // kWh per use
    regionalGridFactors: { region: string; factor: number }[];
    standbyPower?: number; // Watts
    standbyHours?: number; // Hours per day
  };
  
  consumables: {
    item: string;
    replacementFrequency: number; // Per year
    emissionFactor: number; // kgCO2e per replacement
  }[];
  
  maintenance: {
    activity: string;
    frequency: number; // Per year
    emissions: number; // kgCO2e per activity
  }[];
  
  userBehavior: {
    scenario: 'conservative' | 'typical' | 'intensive';
    adjustmentFactor: number;
  };
}
```

#### Step 6: End-of-Life Assessment

**Purpose**: Calculate emissions from product disposal and recycling

**Key Features**:
- **Waste Treatment**: Landfill, incineration, recycling options
- **Material Recovery**: Recycling rates and credits
- **Disposal Infrastructure**: Regional waste management systems
- **Avoided Emissions**: Credits for recycled materials
- **Biodegradation**: Emissions from organic material decomposition

**End-of-Life Model**:
```typescript
interface EndOfLife {
  id: string;
  materialComposition: {
    material: string;
    mass: number;
    unit: string;
    recyclable: boolean;
    recyclingRate: number; // Regional average or product-specific
  }[];
  
  wasteScenarios: {
    scenario: string;
    probability: number; // Percentage of products following this path
    treatment: {
      method: 'landfill' | 'incineration' | 'recycling' | 'composting';
      emissionFactor: number; // kgCO2e per kg waste
      energyRecovery?: number; // kWh per kg (for incineration)
      recyclingCredit?: number; // Avoided emissions per kg recycled
    };
  }[];
  
  transportToTreatment: {
    averageDistance: number; // km
    transportEmissionFactor: number; // gCO2e per tonne-km
  };
}
```

#### Step 7: Results Analysis and Reporting

**Purpose**: Analyze results and generate carbon footprint reports

**Key Features**:
- **Hotspot Analysis**: Identify high-emission lifecycle stages
- **Contribution Analysis**: Material and process contribution breakdown
- **Sensitivity Analysis**: Impact of parameter variations
- **Comparison Tools**: Compare products and scenarios
- **Certification Support**: Generate reports for verification

**Results Dashboard**:
```typescript
interface PCFResults {
  totalCarbonFootprint: {
    value: number; // kgCO2e per functional unit
    uncertainty: number; // Percentage
    breakdown: {
      rawMaterials: number;
      production: number;
      distribution: number;
      usePhase: number;
      endOfLife: number;
    };
  };
  
  hotspots: {
    stage: string;
    contribution: number; // Percentage of total
    emissions: number; // kgCO2e
    improvementPotential: string;
  }[];
  
  dataQuality: {
    primaryDataPercentage: number;
    averageUncertainty: number;
    dataQualityScore: 'A' | 'B' | 'C' | 'D';
  };
  
  comparisons?: {
    baseline: string;
    improvement: number; // Percentage reduction
    absoluteDifference: number; // kgCO2e
  }[];
}
```

**Export Capabilities**:
- **PCF Certificate**: ISO 14067 compliant certificate
- **Detailed Report**: Complete calculation documentation
- **Executive Summary**: Business-focused summary
- **Technical Report**: Verification-ready technical documentation
- **Data Export**: Structured data for LCA software integration

---

## Cross-Module Integration

### Shared Data Flow

```mermaid
graph LR
    subgraph "DMA Module"
        DMA_TOPICS[Material Topics]
        DMA_COMPANY[Company Data]
        DMA_STAKEHOLDERS[Stakeholders]
    end
    
    subgraph "CSRD Module"
        CSRD_TOPICS[CSRD Topics]
        CSRD_DATA[Datapoint Responses]
        CSRD_REPORTS[Reports]
    end
    
    subgraph "PCF Module"
        PCF_PRODUCTS[Products]
        PCF_EMISSIONS[Emission Data]
        PCF_RESULTS[Carbon Footprints]
    end
    
    DMA_TOPICS --> CSRD_TOPICS
    DMA_COMPANY --> CSRD_DATA
    CSRD_DATA --> CSRD_REPORTS
    PCF_EMISSIONS --> CSRD_DATA
    PCF_RESULTS --> CSRD_REPORTS
```

### Common Features Across Modules

**Audit Trail**: All modules implement comprehensive audit trails
**User Management**: Role-based access control across all modules
**Export Functionality**: Consistent export formats and templates
**Data Validation**: Comprehensive validation rules and error handling
**Progress Tracking**: Visual progress indicators and completion status
**Collaboration**: Multi-user editing and review workflows

This comprehensive workflow documentation provides the foundation for understanding and implementing the three core business modules of the Parabella CSRD FAQ Tool.