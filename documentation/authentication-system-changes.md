# Authentication System Changes - Parabella CSRD FAQ Tool

## Overview

This document details the recent enhancements to the Parabella CSRD FAQ Tool authentication system, focusing on the implementation of refresh token management, enhanced security features, and improved session handling.

## Key Features Implemented

### 1. Refresh Token System

**New Entity**: `RefreshToken`
- **Location**: `/src/main/java/com/example/parabella_csrd_db/database/maindatabase/model/authentication/RefreshToken.java`
- **Purpose**: Enables secure token refresh without requiring re-authentication

**Key Features**:
- Long-lived tokens (7 days default, configurable)
- Device tracking (User-Agent, IP address)
- Expiration and revocation support
- Automatic cleanup of expired tokens

```java
@Entity
@Table(name = "refresh_tokens")
public class RefreshToken {
    private String token;           // UUID-based token
    private Instant expiryDate;     // Token expiration
    private String deviceInfo;      // User-Agent tracking
    private String ipAddress;       // IP tracking
    private boolean isRevoked;      // Revocation status
    // ... other fields
}
```

### 2. Token Rotation Security

**Implementation**: Each token refresh generates a new refresh token and invalidates the old one
- **Security Benefit**: Prevents token reuse attacks
- **Session Management**: Maintains user sessions while enhancing security
- **Auto-cleanup**: Expired tokens removed daily at 2 AM

### 3. Session Limits

**Configuration**: Maximum 5 active refresh tokens per user (configurable)
- **Purpose**: Prevents unlimited concurrent sessions
- **Behavior**: Oldest tokens are revoked when limit is exceeded
- **Monitoring**: Active token count available via debug endpoint

### 4. Enhanced Authentication Flow

#### New Endpoints Added:

1. **POST /api/auth/refresh** - Token refresh with rotation
2. **POST /api/auth/logout** - Revoke all refresh tokens
3. **GET /api/auth/me** - Session validation
4. **GET /api/auth/debug/tokens** - Active token monitoring

#### Enhanced Endpoints:

1. **POST /api/auth/signin** - Now returns refresh tokens
2. **POST /api/auth/verify2fa** - 2FA setup completion

## Technical Implementation

### 1. Service Layer

**RefreshTokenService**: `/src/main/java/com/example/parabella_csrd_db/service/authentication/RefreshTokenService.java`

Key Methods:
```java
// Create new refresh token with device tracking
RefreshToken createRefreshToken(Long userId, String deviceInfo, String ipAddress)

// Validate token and check expiration/revocation
RefreshToken verifyExpiration(RefreshToken token)

// Revoke all tokens for a user (logout)
int revokeAllUserTokens(Long userId)

// Scheduled cleanup of expired tokens
@Scheduled(cron = "0 0 2 * * ?")
void cleanupExpiredTokens()
```

### 2. Repository Layer

**RefreshTokenRepository**: `/src/main/java/com/example/parabella_csrd_db/database/maindatabase/repository/authentication/RefreshTokenRepository.java`

Key Queries:
```java
// Find by token string
Optional<RefreshToken> findByToken(String token)

// Count active tokens for user
long countActiveTokensByUser(User user, Instant now)

// Bulk revocation
@Modifying
int revokeAllUserTokens(User user)

// Automatic cleanup
@Modifying
int deleteExpiredTokens(Instant now)
```

### 3. DTOs

**New DTOs Added**:
- `TokenRefreshRequest` - Refresh token input
- `TokenRefreshResponse` - New token pair output
- Enhanced `JwtResponse` - Now includes refresh token

### 4. Exception Handling

**TokenRefreshException**: Custom exception for token-related errors
- **HTTP Status**: 403 Forbidden
- **Message Format**: Includes token reference and specific error
- **Security**: Prevents token enumeration attacks

## Security Enhancements

### 1. Token Security
- **Access Tokens**: Short-lived (15 minutes) for API access
- **Refresh Tokens**: Long-lived (7 days) for token renewal
- **Rotation**: New refresh token issued on each refresh
- **Revocation**: Immediate invalidation on logout

### 2. Session Management
- **Device Tracking**: Each token associated with device/IP
- **Session Limits**: Maximum concurrent sessions per user
- **Automatic Cleanup**: Prevents token table bloat

### 3. Attack Prevention
- **Token Reuse**: Rotation prevents replay attacks
- **Session Hijacking**: Device tracking helps detect anomalies
- **Brute Force**: Rate limiting on sensitive endpoints
- **Token Enumeration**: Generic error messages

## Configuration Options

### Application Properties
```properties
# Refresh token duration (default: 7 days)
parabella_csrd_db.jwtRefreshExpirationMs=604800000

# Maximum active refresh tokens per user (default: 5)
parabella_csrd_db.maxActiveRefreshTokensPerUser=5

# Access token duration (default: 15 minutes)
parabella_csrd_db.jwtExpirationMs=900000
```

## Frontend Integration Guide

### 1. Login Flow
```javascript
const response = await fetch('/api/auth/signin', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username, password, totpCode })
});

const { accessToken, refreshToken } = await response.json();
localStorage.setItem('accessToken', accessToken);
localStorage.setItem('refreshToken', refreshToken);
```

### 2. Token Refresh Implementation
```javascript
const refreshAccessToken = async () => {
  const refreshToken = localStorage.getItem('refreshToken');
  
  const response = await fetch('/api/auth/refresh', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken })
  });
  
  if (response.ok) {
    const { accessToken, refreshToken: newRefreshToken } = await response.json();
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', newRefreshToken);
    return accessToken;
  } else {
    // Refresh failed, redirect to login
    localStorage.clear();
    window.location.href = '/login';
  }
};
```

### 3. API Call Wrapper
```javascript
const apiCall = async (url, options = {}) => {
  let token = localStorage.getItem('accessToken');
  
  const makeRequest = (authToken) => fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${authToken}`
    }
  });
  
  let response = await makeRequest(token);
  
  // If token expired, try to refresh
  if (response.status === 401) {
    const newToken = await refreshAccessToken();
    if (newToken) {
      response = await makeRequest(newToken);
    }
  }
  
  return response;
};
```

### 4. Logout Implementation
```javascript
const logout = async () => {
  const token = localStorage.getItem('accessToken');
  
  await fetch('/api/auth/logout', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  localStorage.clear();
  window.location.href = '/login';
};
```

## Database Schema Changes

### New Table: `refresh_tokens`
```sql
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    token VARCHAR(255) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    device_info TEXT,
    ip_address VARCHAR(45),
    is_revoked BOOLEAN NOT NULL DEFAULT FALSE
);

-- Indexes for performance
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_token ON refresh_tokens(token);
CREATE INDEX idx_refresh_tokens_expiry ON refresh_tokens(expiry_date);
```

## Monitoring and Debugging

### 1. Debug Endpoints
- **GET /api/auth/debug/tokens** - Check active token count for user
- Useful for troubleshooting session issues

### 2. Logging
- Token creation/refresh logged with user and device info
- Failed refresh attempts logged for security monitoring
- Cleanup operations logged with deletion counts

### 3. Metrics to Monitor
- Active refresh token count per user
- Token refresh frequency
- Failed refresh attempts
- Cleanup operation effectiveness

## Migration Guide

### For Existing Users
1. **No Action Required**: Existing JWT tokens continue to work
2. **Enhanced Security**: New logins will use refresh token system
3. **Gradual Migration**: Users will get refresh tokens on next login

### For Frontend Developers
1. **Update Login Handling**: Store both access and refresh tokens
2. **Implement Token Refresh**: Add automatic token refresh logic
3. **Update Logout**: Call logout endpoint to revoke tokens
4. **Error Handling**: Handle refresh token expiration scenarios

## Security Considerations

### 1. Token Storage
- **Access Tokens**: Can be stored in memory or sessionStorage
- **Refresh Tokens**: Should be stored securely (httpOnly cookies preferred)
- **Never**: Store tokens in localStorage for production (XSS risk)

### 2. HTTPS Required
- All token operations must use HTTPS in production
- Tokens contain sensitive authentication information

### 3. Token Lifecycle
- Monitor token usage patterns for anomalies
- Implement alerting for unusual refresh patterns
- Consider geographic/device-based restrictions

## Testing

### 1. Unit Tests
- Token creation and validation
- Expiration handling
- Revocation logic
- Cleanup operations

### 2. Integration Tests
- End-to-end authentication flow
- Token refresh scenarios
- Multi-device session handling
- Error conditions

### 3. Security Tests
- Token reuse prevention
- Session hijacking simulation
- Concurrent session limits
- Cleanup effectiveness

## Performance Considerations

### 1. Database Impact
- Additional table for refresh tokens
- Regular cleanup operations
- Indexed queries for performance

### 2. Network Impact
- Additional refresh token requests
- Larger response payloads (includes refresh token)

### 3. Memory Impact
- Token validation caches
- Service layer object management

## Troubleshooting Common Issues

### 1. Refresh Token Not Found
- **Cause**: Token expired or revoked
- **Solution**: Redirect user to login
- **Prevention**: Implement proper error handling

### 2. Too Many Active Sessions
- **Cause**: User exceeded session limit
- **Solution**: System automatically revokes oldest tokens
- **User Action**: May need to re-login on older devices

### 3. Token Refresh Failures
- **Cause**: Network issues, expired refresh token, or server errors
- **Solution**: Implement retry logic with exponential backoff
- **Fallback**: Redirect to login after failed retries

## Future Enhancements

### 1. Advanced Session Management
- Geographic session tracking
- Device fingerprinting
- Suspicious activity detection

### 2. Token Security
- Encrypted refresh tokens
- Token binding to client certificates
- Hardware security module integration

### 3. User Experience
- Session management dashboard
- Active device listing
- Remote session termination

---

## Configuration Summary

| Setting | Default Value | Description |
|---------|---------------|-------------|
| `jwtExpirationMs` | 900000 (15 min) | Access token lifetime |
| `jwtRefreshExpirationMs` | 604800000 (7 days) | Refresh token lifetime |
| `maxActiveRefreshTokensPerUser` | 5 | Concurrent session limit |

## API Endpoint Summary

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/auth/signin` | POST | Login with refresh token |
| `/api/auth/refresh` | POST | Refresh access token |
| `/api/auth/logout` | POST | Revoke all refresh tokens |
| `/api/auth/me` | GET | Validate current session |
| `/api/auth/debug/tokens` | GET | Check active token count |

This enhanced authentication system provides a robust foundation for secure session management while maintaining backward compatibility and ease of use.