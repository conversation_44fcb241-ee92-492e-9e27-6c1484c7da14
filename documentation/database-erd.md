# Database Entity Relationship Diagrams (ERDs)

This document provides comprehensive Entity Relationship Diagrams for the Parabella CSRD FAQ Tool database architecture, organized by business domain.

## Database Overview

The system uses a **dual-database architecture**:
- **Main Database**: Core business data with PostgreSQL
- **Vector Database**: AI embeddings and document processing with PostgreSQL + pgvector

## Authentication & Security Domain

```mermaid
erDiagram
    User {
        bigint id PK
        varchar username UK
        varchar email UK  
        varchar password
        varchar totp_secret
        timestamp created_at
        timestamp updated_at
    }
    
    Role {
        int id PK
        varchar name UK
        varchar description
        timestamp created_at
        timestamp updated_at
    }
    
    Permission {
        int id PK
        varchar permission_key UK
        varchar description
        varchar category
        timestamp created_at
        timestamp updated_at
    }
    
    UserRole {
        bigint user_id PK,FK
        int role_id PK,FK
        timestamp assigned_at
        bigint assigned_by FK
    }
    
    RolePermission {
        int role_id PK,FK
        int permission_id PK,FK
        timestamp granted_at
        bigint granted_by FK
    }
    
    PasswordResetToken {
        bigint id PK
        varchar token UK
        bigint user_id FK
        timestamp expiry_date
        boolean used
        timestamp created_at
    }
    
    VerificationToken {
        bigint id PK
        varchar token UK
        bigint user_id FK
        varchar token_type
        timestamp expiry_date
        boolean used
        timestamp created_at
    }
    
    User ||--o{ UserRole : has
    Role ||--o{ UserRole : assigned_to
    Role ||--o{ RolePermission : has
    Permission ||--o{ RolePermission : granted_by
    User ||--o{ PasswordResetToken : requests
    User ||--o{ VerificationToken : verifies
    User ||--o{ UserRole : assigns
    User ||--o{ RolePermission : grants
```

## DMA Domain (Mithril) - Double Materiality Assessment

```mermaid
erDiagram
    Project {
        bigint id PK
        bigint user_id FK
        varchar project_name
        text project_description
        varchar project_type
        bigint company_id FK
        bigint company_group_id FK
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    Company {
        bigint id PK
        varchar company_name
        varchar address
        varchar vat
        varchar num_employees
        double revenues
        varchar industry
        boolean is_sub_company
        bigint company_group_id FK
        bigint project_id FK
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    CompanyGroup {
        bigint id PK
        varchar group_name
        varchar industry
        varchar headquarters
        varchar description
        bigint project_id FK
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    Stakeholder {
        bigint id PK
        bigint project_id FK
        varchar stakeholder_name
        varchar stakeholder_email
        varchar role
        varchar stakeholder_type
        bigint company_id FK
        varchar token UK
        varchar status
        int completed_datapoints
        int total_datapoints
        boolean is_responsible
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    EsrsTopic {
        bigint id PK
        varchar area
        varchar esrs_code UK
        varchar topic
        varchar subtopic
        text description
        timestamp created_at
        timestamp updated_at
    }
    
    EsrsTopicSelection {
        bigint id PK
        bigint esrs_topic_id FK
        bigint stakeholder_id FK
        bigint company_id FK
        boolean relevant
        text relevance_reason
        text irrelevance_reason
        double impact_score
        double financial_score
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    Iro {
        bigint id PK
        bigint topic_selection_id FK
        varchar name
        varchar iro_type
        text description
        varchar status
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    IroEvaluation {
        bigint id PK
        bigint iro_id FK
        text description
        double magnitude
        double likelihood
        double impact_score
        double financial_impact
        varchar evaluation_status
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    ValueChain {
        bigint id PK
        varchar name
        text description
        varchar chain_type
        bigint project_id FK
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    ValueChainObject {
        bigint id PK
        varchar name
        varchar industry
        text description
        bigint company_id FK
        bigint value_chain_id FK
        varchar relationship_type
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    User ||--o{ Project : owns
    Project ||--o{ Company : contains
    Project ||--o{ CompanyGroup : manages
    CompanyGroup ||--o{ Company : groups
    Project ||--o{ Stakeholder : engages
    Company ||--o{ Stakeholder : represents
    Stakeholder ||--o{ EsrsTopicSelection : evaluates
    EsrsTopic ||--o{ EsrsTopicSelection : selected_as
    EsrsTopicSelection ||--o{ Iro : identifies
    Iro ||--o{ IroEvaluation : evaluated_by
    Project ||--o{ ValueChain : analyzes
    ValueChain ||--o{ ValueChainObject : contains
    Company ||--o{ ValueChainObject : participates_in
```

## CSRD Domain (Elessar) - CSRD Compliance Reporting

```mermaid
erDiagram
    CsrdProject {
        bigint id PK
        bigint user_id FK
        varchar project_name
        text project_description
        varchar project_type
        bigint company_info_id FK
        varchar status
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    CompanyInfo {
        bigint id PK
        varchar company_name
        varchar revenue
        text industry
        varchar size
        varchar number_of_employees
        varchar country
        varchar sector
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    CsrdTopic {
        bigint id PK
        varchar esrs_section
        varchar topic_code UK
        varchar topic_name
        text description
        int display_order
        timestamp created_at
        timestamp updated_at
    }
    
    CsrdSubtopic {
        bigint id PK
        bigint csrd_topic_id FK
        varchar subtopic_code UK
        varchar subtopic_name
        text description
        int display_order
        timestamp created_at
        timestamp updated_at
    }
    
    CsrdSubtopicSection {
        bigint id PK
        bigint csrd_subtopic_id FK
        varchar section_code UK
        varchar section_name
        text description
        int display_order
        timestamp created_at
        timestamp updated_at
    }
    
    CsrdField {
        bigint id PK
        bigint csrd_subtopic_section_id FK
        varchar field_name
        varchar field_type
        varchar input_type
        boolean is_required
        text description
        text placeholder_text
        int display_order
        timestamp created_at
        timestamp updated_at
    }
    
    CsrdFieldOption {
        bigint id PK
        bigint csrd_field_id FK
        varchar option_value
        varchar option_label
        int display_order
        timestamp created_at
        timestamp updated_at
    }
    
    CsrdConditionalField {
        bigint id PK
        bigint csrd_field_id FK
        varchar condition_field_name
        varchar condition_operator
        varchar condition_value
        boolean show_when_met
        timestamp created_at
        timestamp updated_at
    }
    
    CsrdDatapointResponse {
        bigint id PK
        bigint csrd_project_id FK
        bigint csrd_field_id FK
        bigint user_id FK
        text response_value
        varchar response_status
        text ai_suggestion
        boolean is_ai_generated
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    CsrdSubtopicGeneratedText {
        bigint id PK
        bigint csrd_project_id FK
        bigint csrd_subtopic_id FK
        text generated_content
        varchar generation_status
        text source_context
        timestamp created_at
        timestamp updated_at
        bigint created_by
        bigint updated_by
    }
    
    User ||--o{ CsrdProject : owns
    CsrdProject ||--o{ CompanyInfo : describes
    CsrdTopic ||--o{ CsrdSubtopic : contains
    CsrdSubtopic ||--o{ CsrdSubtopicSection : organized_in
    CsrdSubtopicSection ||--o{ CsrdField : has
    CsrdField ||--o{ CsrdFieldOption : provides
    CsrdField ||--o{ CsrdConditionalField : conditions
    CsrdProject ||--o{ CsrdDatapointResponse : collects
    CsrdField ||--o{ CsrdDatapointResponse : answers
    User ||--o{ CsrdDatapointResponse : provides
    CsrdProject ||--o{ CsrdSubtopicGeneratedText : generates
    CsrdSubtopic ||--o{ CsrdSubtopicGeneratedText : content_for
```

## Vector Database Domain - AI & Document Processing

```mermaid
erDiagram
    EsrsDatapoint {
        bigint id PK
        varchar disclosure_requirement
        text datapoint_text
        varchar source_id
        vector embedding
        text metadata_json
        varchar status
        timestamp created_at
        timestamp updated_at
    }
    
    ProcessedDocumentChunk {
        bigint id PK
        bigint csrd_project_id FK
        varchar document_title
        varchar document_type
        text chunk_content
        int chunk_index
        int chunk_size
        vector embedding
        text metadata_json
        varchar processing_status
        timestamp created_at
        timestamp updated_at
    }
    
    DocumentSimilarity {
        bigint id PK
        bigint source_chunk_id FK
        bigint target_chunk_id FK
        double similarity_score
        varchar similarity_type
        timestamp calculated_at
    }
    
    DatapointSimilarity {
        bigint id PK
        bigint esrs_datapoint_id FK
        bigint document_chunk_id FK
        double similarity_score
        varchar match_type
        text relevance_context
        timestamp calculated_at
    }
    
    ProcessedDocumentChunk ||--o{ DocumentSimilarity : source_of
    ProcessedDocumentChunk ||--o{ DocumentSimilarity : target_of
    EsrsDatapoint ||--o{ DatapointSimilarity : matches
    ProcessedDocumentChunk ||--o{ DatapointSimilarity : matched_by
```

## Cross-Database Relationships

```mermaid
erDiagram
    %% Main Database Entities
    CsrdProject {
        bigint id PK
        varchar project_name
    }
    
    User {
        bigint id PK
        varchar username
    }
    
    %% Vector Database Entities
    ProcessedDocumentChunk {
        bigint id PK
        bigint csrd_project_id FK
        text chunk_content
        vector embedding
    }
    
    %% Cross-database relationships (logical, not enforced by FK)
    CsrdProject ||--o{ ProcessedDocumentChunk : documents_processed_for
```

## Audit Trail Schema

All main database entities inherit from `BaseAuditedEntity` and automatically include:

```mermaid
erDiagram
    BaseAuditedEntity {
        bigint id PK
        timestamp created_at
        timestamp updated_at
    }
    
    RevisionEntity {
        int id PK
        bigint timestamp
        bigint user_id FK
        varchar username
    }
    
    %% All audited entities have corresponding _AUD tables
    User_AUD {
        bigint id PK
        int REV FK
        int REVTYPE
        varchar username
        varchar email
        timestamp created_at_MOD
        timestamp updated_at_MOD
    }
    
    RevisionEntity ||--o{ User_AUD : tracks_changes
```

## Database Indexes and Performance

### Key Indexes for Performance

**Main Database**:
```sql
-- User authentication
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);

-- Project queries
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_created_at ON projects(created_at);

-- Stakeholder management
CREATE INDEX idx_stakeholders_project_id ON stakeholders(project_id);
CREATE INDEX idx_stakeholders_token ON stakeholders(token);

-- CSRD data access
CREATE INDEX idx_csrd_datapoint_response_project ON csrd_datapoint_response(csrd_project_id);
CREATE INDEX idx_csrd_datapoint_response_field ON csrd_datapoint_response(csrd_field_id);

-- Audit trail queries
CREATE INDEX idx_revision_entity_timestamp ON revisionentity(timestamp);
CREATE INDEX idx_revision_entity_user ON revisionentity(user_id);
```

**Vector Database**:
```sql
-- Vector similarity search (HNSW index for fast nearest neighbor)
CREATE INDEX idx_esrs_datapoint_embedding ON esrs_datapoints 
USING hnsw (embedding vector_cosine_ops);

CREATE INDEX idx_document_chunk_embedding ON processed_document_chunks 
USING hnsw (embedding vector_cosine_ops);

-- Document processing queries
CREATE INDEX idx_document_chunk_project ON processed_document_chunks(csrd_project_id);
CREATE INDEX idx_document_chunk_status ON processed_document_chunks(processing_status);
```

## Data Relationships Summary

### Core Relationships
1. **User → Project**: One-to-many (users own multiple projects)
2. **Project → Company**: One-to-many (projects analyze multiple companies)
3. **Company → Stakeholder**: One-to-many (companies have multiple stakeholders)
4. **Stakeholder → EsrsTopicSelection**: One-to-many (stakeholders evaluate topics)
5. **EsrsTopicSelection → Iro**: One-to-many (topics identify multiple IROs)
6. **CsrdProject → CsrdDatapointResponse**: One-to-many (projects collect responses)
7. **CsrdProject → ProcessedDocumentChunk**: One-to-many (projects process documents)

### Key Constraints
- **Unique Constraints**: Usernames, emails, tokens, topic codes
- **Foreign Key Constraints**: Referential integrity across related entities
- **Check Constraints**: Data validation (e.g., status values, score ranges)
- **Not Null Constraints**: Required fields for business logic

This ERD structure supports the complete CSRD compliance workflow while maintaining data integrity and optimal query performance across both traditional relational data and AI-powered vector operations.