# AI Integration Architecture

This document provides comprehensive documentation for the AI-powered features of the Parabella CSRD FAQ Tool, including OpenAI integration, vector database operations, document processing workflows, and semantic search capabilities.

## Overview

The Parabella CSRD FAQ Tool leverages advanced AI technologies to enhance the Corporate Sustainability Reporting Directive (CSRD) compliance process through:

- **Document Intelligence**: Automated processing and analysis of sustainability documents
- **Semantic Search**: Context-aware information retrieval from processed documents
- **AI-Powered Autofill**: Intelligent completion suggestions for CSRD datapoints
- **Conversational AI**: Interactive guidance and support through chat interface
- **Vector-Based Matching**: Similarity matching between ESRS requirements and company data

## Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Interface]
        API_CLIENT[API Client]
    end
    
    subgraph "Application Layer"
        CHAT_CTRL[Chat Controller]
        AI_CTRL[AI Autofill Controller]
        DOC_CTRL[Document Controller]
    end
    
    subgraph "Service Layer"
        OPENAI_SVC[OpenAI Service]
        PROMPT_SVC[Prompting Service]
        CONV_SVC[Conversation Service]
        DOC_SVC[Document Service]
    end
    
    subgraph "AI Processing Pipeline"
        CHUNK[Text Chunking]
        EMBED[Embedding Generation]
        INDEX[Vector Indexing]
        SEARCH[Semantic Search]
    end
    
    subgraph "Data Layer"
        MAIN_DB[(Main Database)]
        VECTOR_DB[(Vector Database<br/>PostgreSQL + pgvector)]
    end
    
    subgraph "External Services"
        OPENAI[OpenAI API<br/>GPT-4o-mini]
        PYTHON_AI[Python AI Service<br/>FastAPI]
    end
    
    WEB --> CHAT_CTRL
    WEB --> AI_CTRL
    WEB --> DOC_CTRL
    
    CHAT_CTRL --> OPENAI_SVC
    AI_CTRL --> PROMPT_SVC
    DOC_CTRL --> DOC_SVC
    
    OPENAI_SVC --> OPENAI
    PROMPT_SVC --> OPENAI
    DOC_SVC --> CHUNK
    
    CHUNK --> EMBED
    EMBED --> INDEX
    INDEX --> VECTOR_DB
    SEARCH --> VECTOR_DB
    
    CONV_SVC --> MAIN_DB
    DOC_SVC --> MAIN_DB
    
    EMBED --> PYTHON_AI
    SEARCH --> PYTHON_AI
```

## OpenAI Integration

### Configuration

**Model Selection**: GPT-4o-mini for optimal balance of performance and cost-effectiveness

**Service Configuration**:
```java
@Service
@Slf4j
public class OpenAIService {
    private static final String MODEL = "gpt-4o-mini";
    private static final String API_URL = "https://api.openai.com/v1/chat/completions";
    
    @Value("${openai.api.key}")
    private String apiKey;
    
    @Value("${openai.api.timeout:30000}")
    private int timeout;
}
```

**Environment Variables**:
```properties
# OpenAI Configuration
openai.api.key=${OPENAI_API_KEY}
openai.api.timeout=30000
openai.model.temperature=0.3
openai.model.max-tokens=2000
```

### API Integration Patterns

#### Chat Completion Request
```java
public class ChatCompletionRequest {
    private String model = "gpt-4o-mini";
    private List<ChatMessage> messages;
    private double temperature = 0.3;
    private int maxTokens = 2000;
    private String user; // User ID for tracking
}
```

#### Error Handling and Retry Logic
```java
@Retryable(
    value = {OpenAIException.class, HttpServerErrorException.class},
    maxAttempts = 3,
    backoff = @Backoff(delay = 1000, multiplier = 2)
)
public ChatCompletionResponse callOpenAI(ChatCompletionRequest request) {
    try {
        return restTemplate.postForObject(API_URL, request, ChatCompletionResponse.class);
    } catch (HttpClientErrorException e) {
        log.error("OpenAI API client error: {}", e.getStatusCode());
        throw new OpenAIException("API request failed", e);
    } catch (HttpServerErrorException e) {
        log.error("OpenAI API server error: {}", e.getStatusCode());
        throw e; // Retry on server errors
    }
}
```

### Rate Limiting and Cost Management

**Request Throttling**:
```java
@Component
public class OpenAIRateLimiter {
    private final RateLimiter rateLimiter = RateLimiter.create(10.0); // 10 requests per second
    
    public void acquire() {
        rateLimiter.acquire();
    }
}
```

**Token Optimization**:
```java
public class TokenOptimizer {
    private static final int MAX_CONTEXT_TOKENS = 4000;
    private static final int RESERVED_RESPONSE_TOKENS = 1000;
    
    public String optimizeContext(String context, String query) {
        int maxContextLength = MAX_CONTEXT_TOKENS - RESERVED_RESPONSE_TOKENS - query.length();
        return context.length() > maxContextLength 
            ? context.substring(0, maxContextLength) + "..."
            : context;
    }
}
```

## Vector Database Architecture

### PostgreSQL + pgvector Setup

**Database Configuration**:
```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Vector storage table
CREATE TABLE processed_document_chunks (
    id BIGSERIAL PRIMARY KEY,
    csrd_project_id BIGINT,
    document_title VARCHAR(255),
    chunk_content TEXT,
    chunk_index INTEGER,
    embedding VECTOR(1536), -- OpenAI embedding dimension
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create HNSW index for fast similarity search
CREATE INDEX ON processed_document_chunks 
USING hnsw (embedding vector_cosine_ops);
```

**Entity Definition**:
```java
@Entity
@Table(name = "processed_document_chunks")
public class ProcessedDocumentChunk {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "csrd_project_id")
    private Long csrdProjectId;
    
    @Column(name = "chunk_content", columnDefinition = "TEXT")
    private String chunkContent;
    
    @Column(name = "embedding", columnDefinition = "vector(1536)")
    private float[] embedding;
    
    @Type(JsonBinaryType.class)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;
}
```

### Vector Operations

#### Similarity Search Query
```java
@Repository
public interface ProcessedDocumentChunkRepository extends JpaRepository<ProcessedDocumentChunk, Long> {
    
    @Query(value = """
        SELECT *, (embedding <=> CAST(:queryEmbedding AS vector)) AS distance
        FROM processed_document_chunks 
        WHERE csrd_project_id = :projectId
        ORDER BY embedding <=> CAST(:queryEmbedding AS vector)
        LIMIT :limit
        """, nativeQuery = true)
    List<Object[]> findSimilarChunks(
        @Param("projectId") Long projectId,
        @Param("queryEmbedding") String queryEmbedding,
        @Param("limit") int limit
    );
}
```

#### Embedding Generation Service
```java
@Service
public class EmbeddingService {
    
    public float[] generateEmbedding(String text) {
        EmbeddingRequest request = EmbeddingRequest.builder()
            .model("text-embedding-ada-002")
            .input(text)
            .build();
            
        EmbeddingResponse response = openAIClient.createEmbedding(request);
        return response.getData().get(0).getEmbedding();
    }
    
    public List<ProcessedDocumentChunk> findSimilarContent(String query, Long projectId) {
        float[] queryEmbedding = generateEmbedding(query);
        return documentChunkRepository.findSimilarChunks(
            projectId, 
            Arrays.toString(queryEmbedding), 
            10
        );
    }
}
```

## Document Processing Pipeline

### Text Chunking Strategy

**Chunking Configuration**:
```java
@Configuration
public class DocumentProcessingConfig {
    @Value("${document.processing.chunk-size:1000}")
    private int chunkSize;
    
    @Value("${document.processing.overlap-size:100}")
    private int overlapSize;
    
    @Value("${document.processing.max-chunks:500}")
    private int maxChunks;
}
```

**Smart Chunking Implementation**:
```java
@Service
public class DocumentChunkingService {
    
    public List<TextChunk> chunkDocument(String content, String documentTitle) {
        List<TextChunk> chunks = new ArrayList<>();
        
        // Split by paragraphs first, then by sentences if needed
        String[] paragraphs = content.split("\n\n");
        StringBuilder currentChunk = new StringBuilder();
        int chunkIndex = 0;
        
        for (String paragraph : paragraphs) {
            if (currentChunk.length() + paragraph.length() > chunkSize) {
                if (currentChunk.length() > 0) {
                    chunks.add(createChunk(currentChunk.toString(), chunkIndex++, documentTitle));
                    
                    // Add overlap from previous chunk
                    String overlap = getOverlap(currentChunk.toString());
                    currentChunk = new StringBuilder(overlap);
                }
            }
            currentChunk.append(paragraph).append("\n\n");
        }
        
        // Add final chunk
        if (currentChunk.length() > 0) {
            chunks.add(createChunk(currentChunk.toString(), chunkIndex, documentTitle));
        }
        
        return chunks;
    }
    
    private String getOverlap(String chunk) {
        if (chunk.length() <= overlapSize) return chunk;
        return chunk.substring(chunk.length() - overlapSize);
    }
}
```

### Asynchronous Processing

**Processing Queue**:
```java
@Service
public class DocumentProcessingService {
    
    @Async("documentProcessingExecutor")
    @Transactional
    public CompletableFuture<ProcessingResult> processDocument(
            MultipartFile file, 
            Long projectId
    ) {
        try {
            // 1. Extract text content
            String content = extractTextContent(file);
            
            // 2. Chunk the document
            List<TextChunk> chunks = chunkingService.chunkDocument(content, file.getOriginalFilename());
            
            // 3. Generate embeddings for each chunk
            List<ProcessedDocumentChunk> processedChunks = new ArrayList<>();
            for (TextChunk chunk : chunks) {
                float[] embedding = embeddingService.generateEmbedding(chunk.getContent());
                
                ProcessedDocumentChunk processedChunk = ProcessedDocumentChunk.builder()
                    .csrdProjectId(projectId)
                    .documentTitle(file.getOriginalFilename())
                    .chunkContent(chunk.getContent())
                    .chunkIndex(chunk.getIndex())
                    .embedding(embedding)
                    .metadata(chunk.getMetadata())
                    .build();
                    
                processedChunks.add(processedChunk);
            }
            
            // 4. Save to vector database
            documentChunkRepository.saveAll(processedChunks);
            
            return CompletableFuture.completedFuture(
                ProcessingResult.success(processedChunks.size())
            );
            
        } catch (Exception e) {
            log.error("Document processing failed", e);
            return CompletableFuture.completedFuture(
                ProcessingResult.failure(e.getMessage())
            );
        }
    }
}
```

## AI-Powered Features Implementation

### Auto-completion Service

**CSRD Datapoint Auto-completion**:
```java
@Service
public class CsrdAutofillService {
    
    public AutofillResponse generateSuggestion(AutofillRequest request) {
        // 1. Find relevant document chunks
        List<ProcessedDocumentChunk> relevantChunks = embeddingService
            .findSimilarContent(request.getQuery(), request.getProjectId());
        
        // 2. Build context from relevant chunks
        String context = buildContext(relevantChunks);
        
        // 3. Generate AI prompt
        String prompt = promptingService.buildAutofillPrompt(
            request.getFieldDescription(),
            context,
            request.getCompanyContext()
        );
        
        // 4. Get AI completion
        ChatCompletionResponse aiResponse = openAIService.getChatCompletion(prompt);
        
        // 5. Extract and validate response
        return AutofillResponse.builder()
            .suggestion(aiResponse.getChoices().get(0).getMessage().getContent())
            .confidence(calculateConfidence(relevantChunks))
            .sourceDocuments(extractSourceInfo(relevantChunks))
            .reasoning(generateReasoning(relevantChunks, request))
            .build();
    }
    
    private double calculateConfidence(List<ProcessedDocumentChunk> chunks) {
        if (chunks.isEmpty()) return 0.0;
        
        // Calculate confidence based on similarity scores and chunk count
        double averageSimilarity = chunks.stream()
            .mapToDouble(chunk -> chunk.getSimilarityScore())
            .average()
            .orElse(0.0);
            
        double chunkFactor = Math.min(chunks.size() / 5.0, 1.0); // Max confidence at 5+ chunks
        
        return averageSimilarity * chunkFactor;
    }
}
```

### Conversation Service

**Chat Flow Management**:
```java
@Service
public class ConversationService {
    
    @Transactional
    public ChatResponse processMessage(ChatRequest request) {
        // 1. Retrieve or create conversation
        Conversation conversation = getOrCreateConversation(request.getConversationId());
        
        // 2. Add user message to conversation
        Message userMessage = Message.builder()
            .conversation(conversation)
            .sender("user")
            .content(request.getMessage())
            .timestamp(LocalDateTime.now())
            .build();
        messageRepository.save(userMessage);
        
        // 3. Build conversation context
        String conversationHistory = buildConversationHistory(conversation);
        
        // 4. Enhance with relevant document context if needed
        String documentContext = "";
        if (request.needsDocumentContext()) {
            List<ProcessedDocumentChunk> relevantChunks = embeddingService
                .findSimilarContent(request.getMessage(), request.getProjectId());
            documentContext = buildDocumentContext(relevantChunks);
        }
        
        // 5. Generate AI response
        String prompt = promptingService.buildChatPrompt(
            conversationHistory,
            documentContext,
            request.getContextMetadata()
        );
        
        ChatCompletionResponse aiResponse = openAIService.getChatCompletion(prompt);
        String aiContent = aiResponse.getChoices().get(0).getMessage().getContent();
        
        // 6. Save AI response
        Message aiMessage = Message.builder()
            .conversation(conversation)
            .sender("assistant")
            .content(aiContent)
            .timestamp(LocalDateTime.now())
            .build();
        messageRepository.save(aiMessage);
        
        // 7. Generate suggested actions
        List<String> suggestedActions = generateSuggestedActions(
            request.getMessage(), 
            aiContent, 
            request.getContextMetadata()
        );
        
        return ChatResponse.builder()
            .response(aiContent)
            .conversationId(conversation.getId())
            .messageId(aiMessage.getId())
            .suggestedActions(suggestedActions)
            .build();
    }
}
```

## Prompt Engineering

### Prompt Templates

**Auto-completion Prompt Template**:
```java
@Component
public class PromptTemplates {
    
    public static final String AUTOFILL_TEMPLATE = """
        You are an expert CSRD (Corporate Sustainability Reporting Directive) consultant helping a company complete their sustainability reporting.
        
        TASK: Provide a comprehensive response for the following CSRD datapoint based on the company's documents.
        
        FIELD TO COMPLETE:
        {fieldDescription}
        
        COMPANY CONTEXT:
        Industry: {industry}
        Size: {companySize}
        
        RELEVANT INFORMATION FROM COMPANY DOCUMENTS:
        {documentContext}
        
        INSTRUCTIONS:
        1. Provide a specific, actionable response based on the company's actual data
        2. Reference specific information from the provided documents
        3. Ensure compliance with CSRD requirements
        4. If information is insufficient, clearly state what additional data is needed
        5. Use professional, clear language suitable for regulatory reporting
        
        RESPONSE FORMAT:
        - Start with a direct answer to the datapoint
        - Support with specific evidence from documents
        - Note any assumptions or limitations
        
        Response:
        """;
}
```

**Chat Prompt Template**:
```java
public static final String CHAT_TEMPLATE = """
    You are a knowledgeable CSRD compliance assistant helping users navigate Corporate Sustainability Reporting Directive requirements.
    
    CONVERSATION CONTEXT:
    {conversationHistory}
    
    CURRENT MODULE: {currentModule}
    CURRENT STEP: {currentStep}
    
    RELEVANT COMPANY INFORMATION:
    {documentContext}
    
    USER QUESTION: {userMessage}
    
    INSTRUCTIONS:
    1. Provide helpful, accurate guidance on CSRD compliance
    2. Reference specific ESRS standards when relevant
    3. Suggest concrete next steps or actions
    4. If discussing materiality assessment, reference double materiality principles
    5. Keep responses focused and actionable
    6. Ask clarifying questions if needed
    
    Response:
    """;
```

### Dynamic Prompt Generation

**Context-Aware Prompt Building**:
```java
@Service
public class PromptingService {
    
    public String buildAutofillPrompt(String fieldDescription, String documentContext, CompanyContext companyContext) {
        return PromptTemplates.AUTOFILL_TEMPLATE
            .replace("{fieldDescription}", fieldDescription)
            .replace("{industry}", companyContext.getIndustry())
            .replace("{companySize}", companyContext.getSize())
            .replace("{documentContext}", documentContext);
    }
    
    public String buildChatPrompt(String conversationHistory, String documentContext, Map<String, Object> metadata) {
        String template = PromptTemplates.CHAT_TEMPLATE
            .replace("{conversationHistory}", conversationHistory)
            .replace("{documentContext}", documentContext)
            .replace("{userMessage}", (String) metadata.get("message"));
            
        // Add module-specific context
        if (metadata.containsKey("module")) {
            template = template.replace("{currentModule}", (String) metadata.get("module"));
            template = template.replace("{currentStep}", (String) metadata.get("currentStep"));
        }
        
        return template;
    }
}
```

## Performance Optimization

### Caching Strategy

**Embedding Cache**:
```java
@Service
@CacheConfig(cacheNames = "embeddings")
public class CachedEmbeddingService {
    
    @Cacheable(key = "#text.hashCode()")
    public float[] generateEmbedding(String text) {
        return embeddingService.generateEmbedding(text);
    }
    
    @CacheEvict(allEntries = true)
    public void clearEmbeddingCache() {
        log.info("Embedding cache cleared");
    }
}
```

**Vector Search Optimization**:
```sql
-- Optimize vector search with proper indexing
CREATE INDEX CONCURRENTLY idx_doc_chunks_project_embedding 
ON processed_document_chunks (csrd_project_id, embedding)
USING hnsw (embedding vector_cosine_ops);

-- Partition large tables by project for better performance
CREATE TABLE processed_document_chunks_partitioned (
    LIKE processed_document_chunks INCLUDING ALL
) PARTITION BY HASH (csrd_project_id);
```

### Monitoring and Metrics

**AI Service Metrics**:
```java
@Component
public class AIMetrics {
    private final Counter openaiRequests = Counter.build()
        .name("openai_requests_total")
        .help("Total OpenAI API requests")
        .register();
        
    private final Histogram openaiLatency = Histogram.build()
        .name("openai_request_duration_seconds")
        .help("OpenAI API request latency")
        .register();
        
    private final Gauge embeddingCacheHitRate = Gauge.build()
        .name("embedding_cache_hit_rate")
        .help("Embedding cache hit rate")
        .register();
}
```

## Security and Privacy

### Data Protection

**Document Security**:
```java
@PreAuthorize("hasPermission(#projectId, 'project', 'READ')")
public List<ProcessedDocumentChunk> getDocumentChunks(Long projectId) {
    return documentChunkRepository.findByProjectId(projectId);
}

@PreAuthorize("hasPermission(#request.projectId, 'project', 'WRITE')")
public AutofillResponse generateAutofill(AutofillRequest request) {
    return autofillService.generateSuggestion(request);
}
```

**PII Handling**:
```java
@Service
public class PIIDetectionService {
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}");
    private static final Pattern PHONE_PATTERN = Pattern.compile("\\b\\d{3}[-.]?\\d{3}[-.]?\\d{4}\\b");
    
    public String sanitizeForAI(String content) {
        // Remove or mask PII before sending to AI service
        content = EMAIL_PATTERN.matcher(content).replaceAll("[EMAIL_REDACTED]");
        content = PHONE_PATTERN.matcher(content).replaceAll("[PHONE_REDACTED]");
        return content;
    }
}
```

## Troubleshooting Guide

### Common Issues

**1. Embedding Generation Failures**
```bash
# Check OpenAI API key
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models

# Monitor embedding service logs
kubectl logs -f deployment/parabella-backend | grep "EmbeddingService"
```

**2. Vector Search Performance Issues**
```sql
-- Check index usage
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM processed_document_chunks 
ORDER BY embedding <=> '[0.1,0.2,...]' 
LIMIT 10;

-- Rebuild HNSW index if needed
REINDEX INDEX idx_doc_chunks_embedding;
```

**3. Document Processing Timeouts**
```java
// Increase processing timeout in application.properties
document.processing.timeout=600000
spring.task.execution.pool.max-size=10
```

This comprehensive AI integration documentation provides the foundation for understanding, maintaining, and extending the AI-powered features of the Parabella CSRD FAQ Tool.