# Parabella CSRD FAQ Tool - System Architecture

## Overview

The Parabella CSRD FAQ Tool is a comprehensive enterprise application for Corporate Sustainability Reporting Directive (CSRD) compliance. The system employs a modern microservice-inspired architecture with clear domain separation, dual-database design, and AI-powered analytics.

## High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        FE[React 18.3.1 SPA<br/>TypeScript + Vite]
        FE --> MOD1[DMA Module<br/>Mithril]
        FE --> MOD2[CSRD Module<br/>Elessar]
        FE --> MOD3[PCF Module<br/>Vilya]
    end
    
    subgraph "API Gateway Layer"
        API[Spring Boot 3.3.0<br/>REST API]
        API --> AUTH[Authentication<br/>JWT + 2FA]
        API --> SEC[Authorization<br/>Role-based Access]
    end
    
    subgraph "Business Logic Layer"
        SVC1[DMA Services<br/>Materiality Analysis]
        SVC2[CSRD Services<br/>Compliance Reporting]  
        SVC3[PCF Services<br/>Carbon Footprint]
        SVC4[AI Services<br/>OpenAI Integration]
        SVC5[Common Services<br/>User, Notification, etc.]
    end
    
    subgraph "Data Layer"
        DB1[(Main Database<br/>PostgreSQL)]
        DB2[(Vector Database<br/>PostgreSQL + pgvector)]
        CACHE[Redis Cache<br/>Session & API Cache]
    end
    
    subgraph "External Integrations"
        AI[OpenAI API<br/>GPT-4o-mini]
        EMAIL[Email Service<br/>SMTP Gmail]
        PYTHON[Python AI Service<br/>FastAPI]
    end
    
    FE --> API
    API --> SVC1
    API --> SVC2
    API --> SVC3
    API --> SVC4
    API --> SVC5
    
    SVC1 --> DB1
    SVC2 --> DB1
    SVC3 --> DB1
    SVC4 --> DB2
    SVC5 --> DB1
    
    SVC4 --> AI
    SVC5 --> EMAIL
    SVC4 --> PYTHON
    
    API --> CACHE
```

## Module Architecture and Domain Boundaries

### 1. DMA Module (Mithril) - Double Materiality Assessment

**Purpose**: Stakeholder-driven materiality analysis for CSRD compliance

```mermaid
graph LR
    subgraph "DMA Frontend"
        UI1[Company Information]
        UI2[Stakeholder Management]
        UI3[Value Chain Mapping]
        UI4[Topic Selection]
        UI5[IRO Assessment]
        UI6[Analysis Results]
        UI1 --> UI2 --> UI3 --> UI4 --> UI5 --> UI6
    end
    
    subgraph "DMA Backend Services"
        SVC1[CompanyService]
        SVC2[StakeholderService]
        SVC3[ValueChainService]
        SVC4[EsrsTopicService]
        SVC5[IroService]
        SVC6[DashboardService]
    end
    
    subgraph "DMA Data Model"
        ENT1[Company]
        ENT2[Project]
        ENT3[Stakeholder]
        ENT4[ValueChain]
        ENT5[EsrsTopic]
        ENT6[IroEvaluation]
    end
    
    UI1 --> SVC1 --> ENT1
    UI2 --> SVC2 --> ENT3
    UI3 --> SVC3 --> ENT4
    UI4 --> SVC4 --> ENT5
    UI5 --> SVC5 --> ENT6
    UI6 --> SVC6
```

**Key Components**:
- **Stakeholder Engagement**: Email-based stakeholder surveys and data collection
- **Topic Selection**: ESRS topic relevance assessment with business context
- **IRO Evaluation**: Impact, Risk, and Opportunity scoring matrix
- **Materiality Matrix**: Dynamic visualization of material topics

### 2. CSRD Module (Elessar) - CSRD Compliance Reporting

**Purpose**: Comprehensive CSRD compliance reporting with AI-powered assistance

```mermaid
graph LR
    subgraph "CSRD Frontend"
        UI1[Project Setup]
        UI2[Data Sources<br/>AI Upload]
        UI3[Content Management<br/>Dynamic Forms]
        UI4[Report Generation<br/>Export]
        UI1 --> UI2 --> UI3 --> UI4
    end
    
    subgraph "CSRD Backend Services"
        SVC1[CsrdProjectService]
        SVC2[DocumentProcessingService]
        SVC3[CsrdDataService]
        SVC4[CoverageService]
        SVC5[ReportGenerationService]
    end
    
    subgraph "CSRD Data Model"
        ENT1[CsrdProject]
        ENT2[CsrdTopic/Subtopic]
        ENT3[CsrdField/Response]
        ENT4[ProcessedDocumentChunk]
        ENT5[CsrdData]
    end
    
    subgraph "AI Processing"
        AI1[Document Chunking]
        AI2[Vector Embeddings]
        AI3[Semantic Search]
        AI4[Auto-completion]
    end
    
    UI1 --> SVC1 --> ENT1
    UI2 --> SVC2 --> AI1 --> AI2 --> ENT4
    UI3 --> SVC3 --> ENT3
    UI3 --> AI3 --> AI4
    UI4 --> SVC4 --> SVC5
```

**Key Components**:
- **AI Document Processing**: Upload and analyze sustainability documents
- **Dynamic Form Generation**: Context-aware CSRD datapoint collection
- **Coverage Analysis**: Gap analysis against CSRD requirements
- **Report Export**: Multi-format report generation (PDF, Excel, JSON)

### 3. PCF Module (Vilya) - Product Carbon Footprint

**Purpose**: Lifecycle carbon assessment and environmental impact analysis

```mermaid
graph LR
    subgraph "PCF Frontend"
        UI1[Product Setup]
        UI2[Raw Materials]
        UI3[Production]
        UI4[Distribution]
        UI5[Use Phase]
        UI6[End of Life]
        UI7[Analytics Dashboard]
        UI1 --> UI2 --> UI3 --> UI4 --> UI5 --> UI6 --> UI7
    end
    
    subgraph "PCF Backend Services"
        SVC1[ProductService]
        SVC2[EmissionCalculationService]
        SVC3[LifecycleService]
        SVC4[CarbonAnalyticsService]
    end
    
    subgraph "PCF Data Model"
        ENT1[Product]
        ENT2[EmissionFactor]
        ENT3[LifecyclePhase]
        ENT4[CarbonFootprint]
    end
    
    UI1 --> SVC1 --> ENT1
    UI2 --> SVC2 --> ENT2
    UI3 --> SVC3 --> ENT3
    UI7 --> SVC4 --> ENT4
```

**Key Components**:
- **Lifecycle Assessment**: Cradle-to-grave carbon footprint calculation
- **Emission Factors**: Comprehensive emission factor database
- **Phase Analysis**: Stage-specific carbon impact assessment
- **Carbon Dashboards**: Visual analytics and reporting

## Data Architecture

### Dual Database Strategy

```mermaid
graph TB
    subgraph "Main Database (PostgreSQL)"
        AUTH[Authentication<br/>Users, Roles, Permissions]
        BIZ[Business Data<br/>Companies, Projects, Stakeholders]
        CSRD[CSRD Data<br/>Topics, Fields, Responses]
        AUDIT[Audit Trail<br/>Hibernate Envers]
    end
    
    subgraph "Vector Database (PostgreSQL + pgvector)"
        DOCS[Document Chunks<br/>Text + Embeddings]
        ESRS[ESRS Datapoints<br/>Structured + Embeddings]
        SEARCH[Vector Search<br/>Similarity Queries]
    end
    
    subgraph "Application Services"
        APP[Spring Boot Application]
        AI[AI Services]
    end
    
    APP --> AUTH
    APP --> BIZ
    APP --> CSRD
    APP --> AUDIT
    AI --> DOCS
    AI --> ESRS
    AI --> SEARCH
```

### Database Configuration

**Main Database**: Core business data with traditional relational structure
- **Connection Pool**: HikariCP with Google Cloud SQL support
- **ORM**: Hibernate/JPA with automatic auditing
- **Migration**: Flyway for version-controlled schema evolution

**Vector Database**: AI-powered semantic search and document processing
- **Vector Extension**: pgvector for high-dimensional vector operations  
- **Embeddings**: OpenAI text-embedding-ada-002 (1536 dimensions)
- **Search**: Cosine similarity for semantic document retrieval

## Security Architecture

### Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Controller
    participant S as Auth Service
    participant DB as Database
    participant 2FA as TOTP Service
    
    C->>A: POST /api/auth/signin
    A->>S: validateCredentials()
    S->>DB: findUserByUsername()
    DB-->>S: User entity
    S->>S: BCrypt.checkPassword()
    S->>2FA: validate2FA() [if enabled]
    2FA-->>S: validation result
    S->>S: generateJWT()
    S-->>A: JwtResponse
    A-->>C: JWT Token + User Info
    
    Note over C,2FA: Subsequent requests include Bearer token
    C->>A: API Request + Authorization: Bearer <token>
    A->>A: JWT validation
    A->>A: Permission check
    A-->>C: API Response
```

### Authorization Model

**Role-Based Access Control (RBAC)**:
- **ADMIN**: Full system access, user management, system configuration
- **MANAGER**: Project management, team oversight, advanced analytics  
- **USER**: Standard functionality, project participation, data entry

**Permission Matrix** (examples):
- `USER_CREATE`: Create new users
- `PROJECT_MANAGE`: Create and manage projects
- `STAKEHOLDER_INVITE`: Invite external stakeholders
- `REPORT_EXPORT`: Export reports and data
- `SYSTEM_CONFIG`: Modify system settings

## Integration Architecture

### External Service Integration

```mermaid
graph LR
    subgraph "Parabella System"
        API[Spring Boot API]
        AI_SVC[AI Services]
        EMAIL_SVC[Email Service]
    end
    
    subgraph "External Services"
        OPENAI[OpenAI API<br/>GPT-4o-mini]
        PYTHON[Python AI Service<br/>FastAPI localhost:8000]
        GMAIL[Gmail SMTP<br/>Email Delivery]
    end
    
    subgraph "Configuration"
        CONFIG[Environment Variables<br/>API Keys & URLs]
    end
    
    AI_SVC --> OPENAI
    AI_SVC --> PYTHON
    EMAIL_SVC --> GMAIL
    CONFIG --> API
```

**Integration Patterns**:
- **OpenAI**: REST API with exponential backoff retry
- **Python AI Service**: Internal microservice for specialized AI processing
- **Email Service**: SMTP with HTML template support
- **Error Handling**: Circuit breaker pattern for external service failures

## Performance and Scalability

### Application Performance

```mermaid
graph TB
    subgraph "Performance Optimizations"
        CONN[Connection Pooling<br/>HikariCP]
        CACHE[Redis Caching<br/>Session & API Cache]
        LAZY[JPA Lazy Loading<br/>Optimized Queries]
        BATCH[Batch Processing<br/>Large Dataset Handling]
    end
    
    subgraph "Frontend Optimizations"
        CODE[Code Splitting<br/>Module-based Chunks]
        LAZY_LOAD[Lazy Loading<br/>Route-based]
        MEMO[React Memoization<br/>Component Optimization]
    end
    
    subgraph "Database Optimizations"
        INDEX[Strategic Indexing<br/>Query Optimization]
        PART[Table Partitioning<br/>Audit Data]
        VECTOR[Vector Indexing<br/>HNSW for Similarity]
    end
```

### Monitoring and Observability

**Application Metrics**:
- **JVM Metrics**: Memory usage, GC performance, thread pools
- **Database Metrics**: Connection pool status, query performance
- **API Metrics**: Response times, error rates, throughput
- **Custom Metrics**: Business KPIs, AI service usage

**Logging Strategy**:
- **Structured Logging**: JSON format with correlation IDs
- **Log Levels**: DEBUG (dev), INFO (staging), WARN/ERROR (production)
- **Audit Logging**: Comprehensive audit trail with Hibernate Envers

## Deployment Architecture

### Environment Configuration

```mermaid
graph TB
    subgraph "Development"
        DEV_APP[Local Spring Boot<br/>Port 8080]
        DEV_DB[Local PostgreSQL<br/>Docker Container]
        DEV_REDIS[Local Redis<br/>Session Storage]
    end
    
    subgraph "Staging"
        STAGE_APP[Cloud Application<br/>Auto-scaling]
        STAGE_DB[Cloud SQL<br/>PostgreSQL]
        STAGE_REDIS[Cloud Redis<br/>Managed Service]
    end
    
    subgraph "Production"
        PROD_APP[Load Balanced<br/>Multiple Instances]
        PROD_DB[High Availability<br/>Read Replicas]
        PROD_REDIS[Redis Cluster<br/>High Availability]
    end
```

**Infrastructure Requirements**:
- **Application Server**: Java 21 JVM, 4GB+ heap memory
- **Database**: PostgreSQL 13+, pgvector extension
- **Cache**: Redis 6+ for session management
- **Load Balancer**: HTTPS termination, health checks
- **Monitoring**: Application metrics, log aggregation

## Detailed Authentication Flow Architecture

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant LoadBalancer
    participant AuthService
    participant Database
    participant EmailService
    
    Note over User,EmailService: User Registration Flow
    User->>Frontend: Register (username, email, password)
    Frontend->>LoadBalancer: POST /api/auth/signup
    LoadBalancer->>AuthService: Forward request
    AuthService->>Database: Create user + TOTP secret
    AuthService->>Frontend: Return QR code for 2FA setup
    Frontend->>User: Display QR code
    User->>Frontend: Scan QR + enter TOTP code
    Frontend->>AuthService: POST /api/auth/verify2fa
    AuthService->>Database: Enable 2FA for user
    
    Note over User,EmailService: Login Flow
    User->>Frontend: Login (username, password, TOTP)
    Frontend->>AuthService: POST /api/auth/signin
    AuthService->>Database: Validate credentials + 2FA
    AuthService->>Database: Create refresh token
    AuthService->>Frontend: Return JWT + refresh token
    Frontend->>Frontend: Store tokens securely
    
    Note over User,EmailService: API Request Flow
    Frontend->>Frontend: Check token expiry
    alt Token expiring soon
        Frontend->>AuthService: POST /api/auth/refresh
        AuthService->>Database: Validate refresh token
        AuthService->>Database: Create new token pair
        AuthService->>Frontend: Return new tokens
    end
    Frontend->>LoadBalancer: API request with JWT
    LoadBalancer->>AuthService: Validate JWT
    AuthService->>LoadBalancer: Token valid + user context
    LoadBalancer->>Frontend: API response
```

## Data Flow Architecture

```mermaid
graph LR
    subgraph "User Context Flow"
        Login[User Login] --> AuthToken[JWT Token]
        AuthToken --> UserContext[User Context]
        UserContext --> Permissions[User Permissions]
        Permissions --> DataAccess[Data Access]
    end
    
    subgraph "Request Processing Flow"
        Request[API Request] --> Interceptor[Request Interceptor]
        Interceptor --> TokenCheck{Token Valid?}
        TokenCheck -->|No| RefreshFlow[Token Refresh]
        TokenCheck -->|Yes| AuthHeader[Add Auth Header]
        RefreshFlow --> NewToken[New Token Pair]
        NewToken --> AuthHeader
        AuthHeader --> Backend[Backend Processing]
        Backend --> PermissionCheck{Permission Check}
        PermissionCheck -->|Allowed| DataQuery[Database Query]
        PermissionCheck -->|Denied| AccessDenied[403 Forbidden]
        DataQuery --> Response[API Response]
    end
    
    subgraph "Session Management Flow"
        SessionStart[Session Start] --> ActivityMonitor[Activity Monitor]
        ActivityMonitor --> TimeoutCheck{Near Timeout?}
        TimeoutCheck -->|Yes| WarningModal[Timeout Warning]
        TimeoutCheck -->|No| ContinueSession[Continue Session]
        WarningModal --> UserChoice{User Action}
        UserChoice -->|Extend| RefreshSession[Refresh Session]
        UserChoice -->|Logout| ClearSession[Clear Session]
        RefreshSession --> ContinueSession
        ClearSession --> LoginPage[Login Page]
    end
```

## Security Architecture Layers

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Layer 1: Network Security"
            HTTPS[HTTPS/TLS 1.3]
            CORS[CORS Policy]
            CSP[Content Security Policy]
        end
        
        subgraph "Layer 2: Authentication"
            JWT[JWT Tokens]
            TOTP[2FA/TOTP]
            RefreshTokens[Refresh Tokens]
        end
        
        subgraph "Layer 3: Authorization"
            RBAC[Role-Based Access]
            Permissions[Fine-grained Permissions]
            ResourceOwnership[Resource Ownership]
        end
        
        subgraph "Layer 4: Session Management"
            SessionLimits[Session Limits]
            TokenRotation[Token Rotation]
            DeviceTracking[Device Tracking]
        end
        
        subgraph "Layer 5: Data Protection"
            InputValidation[Input Validation]
            SQLInjectionPrevention[SQL Injection Prevention]
            XSSPrevention[XSS Prevention]
        end
        
        subgraph "Layer 6: Monitoring & Audit"
            SecurityLogging[Security Logging]
            AnomalyDetection[Anomaly Detection]
            AuditTrail[Audit Trail]
        end
    end
    
    HTTPS --> JWT
    CORS --> TOTP
    CSP --> RefreshTokens
    JWT --> RBAC
    TOTP --> Permissions
    RefreshTokens --> ResourceOwnership
    RBAC --> SessionLimits
    Permissions --> TokenRotation
    ResourceOwnership --> DeviceTracking
    SessionLimits --> InputValidation
    TokenRotation --> SQLInjectionPrevention
    DeviceTracking --> XSSPrevention
    InputValidation --> SecurityLogging
    SQLInjectionPrevention --> AnomalyDetection
    XSSPrevention --> AuditTrail
```

## Database Architecture

```mermaid
erDiagram
    users {
        bigserial id PK
        varchar username UK
        varchar email UK
        varchar password_hash
        varchar totp_secret
        boolean totp_enabled
        boolean enabled
        timestamp created_at
        timestamp updated_at
    }
    
    roles {
        bigserial id PK
        varchar name UK
        varchar description
        timestamp created_at
    }
    
    permissions {
        bigserial id PK
        varchar function_key UK
        varchar description
        varchar category
    }
    
    role_permissions {
        bigint role_id PK,FK
        bigint permission_id PK,FK
    }
    
    refresh_tokens {
        bigserial id PK
        bigint user_id FK
        varchar token UK
        timestamp expiry_date
        timestamp created_at
        varchar device_info
        varchar ip_address
        boolean is_revoked
    }
    
    user_roles {
        bigserial id PK
        bigint user_id FK
        bigint role_id FK
        timestamp assigned_at
    }
    
    security_events {
        bigserial id PK
        varchar event_type
        varchar username
        varchar ip_address
        varchar user_agent
        varchar resource_accessed
        varchar outcome
        timestamp timestamp
        text additional_info
    }
    
    projects {
        bigserial id PK
        bigint owner_id FK
        varchar name
        text description
        varchar status
        timestamp created_at
        timestamp updated_at
        varchar created_by
        varchar last_modified_by
    }
    
    project_collaborators {
        bigserial id PK
        bigint project_id FK
        bigint user_id FK
        varchar permission_level
        timestamp added_at
    }
    
    users ||--o{ refresh_tokens : "has"
    users ||--o{ user_roles : "has"
    roles ||--o{ user_roles : "assigned to"
    roles ||--o{ role_permissions : "has"
    permissions ||--o{ role_permissions : "granted to"
    users ||--o{ projects : "owns"
    users ||--o{ project_collaborators : "collaborates on"
    projects ||--o{ project_collaborators : "has"
    users ||--o{ security_events : "generates"
```

## Token Lifecycle Management

```mermaid
stateDiagram-v2
    [*] --> UserLogin
    UserLogin --> TokenGeneration : Credentials + 2FA Valid
    TokenGeneration --> ActiveSession : Store Tokens
    
    ActiveSession --> TokenRefresh : Token Expiring
    ActiveSession --> APIRequest : Normal Operation
    ActiveSession --> SessionWarning : 2min Before Expiry
    
    TokenRefresh --> ActiveSession : New Token Pair
    TokenRefresh --> ForceLogout : Refresh Failed
    
    APIRequest --> ActiveSession : Success
    APIRequest --> TokenRefresh : 401 Unauthorized
    
    SessionWarning --> ExtendSession : User Extends
    SessionWarning --> ForceLogout : User Ignores/Logout
    SessionWarning --> AutoLogout : Timeout
    
    ExtendSession --> TokenRefresh : Manual Refresh
    
    ForceLogout --> TokenRevocation : Revoke All Tokens
    AutoLogout --> TokenRevocation : Revoke All Tokens
    
    TokenRevocation --> [*] : Clear Session
```

## Error Handling Architecture

```mermaid
graph TB
    subgraph "Frontend Error Handling"
        APIError[API Error] --> ErrorInterceptor[Response Interceptor]
        ErrorInterceptor --> ErrorType{Error Type}
        ErrorType -->|401| TokenRefresh[Automatic Token Refresh]
        ErrorType -->|403| PermissionError[Permission Denied]
        ErrorType -->|422| ValidationError[Validation Error]
        ErrorType -->|500| ServerError[Server Error]
        
        TokenRefresh --> RetryRequest[Retry Original Request]
        TokenRefresh --> LoginRequired[Force Re-login]
        
        PermissionError --> AccessDenied[Access Denied Page]
        ValidationError --> FormErrors[Show Form Errors]
        ServerError --> ErrorBoundary[Error Boundary]
    end
    
    subgraph "Backend Error Handling"
        Exception[Exception Thrown] --> ExceptionHandler[Global Exception Handler]
        ExceptionHandler --> ExceptionType{Exception Type}
        ExceptionType -->|AuthenticationException| Unauthorized[401 Response]
        ExceptionType -->|AccessDeniedException| Forbidden[403 Response]
        ExceptionType -->|ValidationException| BadRequest[422 Response]
        ExceptionType -->|RuntimeException| InternalError[500 Response]
        
        Unauthorized --> SecurityLog[Log Security Event]
        Forbidden --> SecurityLog
        BadRequest --> ValidationLog[Log Validation Error]
        InternalError --> ErrorLog[Log System Error]
    end
    
    SecurityLog --> AuditTrail[Audit Trail]
    ValidationLog --> Monitoring[Error Monitoring]
    ErrorLog --> AlertSystem[Alert System]
```

## Component Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Architecture"
        subgraph "Core Services"
            AuthContext[AuthContext Provider]
            TokenService[Token Service]
            ApiService[API Service]
        end
        
        subgraph "UI Components"
            LoginComp[Login Component]
            SessionWarning[Session Warning]
            RouteGuards[Route Guards]
            PermissionGuards[Permission Guards]
        end
        
        subgraph "Business Modules"
            CSRDModule[CSRD Module]
            DMAModule[DMA Module]
            PCFModule[PCF Module]
            UserMgmt[User Management]
        end
        
        subgraph "Infrastructure"
            Interceptors[HTTP Interceptors]
            ErrorHandling[Error Boundaries]
            StateManagement[Redux Store]
        end
    end
    
    subgraph "Backend Architecture"
        subgraph "Controllers"
            AuthController[Auth Controller]
            UserController[User Controller]
            ProjectController[Project Controller]
        end
        
        subgraph "Services"
            AuthService[Auth Service]
            RefreshTokenService[Refresh Token Service]
            UserService[User Service]
            ProjectService[Project Service]
        end
        
        subgraph "Security"
            JwtUtils[JWT Utilities]
            SecurityConfig[Security Config]
            PermissionEvaluator[Permission Evaluator]
        end
        
        subgraph "Data Access"
            UserRepo[User Repository]
            RefreshTokenRepo[RefreshToken Repository]
            PermissionRepo[Permission Repository]
        end
    end
    
    subgraph "Infrastructure"
        Database[(PostgreSQL Database)]
        Redis[(Redis Cache)]
        FileStorage[(File Storage)]
        EmailService[Email Service]
        OpenAI[OpenAI API]
    end
    
    %% Frontend connections
    LoginComp --> AuthContext
    SessionWarning --> TokenService
    RouteGuards --> AuthContext
    PermissionGuards --> AuthContext
    CSRDModule --> ApiService
    DMAModule --> ApiService
    PCFModule --> ApiService
    UserMgmt --> ApiService
    
    %% Backend connections
    AuthController --> AuthService
    AuthController --> RefreshTokenService
    UserController --> UserService
    AuthService --> JwtUtils
    AuthService --> UserRepo
    RefreshTokenService --> RefreshTokenRepo
    UserService --> PermissionEvaluator
    
    %% Infrastructure connections
    UserRepo --> Database
    RefreshTokenRepo --> Database
    PermissionRepo --> Database
    TokenService --> Redis
    AuthService --> EmailService
    ApiService --> OpenAI
```

## Technology Stack Summary

| Layer | Technology | Version | Purpose |
|-------|------------|---------|---------|
| Frontend | React | 18.3.1 | Single-page application |
| Frontend | TypeScript | 5.4.5 | Type-safe development |
| Frontend | Vite | 5.2.12 | Build tool and dev server |
| Backend | Spring Boot | 3.3.0 | Application framework |
| Backend | Java | 21 | Programming language |
| Database | PostgreSQL | 13+ | Primary data storage |
| Database | pgvector | Latest | Vector similarity search |
| Migration | Flyway | 10.4.1 | Database version control |
| Security | Spring Security | 6.x | Authentication & authorization |
| AI | OpenAI | GPT-4o-mini | Language model integration |
| Documentation | SpringDoc | 2.3.0 | OpenAPI/Swagger |
| Build | Gradle | 8.x | Build automation |
| Testing | JUnit | 5.x | Unit testing framework |

This architecture provides a scalable, maintainable foundation for CSRD compliance while supporting advanced AI-powered features and comprehensive sustainability reporting.