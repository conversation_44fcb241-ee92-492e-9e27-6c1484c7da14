# Documentation Index and Navigation

This comprehensive index provides quick access to all documentation across the Parabella CSRD FAQ Tool project.

📖 **Main Project Overview**: See the [root README.md](../README.md) for the primary project documentation and getting started guide.

## 📚 Quick Reference Guide

| Need to... | Go to... |
|------------|----------|
| **Understand the system** | [System Architecture](system-architecture.md) |
| **Set up development environment** | [Configuration Guide](configuration-guide.md) |
| **Use the API** | [API Reference](api-reference.md) + [Swagger UI](http://localhost:8080/swagger-ui.html) |
| **Understand the database** | [Database ERDs](database-erd.md) |
| **Work with frontend components** | [Frontend Components](frontend-components.md) |
| **Understand business workflows** | [Module Workflows](module-workflows.md) |
| **Deploy to production** | [Deployment Guide](deployment-guide.md) |
| **Implement security** | [Authentication and Security](authentication-security.md) |
| **Write and run tests** | [Testing Infrastructure](../TESTING-INFRASTRUCTURE.md) + [Testing Strategy](testing-strategy.md) |

---

## 🏗️ Foundation Documentation

### System Architecture
- **[System Architecture](system-architecture.md)** - Master architecture document
  - Module overview (DMA/Mithril, CSRD/Elessar, PCF/Vilya)
  - Detailed authentication flow architecture
  - Data flow diagrams and security architecture layers
  - Database architecture with ERD diagrams
  - Token lifecycle management
  - Error handling architecture
  - Integration architecture
  - Technology stack
  - Performance and scalability
  - Deployment architecture

### Database Documentation
- **[Database Structure](database-structure.md)** - Schema and migrations
  - Multi-database setup (Main + Vector)
  - Migration strategy with Flyway
  - Connection pooling and performance
  
- **[Database ERDs](database-erd.md)** - Entity relationship diagrams
  - Authentication & Security domain
  - DMA domain (Mithril) 
  - CSRD domain (Elessar)
  - Vector database (AI processing)
  - Cross-database relationships

### Legacy Architecture
- **[Architecture Overview](architecture-overview.md)** - Traditional layered view
  - Controller → Service → Repository pattern
  - Cross-cutting concerns
  - Updated technology stack

---

## 🔌 API Documentation

### Interactive Documentation
- **Swagger UI**: [http://localhost:8080/swagger-ui.html](http://localhost:8080/swagger-ui.html) (when running locally)
- **OpenAPI JSON**: `/v3/api-docs`

### API Reference
- **[API Reference](api-reference.md)** - Comprehensive endpoint documentation
  - Authentication endpoints with 2FA
  - Request/response examples
  - Error handling
  - Status codes

### Security
- **[Authentication and Security](authentication-security.md)** - Comprehensive security implementation
  - JWT authentication flow with refresh tokens
  - 2FA (TOTP) implementation
  - Role-based access control (RBAC)
  - Session management and timeout handling
  - Advanced security features and monitoring
  - Migration guide for authentication system
  - Comprehensive troubleshooting guide

---

## 🎨 Frontend Documentation

### Component Documentation
- **[Frontend Components](frontend-components.md)** - React component library
  - Component architecture
  - Props interfaces and usage
  - State management patterns
  - Development guidelines

### Business Workflows  
- **[Module Workflows](module-workflows.md)** - Complete business process documentation
  - **DMA Module (Mithril)**: Double Materiality Assessment workflow
  - **CSRD Module (Elessar)**: CSRD compliance reporting process
  - **PCF Module (Vilya)**: Product Carbon Footprint calculation
  - Cross-module integration

---

## ⚙️ Implementation Documentation

### Development Setup
- **[Configuration Guide](configuration-guide.md)** - Development environment setup
  - Local development configuration
  - Database setup
  - Environment variables
  - IDE configuration

### Code Architecture
- **[Service Layer](service-layer.md)** - Business logic patterns
  - Service layer architecture
  - Repository patterns
  - DTO mapping
  - Transaction management

### Testing
- **[Testing Strategy](testing-strategy.md)** - Testing approaches
  - Unit testing with JUnit
  - Integration testing with Testcontainers
  - Frontend testing strategies
  - Test data management

### Deployment
- **[Deployment Guide](deployment-guide.md)** - Production deployment
  - Environment configuration
  - Database migration procedures
  - Cloud deployment strategies
  - Monitoring and logging

---

## 📋 Documentation Cross-References

### By User Type

#### **Developers** 🧑‍💻
**Start Here**: [System Architecture](system-architecture.md) → [Configuration Guide](configuration-guide.md)

**Key Documents**:
1. [Database ERDs](database-erd.md) - Understanding data models
2. [Frontend Components](frontend-components.md) - Component development
3. [API Reference](api-reference.md) - API integration
4. [Service Layer](service-layer.md) - Backend patterns
5. [Testing Strategy](testing-strategy.md) - Testing approaches

#### **System Administrators** 🔧  
**Start Here**: [Architecture Overview](architecture-overview.md) → [Deployment Guide](deployment-guide.md)

**Key Documents**:
1. [Database Structure](database-structure.md) - Database management
2. [Authentication and Security](authentication-security.md) - Security configuration  
3. [Configuration Guide](configuration-guide.md) - Environment setup
4. [System Architecture](system-architecture.md) - System understanding

#### **Business Analysts** 📊
**Start Here**: [Module Workflows](module-workflows.md)

**Key Documents**:
1. [System Architecture](system-architecture.md) - System capabilities
2. [Frontend Components](frontend-components.md) - User interface understanding
3. [API Reference](api-reference.md) - Integration possibilities

#### **QA Engineers** 🧪
**Start Here**: [Testing Strategy](testing-strategy.md) → [Module Workflows](module-workflows.md)

**Key Documents**:
1. [API Reference](api-reference.md) - API testing
2. [Frontend Components](frontend-components.md) - UI testing
3. [Authentication and Security](authentication-security.md) - Security testing
4. [Database ERDs](database-erd.md) - Data validation

### By Module

#### **DMA Module (Mithril)** 🎯
- [Module Workflows](module-workflows.md#dma-module-mithril---double-materiality-assessment) - Complete workflow
- [Database ERDs](database-erd.md#dma-domain-mithril---double-materiality-assessment) - Data model
- [Frontend Components](frontend-components.md#dma-module-components-mithril) - UI components
- [API Reference](api-reference.md) - Related endpoints (search for "mithril")

#### **CSRD Module (Elessar)** 📊  
- [Module Workflows](module-workflows.md#csrd-module-elessar---compliance-reporting) - Complete workflow
- [Database ERDs](database-erd.md#csrd-domain-elessar---csrd-compliance-reporting) - Data model
- [Frontend Components](frontend-components.md#csrd-module-components-elessar) - UI components
- [API Reference](api-reference.md) - Related endpoints (search for "csrd")

#### **PCF Module (Vilya)** 🌱
- [Module Workflows](module-workflows.md#pcf-module-vilya---product-carbon-footprint) - Complete workflow
- [Database ERDs](database-erd.md) - Emission data models
- [Frontend Components](frontend-components.md#pcf-module-components-vilya) - UI components
- [API Reference](api-reference.md) - Related endpoints (search for "emission", "pcf")

### By Technology Stack

#### **Frontend (React + TypeScript)** ⚛️
- [Frontend Components](frontend-components.md) - Component library
- [System Architecture](system-architecture.md) - Frontend architecture
- [Module Workflows](module-workflows.md) - User workflows
- [Configuration Guide](configuration-guide.md) - Development setup

#### **Backend (Spring Boot + Java)** ☕
- [API Reference](api-reference.md) - REST endpoints
- [Service Layer](service-layer.md) - Business logic
- [Authentication and Security](authentication-security.md) - Security implementation
- [Database Structure](database-structure.md) - Data persistence

#### **Database (PostgreSQL)** 🐘
- [Database ERDs](database-erd.md) - Complete data model
- [Database Structure](database-structure.md) - Schema and migrations
- [System Architecture](system-architecture.md) - Database architecture
- [Deployment Guide](deployment-guide.md) - Database deployment

#### **AI Integration (OpenAI + Vector DB)** 🤖
- [AI Integration Guide](ai-integration.md) - Complete AI architecture and implementation
- [System Architecture](system-architecture.md#integration-architecture) - AI architecture overview
- [Database ERDs](database-erd.md#vector-database-domain---ai--document-processing) - Vector database design
- [Module Workflows](module-workflows.md#step-2-data-sources-and-ai-document-processing) - AI processing workflows
- [API Reference](api-reference.md#ai-services-endpoints) - AI-related endpoints

---

## 🔍 Search Guide

### Finding Information by Topic

**Authentication & Security**: 
- [Authentication and Security](authentication-security.md) - Complete security model with RBAC, 2FA, session management
- [System Architecture](system-architecture.md#detailed-authentication-flow-architecture) - Authentication flow diagrams
- [API Reference](api-reference.md#authentication-endpoints) - Security endpoints

**Database Design**:
- [Database ERDs](database-erd.md) (comprehensive)
- [Database Structure](database-structure.md) (implementation)

**API Development**:
- [API Reference](api-reference.md) (detailed endpoints)
- [Swagger UI](http://localhost:8080/swagger-ui.html) (interactive)

**Frontend Development**:
- [Frontend Components](frontend-components.md) (components)
- [Module Workflows](module-workflows.md) (user flows)

**Business Processes**:
- [Module Workflows](module-workflows.md) (detailed workflows)
- [System Architecture](system-architecture.md) (high-level overview)

**Deployment & Operations**:
- [Deployment Guide](deployment-guide.md) (production)
- [Configuration Guide](configuration-guide.md) (development)

---

## 📦 Consolidated Documentation

### Merged from docs/ Folder
The following documentation has been consolidated from the `docs/` folder into the main `documentation/` folder following best practices:

#### **Authentication and Security** 🔒
- **TOKEN_MANAGEMENT.md** → [Authentication and Security](authentication-security.md#jwt-token-structure)
- **AUTHENTICATION_MIGRATION_GUIDE.md** → [Authentication and Security](authentication-security.md#migration-guide)
- **SECURITY_MODEL_DOCUMENTATION.md** → [Authentication and Security](authentication-security.md#advanced-security-features)

#### **System Architecture** 🏗️
- **SYSTEM_ARCHITECTURE_DIAGRAM.md** → [System Architecture](system-architecture.md#detailed-authentication-flow-architecture)
  - All architectural diagrams including authentication flows, security layers, database ERDs, token lifecycle management, and error handling

#### **API Documentation** 📚
- Enhanced [API Reference](api-reference.md) with:
  - User Management endpoints
  - Role Management endpoints
  - Comprehensive authentication endpoints with examples

#### **Migration Benefits**
✅ **Centralized Documentation**: All documentation now in single location  
✅ **Best Practices**: Following documentation organization standards  
✅ **Cross-References**: Better linking between related content  
✅ **Comprehensive Coverage**: No information lost during consolidation  
✅ **Updated Content**: Enhanced with latest architectural improvements  

---

## 📈 Documentation Maintenance

### Last Updated
- **System Architecture**: Enhanced with detailed authentication flows, security layers, and deployment architecture
- **Authentication and Security**: Comprehensive security model documentation with advanced features and migration guide
- **Database ERDs**: Comprehensive ERDs created including security schema
- **API Reference**: Enhanced with request/response examples and security endpoints
- **Frontend Components**: Complete component documentation
- **Module Workflows**: Detailed business process documentation

### Documentation Standards
- **Mermaid Diagrams**: Used for all architectural diagrams
- **Code Examples**: TypeScript/Java examples with proper syntax highlighting  
- **Cross-references**: Internal links between related documentation
- **Version Control**: All documentation tracked in Git
- **Review Process**: Documentation updated with code changes

### Contributing to Documentation
1. **Format**: Use Markdown with Mermaid diagrams
2. **Examples**: Include practical code examples
3. **Cross-references**: Link to related documentation
4. **Updates**: Update documentation with code changes
5. **Review**: Documentation changes require review

This index provides comprehensive navigation across all documentation in the Parabella CSRD FAQ Tool project.