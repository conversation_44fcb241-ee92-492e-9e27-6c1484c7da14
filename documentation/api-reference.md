# API Reference

This document provides a comprehensive reference for the Parabella CSRD FAQ Tool API endpoints with detailed request/response examples.

## Base URL

The base URL for all API endpoints is:

- **Development**: `http://localhost:8080`
- **Staging**: `https://parabella-elessar-1091242934000.europe-west3.run.app`
- **Production**: `https://parabella.app`

## Interactive Documentation

For interactive API exploration, access the **Swagger UI** at:
- `{BASE_URL}/swagger-ui.html` (when running locally: http://localhost:8080/swagger-ui.html)

## Authentication

The API uses **JWT (JSON Web Token)** authentication with **refresh token rotation** and optional **2FA (TOTP)** support.

### Token Security Features

- **Access Tokens**: Short-lived (15 minutes default) JWT tokens for API access
- **Refresh Tokens**: Long-lived (7 days default) tokens for obtaining new access tokens
- **Token Rotation**: New refresh token issued with each refresh for enhanced security
- **Session Limits**: Maximum 5 active refresh tokens per user
- **Device Tracking**: Tokens associated with device information and IP addresses
- **Automatic Cleanup**: Expired tokens automatically cleaned up daily

### Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Server
    participant DB as Database
    
    C->>API: POST /api/auth/signin
    API->>DB: Validate credentials
    DB-->>API: User data + 2FA status
    alt 2FA Setup Required
        API-->>C: {"message": "2FA setup required", "qrCodeUrl": "..."}
        C->>API: POST /api/auth/verify2fa
        API-->>C: {"message": "2FA verified successfully"}
        Note over C: User must login again after 2FA setup
    else 2FA Enabled
        Note over C: Include totpCode in login request
        API->>API: Validate TOTP code
        API-->>C: {"accessToken": "...", "refreshToken": "..."}
    end
    
    Note over C,API: Include in all subsequent requests:
    Note over C,API: Authorization: Bearer {access_token}
    
    alt Access Token Expires
        C->>API: POST /api/auth/refresh
        API->>API: Validate & rotate refresh token
        API-->>C: {"accessToken": "...", "refreshToken": "..."}
    end
    
    C->>API: POST /api/auth/logout
    API->>DB: Revoke all user refresh tokens
    API-->>C: {"message": "Logged out successfully"}
```

## Authentication Endpoints

### User Registration

**Endpoint**: `POST /api/auth/signup`

Register a new user account with mandatory 2FA setup.

**Request Headers**:
```
Content-Type: application/json
```

**Request Body**:
```json
{
  "username": "john.doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Success Response** (200 OK):
```json
{
  "message": "User registered successfully! Set up your authenticator with this QR:",
  "qrCodeUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "username": "john.doe"
}
```

**Error Responses**:
```json
// 400 Bad Request - Username taken
{
  "message": "Error: Username is already taken!"
}

// 400 Bad Request - Email in use
{
  "message": "Error: Email is already in use!"
}
```

---

### 2FA Setup Verification

**Endpoint**: `POST /api/auth/verify2fa`

Complete 2FA setup by verifying TOTP code from authenticator app.

**Request Body**:
```json
{
  "username": "john.doe",
  "code": "123456"
}
```

**Success Response** (200 OK):
```json
{
  "message": "2FA verified successfully. You can now log in."
}
```

**Error Responses**:
```json
// 400 Bad Request - Missing parameters
{
  "message": "Username and TOTP code are required"
}

// 400 Bad Request - Invalid code format
{
  "message": "Invalid TOTP code format"
}

// 400 Bad Request - Already verified
{
  "message": "2FA is already verified and enabled for this account."
}

// 401 Unauthorized - Invalid TOTP code
{
  "message": "Invalid TOTP code"
}
```

---

### User Login

**Endpoint**: `POST /api/auth/signin`

Authenticate user credentials and receive JWT tokens.

**Request Body**:
```json
{
  "username": "john.doe",
  "password": "SecurePass123!",
  "totpCode": 123456
}
```

**Success Response** (200 OK):
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "550e8400-e29b-41d4-a716-************",
  "type": "Bearer",
  "id": 1,
  "username": "john.doe", 
  "email": "<EMAIL>",
  "roles": ["User"],
  "permissions": ["PROJECT_CREATE", "STAKEHOLDER_MANAGE"]
}
```

**2FA Setup Required Response** (401 Unauthorized):
```json
{
  "message": "2FA setup required. Please scan the QR code and verify.",
  "qrCodeUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "username": "john.doe"
}
```

**Error Responses**:
```json
// 401 Unauthorized - Invalid credentials
{
  "message": "Error: Invalid username or password"
}

// 401 Unauthorized - Invalid/missing 2FA code
{
  "message": "Error: Invalid or missing TOTP code."
}

// 500 Internal Server Error - System error
{
  "message": "Error: Login failed - {error_details}"
}
```

---

### Refresh Access Token

**Endpoint**: `POST /api/auth/refresh`

Exchange a valid refresh token for new access and refresh tokens.

**Request Body**:
```json
{
  "refreshToken": "550e8400-e29b-41d4-a716-************"
}
```

**Success Response** (200 OK):
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "660f9500-f39c-52e5-b827-557766551111",
  "tokenType": "Bearer"
}
```

**Error Responses**:
```json
// 403 Forbidden - Invalid refresh token
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 403,
  "error": "Forbidden",
  "message": "Failed for [550e8400-e29b-41d4-a716-************]: Refresh token is not in database!",
  "path": "/api/auth/refresh"
}

// 403 Forbidden - Expired refresh token
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 403,
  "error": "Forbidden", 
  "message": "Failed for [550e8400-e29b-41d4-a716-************]: Refresh token has expired. Please sign in again.",
  "path": "/api/auth/refresh"
}

// 403 Forbidden - Revoked refresh token
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 403,
  "error": "Forbidden",
  "message": "Failed for [550e8400-e29b-41d4-a716-************]: Refresh token has been revoked. Please sign in again.",
  "path": "/api/auth/refresh"
}
```

---

### User Logout

**Endpoint**: `POST /api/auth/logout`

Revoke all refresh tokens for the authenticated user.

**Request Headers**:
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
{
  "message": "Logged out successfully!"
}
```

**Error Response**:
```json
// 401 Unauthorized - Not authenticated
{
  "message": "Error: Not authenticated"
}
```

---

### Get Current User

**Endpoint**: `GET /api/auth/me`

Validate session and retrieve current user information.

**Request Headers**:
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
{
  "accessToken": null,
  "refreshToken": null,
  "type": "Bearer",
  "id": 1,
  "username": "john.doe",
  "email": "<EMAIL>",
  "roles": ["User"],
  "permissions": ["PROJECT_CREATE", "STAKEHOLDER_MANAGE"]
}
```

**Error Response**:
```json
// 401 Unauthorized - Invalid session
{
  "message": "Error: User is not authenticated or session is invalid."
}
```

---

### Password Reset

**Endpoint**: `POST /api/auth/forgot-password`

Initiate password reset process.

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Success Response** (200 OK):
```json
{
  "message": "If that email is associated with an account, a reset link was sent."
}
```

**Complete Password Reset**:

**Endpoint**: `POST /api/auth/reset-password`

**Request Body**:
```json
{
  "token": "password_reset_token_here",
  "password": "NewSecurePass123!"
}
```

**Success Response** (200 OK):
```json
true
```

---

### Set Password (After Invitation)

**Endpoint**: `POST /api/auth/set-password`

Set password for invited users using verification token.

**Request Body**:
```json
{
  "token": "verification_token_here",
  "password": "SecurePass123!"
}
```

**Success Response** (200 OK):
```json
"Password has been set successfully. You can now log in."
```

**Error Responses**:
```json
// 400 Bad Request - Invalid token
"Invalid token."

// 400 Bad Request - Expired token
"Token has expired."
```

---

### 2FA Management

**Enable 2FA**: `PATCH /api/auth/enable2fa`

**Request Headers**:
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
{
  "message": "2FA enabled. Scan this QR: data:image/png;base64,..."
}
```

**Disable 2FA**: `PATCH /api/auth/disable2fa`

**Request Headers**:
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
{
  "message": "2FA disabled."
}
```

---

### Debug Endpoints

**Get Active Token Count**: `GET /api/auth/debug/tokens`

**Request Headers**:
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
{
  "activeTokens": 3
}
```

---

## Token Management

### Token Lifecycle

1. **Login**: Generates access token (15min) + refresh token (7 days)
2. **API Calls**: Use access token in Authorization header
3. **Token Refresh**: Before access token expires, use refresh token to get new pair
4. **Token Rotation**: Each refresh invalidates old refresh token and issues new one
5. **Logout**: Revokes all refresh tokens for the user
6. **Cleanup**: Expired tokens automatically removed daily at 2 AM

### Security Considerations

- **Session Limits**: Maximum 5 concurrent sessions per user
- **Device Tracking**: Each refresh token associated with device info and IP
- **Token Rotation**: Prevents token reuse attacks
- **Automatic Cleanup**: Prevents token table bloat
- **Revocation**: Immediate logout across all devices when needed

### Frontend Integration

```javascript
// Login flow
const loginResponse = await fetch('/api/auth/signin', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username, password, totpCode })
});

if (loginResponse.ok) {
  const { accessToken, refreshToken } = await loginResponse.json();
  localStorage.setItem('accessToken', accessToken);
  localStorage.setItem('refreshToken', refreshToken);
}

// API calls with token
const apiCall = async (url, options = {}) => {
  let token = localStorage.getItem('accessToken');
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${token}`
    }
  });
  
  // If token expired, try to refresh
  if (response.status === 401) {
    const refreshToken = localStorage.getItem('refreshToken');
    if (refreshToken) {
      const refreshResponse = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken })
      });
      
      if (refreshResponse.ok) {
        const { accessToken, refreshToken: newRefreshToken } = await refreshResponse.json();
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', newRefreshToken);
        
        // Retry original request
        return fetch(url, {
          ...options,
          headers: {
            ...options.headers,
            'Authorization': `Bearer ${accessToken}`
          }
        });
      } else {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }
  }
  
  return response;
};

// Logout
const logout = async () => {
  await fetch('/api/auth/logout', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
    }
  });
  
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  window.location.href = '/login';
};
```

---

## User Management Endpoints

### Get All Users

```
GET /api/users
```

Get all users in the system.

**Authorization Required**: `user.view` permission

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
[
  {
    "id": 1,
    "username": "john.doe",
    "email": "<EMAIL>",
    "roleName": "Admin",
    "createdAt": "2024-01-15T10:30:00Z",
    "isActive": true
  }
]
```

### Invite User

```
POST /api/users/invite
```

Send an invitation to a new user.

**Authorization Required**: `user.invite` permission

**Request Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "roleId": 2
}
```

**Success Response** (201 Created):
```json
{
  "id": 5,
  "username": null,
  "email": "<EMAIL>", 
  "roleName": "User",
  "invitationSent": true,
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### Update User Role

```
PUT /api/users/{userId}/role
```

Update a user's role.

**Authorization Required**: `user.edit` permission

**Path Parameters:**
- `userId`: The ID of the user to update

**Request Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "roleId": 3
}
```

**Success Response** (200 OK):
```json
{
  "id": 5,
  "username": "john.doe",
  "email": "<EMAIL>",
  "roleName": "Moderator",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### Delete User

```
DELETE /api/users/{userId}
```

Remove a user from the system.

**Authorization Required**: `user.delete` permission

**Path Parameters:**
- `userId`: The ID of the user to delete

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Success Response** (204 No Content)

---

## Role Management Endpoints

### Get All Roles

```
GET /api/role-management/roles
```

Get all roles with their permissions.

**Authorization Required**: `role.view` permission

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
[
  {
    "id": 1,
    "name": "Admin",
    "permissions": [
      {
        "id": 1,
        "functionKey": "user.view",
        "functionName": "View Users",
        "category": "User Management"
      },
      {
        "id": 2,
        "functionKey": "user.edit", 
        "functionName": "Edit Users",
        "category": "User Management"
      }
    ],
    "createdAt": "2024-01-01T00:00:00Z"
  }
]
```

### Get All Permissions

```
GET /api/role-management/permissions
```

Get all available permissions in the system.

**Authorization Required**: `role.view` permission

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Success Response** (200 OK):
```json
[
  {
    "id": 1,
    "functionKey": "user.view",
    "functionName": "View Users", 
    "category": "User Management"
  },
  {
    "id": 2,
    "functionKey": "user.edit",
    "functionName": "Edit Users",
    "category": "User Management"
  },
  {
    "id": 3,
    "functionKey": "role.view",
    "functionName": "View Roles",
    "category": "Role Management"
  },
  {
    "id": 4,
    "functionKey": "dma.read",
    "functionName": "Read DMA",
    "category": "DMA Module"
  }
]
```

### Create Role

```
POST /api/role-management/roles
```

Create a new role with permissions.

**Authorization Required**: `role.create` permission

**Request Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Project Manager",
  "permissionIds": [1, 2, 5, 6]
}
```

**Success Response** (201 Created):
```json
{
  "id": 4,
  "name": "Project Manager",
  "permissions": [
    {
      "id": 1,
      "functionKey": "user.view",
      "functionName": "View Users",
      "category": "User Management"
    }
  ],
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### Update Role Permissions

```
PUT /api/role-management/roles/{roleId}
```

Update the permissions for a role.

**Authorization Required**: `role.edit` permission

**Path Parameters:**
- `roleId`: The ID of the role to update

**Request Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "permissionIds": [1, 2, 3, 7, 8]
}
```

**Success Response** (200 OK):
```json
{
  "id": 4,
  "name": "Project Manager", 
  "permissions": [
    {
      "id": 1,
      "functionKey": "user.view",
      "functionName": "View Users",
      "category": "User Management"
    }
  ],
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### Delete Role

```
DELETE /api/role-management/roles/{roleId}
```

Delete a role from the system.

**Authorization Required**: `role.delete` permission

**Path Parameters:**
- `roleId`: The ID of the role to delete

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Success Response** (204 No Content)

**Error Response** (400 Bad Request):
```json
{
  "error": "Cannot delete role - users are assigned to this role",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## AI Services Endpoints

### AI Auto-completion for CSRD Datapoints

**Endpoint**: `POST /api/ai/autofill`

Generate AI-powered suggestions for CSRD datapoint completion based on processed documents.

**Request Headers**:
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Request Body**:
```json
{
  "datapointId": "csrd_field_123",
  "query": "What is our company's approach to climate change mitigation?",
  "context": "E1 Climate Change - Transition Plan",
  "projectId": "project_456"
}
```

**Success Response** (200 OK):
```json
{
  "suggestion": "Our company has implemented a comprehensive climate transition plan focusing on renewable energy adoption, energy efficiency improvements, and carbon footprint reduction across all operations.",
  "confidence": 0.85,
  "sourceDocuments": [
    {
      "documentTitle": "Sustainability Report 2023",
      "chunkId": "chunk_789",
      "relevanceScore": 0.92
    }
  ],
  "reasoning": "Based on your sustainability report and climate policy documents, this response aligns with your documented transition strategies."
}
```

**Error Response**:
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Insufficient context for AI completion",
  "path": "/api/ai/autofill"
}
```

---

## Chat & Conversation Endpoints

### AI Chat Conversation

**Endpoint**: `POST /api/chat/conversation`

Start or continue an AI-powered conversation for CSRD guidance and support.

**Request Headers**:
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Request Body**:
```json
{
  "message": "How do I assess double materiality for climate risks?",
  "conversationId": "conv_123",
  "context": {
    "module": "DMA",
    "currentStep": "impact_assessment",
    "companyIndustry": "manufacturing"
  }
}
```

**Success Response** (200 OK):
```json
{
  "response": "To assess double materiality for climate risks, you need to evaluate both impact materiality (how your operations affect the environment) and financial materiality (how climate change affects your business). For manufacturing companies like yours, key considerations include...",
  "conversationId": "conv_123",
  "messageId": "msg_456",
  "suggestedActions": [
    "Review ESRS E1 Climate Change requirements",
    "Map your value chain for climate touchpoints",
    "Engage stakeholders on climate risk priorities"
  ]
}
```

### Get Conversation History

**Endpoint**: `GET /api/chat/conversation/{conversationId}`

Retrieve chat history for a specific conversation.

**Request Headers**:
```
Authorization: Bearer {jwt_token}
```

**Success Response** (200 OK):
```json
{
  "conversationId": "conv_123",
  "messages": [
    {
      "messageId": "msg_001",
      "sender": "user",
      "content": "How do I start with double materiality assessment?",
      "timestamp": "2024-01-15T10:30:00Z"
    },
    {
      "messageId": "msg_002", 
      "sender": "ai",
      "content": "Double materiality assessment begins with stakeholder identification...",
      "timestamp": "2024-01-15T10:30:15Z"
    }
  ],
  "totalMessages": 2,
  "createdAt": "2024-01-15T10:30:00Z"
}
```

---

## Document Processing Endpoints

### Process Document Chunks

**Endpoint**: `POST /api/documents/process`

Upload and process sustainability documents for AI-powered analysis.

**Request Headers**:
```
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data
```

**Request Body** (Form Data):
```
file: [PDF/Word/Excel file]
projectId: "project_123"
documentType: "sustainability_report"
processingOptions: {
  "chunkSize": 1000,
  "overlapSize": 100,
  "generateEmbeddings": true
}
```

**Success Response** (202 Accepted):
```json
{
  "processingId": "proc_789",
  "status": "processing",
  "message": "Document processing started",
  "estimatedCompletion": "2024-01-15T10:35:00Z"
}
```

### Get Processing Status

**Endpoint**: `GET /api/documents/process/{processingId}/status`

Check the status of document processing.

**Request Headers**:
```
Authorization: Bearer {jwt_token}
```

**Success Response** (200 OK):
```json
{
  "processingId": "proc_789",
  "status": "completed",
  "progress": 100,
  "chunks": {
    "total": 45,
    "processed": 45,
    "indexed": 45
  },
  "summary": {
    "documentTitle": "Annual Sustainability Report 2023",
    "totalChunks": 45,
    "averageChunkSize": 850,
    "processingTime": "2m 34s"
  }
}
```

---

## File Processing Endpoints  

### Excel Data Processing

**Endpoint**: `POST /api/excel/process`

Process Excel files for CSRD data import and analysis.

**Request Headers**:
```
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data
```

**Request Body** (Form Data):
```
file: [Excel file]
projectId: "project_123"
sheetName: "Environmental_Data"  
dataMapping: {
  "columns": {
    "A": "metric_name",
    "B": "value", 
    "C": "unit",
    "D": "year"
  }
}
```

**Success Response** (200 OK):
```json
{
  "importId": "import_456",
  "status": "completed",
  "recordsProcessed": 127,
  "recordsImported": 125,
  "errors": [
    {
      "row": 15,
      "error": "Invalid unit format",
      "value": "kg CO2-eq"
    }
  ],
  "summary": {
    "metrics": ["energy_consumption", "carbon_emissions", "water_usage"],
    "timespan": "2020-2023",
    "dataQuality": "high"
  }
}
```

## CSRD Project Endpoints

### Project Management

#### Get User Projects

```
GET /api/csrd-projects/user/{userId}
```

Get all CSRD projects for a user.

**Path Parameters:**
- `userId`: The ID of the user

**Response:**
```json
[
  {
    "id": "number",
    "userId": "number",
    "projectName": "string",
    "projectDescription": "string",
    "projectType": "string",
    "createdAt": "string",
    "updatedAt": "string"
  }
]
```

#### Get Project by ID

```
GET /api/csrd-projects/{projectId}
```

Get a CSRD project by ID.

**Path Parameters:**
- `projectId`: The ID of the project

**Response:**
```json
{
  "id": "number",
  "userId": "number",
  "projectName": "string",
  "projectDescription": "string",
  "projectType": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

#### Create Project

```
POST /api/csrd-projects/create
```

Create a new CSRD project.

**Request Body:**
```json
{
  "userId": "number",
  "projectName": "string",
  "projectDescription": "string",
  "projectType": "string"
}
```

**Response:**
```json
{
  "id": "number",
  "userId": "number",
  "projectName": "string",
  "projectDescription": "string",
  "projectType": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

### Company Information

#### Get Company Information

```
GET /api/csrd-projects/{projectId}/company-info
```

Get company information for a CSRD project.

**Path Parameters:**
- `projectId`: The ID of the project

**Response:**
```json
{
  "id": "number",
  "companyName": "string",
  "industry": "string",
  "size": "string",
  "revenue": "string",
  "numberOfEmployees": "string"
}
```

#### Save Company Information

```
POST /api/csrd-projects/{projectId}/company-info
```

Save company information for a CSRD project.

**Path Parameters:**
- `projectId`: The ID of the project

**Request Body:**
```json
{
  "companyName": "string",
  "industry": "string",
  "size": "string",
  "revenue": "string",
  "numberOfEmployees": "string"
}
```

**Response:**
```json
{
  "id": "number",
  "companyName": "string",
  "industry": "string",
  "size": "string",
  "revenue": "string",
  "numberOfEmployees": "string"
}
```

## CSRD Data Endpoints

### CSRD Topics

#### Get All CSRD Topics

```
GET /api/csrd/topics
```

Get all CSRD topics.

**Response:**
```json
[
  {
    "id": "number",
    "code": "string",
    "name": "string",
    "subtopics": [
      {
        "id": "number",
        "csrdSubtopicId": "string",
        "csrdSubtopicLabel": "string"
      }
    ]
  }
]
```

#### Get CSRD Data

```
GET /api/csrd/data
```

Get aggregated CSRD data.

**Response:**
```json
[
  {
    "id": "number",
    "code": "string",
    "name": "string",
    "subtopics": [
      {
        "id": "number",
        "csrdSubtopicId": "string",
        "csrdSubtopicLabel": "string",
        "sections": [
          {
            "id": "number",
            "sectionId": "string",
            "sectionTitle": "string",
            "fields": [
              {
                "id": "number",
                "fieldType": "string",
                "label": "string",
                "options": [
                  {
                    "id": "number",
                    "optionValue": "string",
                    "optionLabel": "string"
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]
```

### Emissions Data

#### Import Emissions Data

```
GET /api/emissions/import
```

Import emissions data from an Excel file.

**Response:**
```
Data imported successfully.
```

## Mithril Module Endpoints

### Double Materiality Analysis

#### Dashboard

##### Get Datapoint Status

```
GET /api/dashboard/datapoints-status
```

Get the status of datapoints for a company.

**Query Parameters:**
- `companyId`: The ID of the company

**Response:**
```json
{
  "collected": "number",
  "pending": "number",
  "overdue": "number",
  "collectedPercentage": "number"
}
```

##### Get Stakeholders Progress

```
GET /api/dashboard/stakeholders-progress
```

Get the progress of stakeholders for a company.

**Query Parameters:**
- `companyId`: The ID of the company

**Response:**
```json
[
  {
    "name": "string",
    "completedDatapoints": "number",
    "totalDatapoints": "number",
    "progress": "number"
  }
]
```

### ESRS Topics

#### Get All Subtopics

```
GET /api/esrs-topics/subtopics
```

Get all ESRS subtopics.

**Response:**
```json
[
  {
    "id": "number",
    "area": "string",
    "esrsCode": "string",
    "topic": "string",
    "subtopic": "string",
    "subSubTopic": "string"
  }
]
```

#### Get Subtopics by Area

```
GET /api/esrs-topics/subtopics/area/{area}
```

Get ESRS subtopics by area.

**Path Parameters:**
- `area`: The area (e.g., "Environmental")

**Response:**
```json
[
  {
    "id": "number",
    "area": "string",
    "esrsCode": "string",
    "topic": "string",
    "subtopic": "string",
    "subSubTopic": "string"
  }
]
```

#### Get Subtopics by ESRS Code

```
GET /api/esrs-topics/subtopics/esrs-code/{esrsCode}
```

Get ESRS subtopics by ESRS code.

**Path Parameters:**
- `esrsCode`: The ESRS code (e.g., "E1")

**Response:**
```json
[
  {
    "id": "number",
    "area": "string",
    "esrsCode": "string",
    "topic": "string",
    "subtopic": "string",
    "subSubTopic": "string"
  }
]
```

### ESRS Topic Selections

#### Create ESRS Topic Selection

```
POST /api/esrs-topic-selections
```

Create a new ESRS topic selection.

**Request Body:**
```json
{
  "esrsTopicId": "number",
  "companyId": "number",
  "stakeholderId": "number",
  "relevant": "boolean",
  "reasonIrrelevance": "string"
}
```

**Response:**
```json
{
  "id": "number",
  "esrsTopicId": "number",
  "companyId": "number",
  "stakeholderId": "number",
  "relevant": "boolean",
  "reasonIrrelevance": "string"
}
```

### IRO (Impact, Risk, Opportunity)

#### Get IRO Evaluation

```
GET /api/iroEval/{id}
```

Get an IRO evaluation by ID.

**Path Parameters:**
- `id`: The ID of the IRO evaluation

**Response:**
```json
{
  "id": "number",
  "iroId": "number",
  "actualPotentialImpact": "string",
  "affectedArea": "string",
  "description": "string",
  "effect": "string",
  "connection": "string",
  "scale": "number",
  "scope": "number",
  "irreversibility": "number",
  "likelihood": "number",
  "timeHorizon": "string",
  "magnitude": "number",
  "leverageToInfluence": "number",
  "financialMateriality": "boolean",
  "impactMateriality": "boolean",
  "doubleMateriality": "boolean"
}
```

#### Get IRO Evaluation by Topic Selection ID

```
GET /api/iroEval/topicId/{id}
```

Get an IRO evaluation by topic selection ID.

**Path Parameters:**
- `id`: The ID of the topic selection

**Response:**
```json
{
  "id": "number",
  "iroId": "number",
  "actualPotentialImpact": "string",
  "affectedArea": "string",
  "description": "string",
  "effect": "string",
  "connection": "string",
  "scale": "number",
  "scope": "number",
  "irreversibility": "number",
  "likelihood": "number",
  "timeHorizon": "string",
  "magnitude": "number",
  "leverageToInfluence": "number",
  "financialMateriality": "boolean",
  "impactMateriality": "boolean",
  "doubleMateriality": "boolean"
}
```

## Error Responses

The API returns standard HTTP status codes to indicate the success or failure of a request.

### Common Error Codes

- `400 Bad Request`: The request was malformed or invalid
- `401 Unauthorized`: Authentication is required or failed
- `403 Forbidden`: The authenticated user does not have permission to access the resource
- `404 Not Found`: The requested resource was not found
- `500 Internal Server Error`: An unexpected error occurred on the server

### Error Response Format

```json
{
  "timestamp": "string",
  "status": "number",
  "error": "string",
  "message": "string",
  "path": "string"
}
```